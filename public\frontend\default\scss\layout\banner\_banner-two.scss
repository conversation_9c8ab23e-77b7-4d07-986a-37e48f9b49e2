@use '../../utils' as *;

/*----------------------------------------*/
/* Banner styles
/*----------------------------------------*/
.td-banner-area {
    &.banner-style-two {
        padding-top: 160px;
        padding-bottom: 210px;

        @media #{$xxl} {
            padding-top: 130px;
            padding-bottom: 180px;
        }

        @media #{$xl} {
            padding-top: 130px;
            padding-bottom: 160px;
        }

        @media #{$lg} {
            padding-top: 120px;
            padding-bottom: 130px;
        }

        @media #{$md} {
            padding-top: 140px;
            padding-bottom: 80px;
        }

        @media #{$sm,$xs} {
            padding-top: 130px;
            padding-bottom: 60px;
        }

        .banner-bg-thumb {
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            z-index: -1;
            height: 100%;
            width: 100%;

            @include dark-theme {
                display: none;
            }

            img {
                width: 100%;
                height: 100%;
            }
        }

        .drowing-line {
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .shape-one {
            position: absolute;
            top: 50%;
            inset-inline-start: 110px;
            transform: translateY(-50%);
            z-index: -1;

            @media #{$xl,$lg} {
                inset-inline-start: 0;
            }
        }

        .shape-two {
            position: absolute;
            bottom: 84px;
            inset-inline-start: 77px;
            z-index: -1;

            img {
                width: 158px;

                @media #{$xl,$lg} {
                    width: 60%;
                }
            }
        }

        .shape-three {
            position: absolute;
            inset-inline-start: 35%;
            bottom: 50px;
            z-index: -1;

            img {
                width: 189px;

                @media #{$xl,$lg} {
                    width: 60%;
                }
            }
        }

        .shape-one-four {
            position: absolute;
            inset-inline-end: 80px;
            top: 22%;
            z-index: -1;

            img {
                @media #{$xl,$lg,$md} {
                    width: 60%;
                }
            }
        }

        .shape-torch-light {
            position: absolute;
            right: 0;
            top: 0;
            z-index: -1;
        }

        .banner-contents {

            .banner-title {
                font-size: 80px;
                line-height: 1.2;
                margin-bottom: rem(15);

                @media #{$x3l} {
                    font-size: 64px;
                }

                @media #{$xxl} {
                    font-size: 64px;
                }

                @media #{$xl} {
                    font-size: 54px;
                }

                @media #{$lg} {
                    font-size: 44px;
                }

                @media #{$md} {
                    font-size: 36px;
                }

                @media #{$sm} {
                    font-size: 34px;
                }

                @media #{$xs} {
                    font-size: 28px;
                }

                .highlight {
                    background: linear-gradient(90deg, #4776E6 25.04%, #8E54E9 60.72%);
                    background-clip: text;
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    display: inline-block;
                }
            }

            .description {
                max-width: rem(670);
                margin: 0 auto;
                margin-bottom: rem(35);
                font-size: rem(16);
            }

            .banner-btns {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                gap: 20px 20px;
            }
        }

        .banner-thumb {
            padding-inline-start: 20px;

            @media #{$xs,$sm,$md} {
                padding-inline-start: 0;
            }
        }
    }
}