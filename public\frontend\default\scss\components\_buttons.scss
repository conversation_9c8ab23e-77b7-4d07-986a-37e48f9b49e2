@use "../utils" as *;

/*----------------------------------------*/
/* Buttons styles
/*----------------------------------------*/
.btn-wrap {
    @include flexbox();
    align-items: center;
    gap: 15px 15px;
    flex-wrap: wrap;
}

.td-btn {
    padding: 0 26px;
    background: var(--td-primary);
    position: relative;
    z-index: 1;
    transition: all 0.4s ease-in-out;
    @include border-radius(8px);
    @include inline-flex();
    align-items: center;
    justify-content: center;
    height: 50px;
    color: var(--td-white);
    font-size: 16px;
    font-weight: 600;
    gap: 8px;

    // btn with icon
    .btn-icon {
        display: flex;
        align-items: center;

        i {
            transition: .3s;
        }
    }

    &:focus {
        color: var(--td-white);
    }

    &:hover {
        background-color: var(--td-primary);
        color: var(--td-heading);
        transform: translate3d(0, -2px, 0);
    }

    // outline btn
    &.btn-primary-outline {
        border: 2px solid rgba(166, 239, 103, 0.4);
        background-color: transparent;
        color: var(--td-white);

        &:hover {
            background-color: var(--td-heading);
            color: var(--td-white);
        }
    }

    &.outline-white-btn {
        background: var(--td-white);
        border: 1px solid #DBDBDB;
        color: var(--td-heading);
        gap: 8px;

        span {
            svg {
                width: 20px;
                height: 20px;
            }
        }

        &:hover {
            background-color: var(--td-heading);
            border-color: var(--td-heading);
            color: var(--td-white);
        }
    }

    &.outline-danger-btn {
        border: 1px solid var(--td-danger);
        background-color: transparent;
        color: var(--td-danger);
        font-weight: 500;
    }

    &.outline-black-btn {
        border: 1px solid rgba($heading, $alpha: 0.6);
        color: rgba($heading, $alpha: 0.6);
        background-color: transparent;
    }

    &.outline-success-btn {
        border: 1px solid rgba($success, $alpha: 0.6);
        color: rgba($success, $alpha: 0.6);
        background-color: transparent;
    }

    // Default btns
    &.btn-primary {
        background: var(--td-primary-alt);
        color: var(--td-white);

        span {
            svg * {
                width: 16px;
                height: 16px;
                stroke: var(--td-white);
            }
        }

        &:hover {
            background: var(--td-primary);
        }
    }

    &.danger-btn {
        background: var(--td-danger);
        color: var(--td-white);

        span {
            svg * {
                stroke: var(--td-white);
            }
        }
    }

    &.btn-white {
        background-color: var(--td-white);
        color: var(--td-heading);

        &:hover {
            background-color: var(--td-primary);
            color: var(--td-white);
        }
    }

    &.btn-gray {
        background: var(--td-alice-blue);
        border: 1px solid rgba(171, 178, 225, 0.3);
        @include border-radius(8px);
        color: var(--td-heading);

        .td-btn.btn-gray {
            background: rgba(255, 255, 255, 0.2);
            @include border-radius(8px);
            border-style: solid;
            border-color: rgba(255, 255, 255, 0.3);
            color: var(--td-white);
        }
    }

    &.success-btn {
        background: var(--td-success);
    }

    &.btn-dark {
        background: #010c1a;
        border: 1px solid rgba(255, 255, 255, 0.16);
        color: var(--td-white);
    }

    &.white-btn-12 {
        background: rgba(26, 26, 26, 0.12);
        color: #47494E;

        @include dark-theme {
            background: rgba(255, 255, 255, 0.12);
            color: #9A9DA7;
        }
    }

    // Gradients btns
    &.grd-fill-btn-primary {
        background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
        color: var(--td-white);
    }

    &.grd-fill-btn-secondary {
        background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
        color: var(--td-white);
    }

    &.grd-outline-fill-btn {
        &.primary-btn {
            background: transparent;
            color: var(--td-heading);

            &:hover {
                color: var(--td-heading);
            }
        }
    }

    &.grd-outline-fill-btn {
        position: relative;
        padding: 1px;
        background-color: transparent;
        background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
        color: var(--td-white);

        &.btn-secondary {

            &::before {
                background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) -3.77%, rgba(142, 84, 233, 0.3) 100%);
            }

            .inner-btn {
                background: #edf1fd;
                color: var(--td-heading);

                @include dark-theme {
                    background: #254083;
                }
            }
        }

        .inner-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            position: relative;
            z-index: 3;
            background: #fbfbfb;
            padding: 0 30px;
            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
            border-radius: 2px;
            width: 100%;
            font-size: 16px;
            font-weight: 600;
            gap: 8px;

            @include dark-theme {
                background: #282138;
            }

            &:before {
                position: absolute;
                top: 0;
                inset-inline-start: 0;
                content: "";
                background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) -3.77%, rgba(142, 84, 233, 0.1) 100%);
                z-index: -1;
                width: 100%;
                height: 100%;

                @include dark-theme {
                    background: linear-gradient(90deg, rgba(195, 64, 192, 0.1) 0%, rgba(251, 64, 90, 0.1) 50%, rgba(247, 163, 74, 0.1) 100%);
                }
            }
        }

        &::before {
            position: absolute;
            content: "";
            inset-inline-start: 0;
            top: 0;
            transition: all .3s;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
            clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
            border-radius: 2px;
        }

        &::after {
            background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
            opacity: 0;
            visibility: hidden;
        }

        &:hover {
            color: var(--td-white);

            &::after {
                opacity: 1;
                visibility: visible;
            }
        }
    }

    // Btn chip
    &.btn-chip {
        clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
        border-radius: 2px;
    }

    // Has underline
    &.has-underline {
        background-color: transparent;
        height: auto;
        padding: 0;

        .btn-text {
            color: var(--td-heading);

            &::after {
                position: absolute;
                content: "";
                inset-inline-start: auto;
                bottom: -2px;
                background: currentColor;
                width: 0;
                height: 2px;
                transition: 0.3s;
                inset-inline-end: 0;
            }
        }

        .btn-icon {
            width: 22px;
            height: 22px;
            background-color: var(--td-primary);
            @include inline-flex();
            align-items: center;
            justify-content: center;
            border-radius: 50%;

            svg * {
                stroke: var(--td-white);
            }
        }

        &:hover {
            .btn-text {
                &::after {
                    width: 100%;
                    inset-inline-start: 0;
                    inset-inline-end: auto;
                }
            }
        }
    }

    // Size Variation
    &.btn-xs {
        padding: 0 16px;
        height: 30px;
        font-size: 14px;
        gap: 4px;

        .btn-icon {
            i {
                width: 14px;
                height: 14px;
            }
        }
    }

    &.btn-h-36 {
        height: 36px;
        padding: 0 15px;
        font-weight: 500;
        font-size: 14px;
        gap: 4px;
    }

    &.btn-h-40 {
        height: 40px;
        padding: 0 24px;
        font-weight: 500;
        font-size: 14px;
        gap: 4px;
    }

    &.btn-sm {
        height: 44px;
        gap: 6px;
        font-size: 14px;

        svg {
            width: 16px;
            height: 16px;
        }
    }

    &.btn-md {
        font-size: var(--font-size-b3);
        height: 50px;
    }

    &.btn-lg {
        height: 70px;
        font-size: 18px;
    }

    &.btn-xl {
        font-size: 20px;
        height: 75px;
    }

    &.btn-xxl {
        font-size: 22px;
        height: 100px;
    }

    &.btn-m-w {
        min-width: rem(120);
    }
}

// underline btn
.td-underline-btn {
    font-weight: 500;
    position: relative;
    font-size: 16px;

    &::after {
        content: "";
        position: absolute;
        height: 1px;
        transition: .3s;
        inset-inline-start: auto;
        bottom: -1px;
        background: var(--td-primary);
        width: 0;
        inset-inline-end: 0;
    }

    &:hover {
        color: var(--td-primary);

        &::after {
            width: 100%;
            inset-inline-start: 0;
            inset-inline-end: auto;
        }
    }

    &.has-grad-one {
        background: var(--Gradiant3, linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%));
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;

        &:after {
            background: var(--Gradiant3, linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%));
        }
    }
}

.td-form-btns {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.notification-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;

    .icon {
        display: inline-flex;
        align-items: center;
    }

    .count {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 20px;
        width: 20px;
        font-size: 12px;
        gap: 2px;
        border-radius: 50%;
        background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
        color: var(--td-white);
    }
}