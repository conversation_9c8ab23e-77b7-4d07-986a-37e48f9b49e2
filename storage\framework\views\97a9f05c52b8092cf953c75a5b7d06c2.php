<?php if(session('notify')): ?>
    <?php
        $notification = session('notify');
        $notificationClass = match ($notification['type']) {
            'success' => 'has-success',
            'error' => 'has-danger',
            'warning' => 'has-warning',
            default => 'has-info',
        };

        $notificationSvg = match ($notification['type']) {
            'success' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="12" fill="#1FC54D"></circle>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M19.2635 6.55001C19.7257 7.01219 19.7257 7.76155 19.2635 8.22373L10.5845 16.9027C10.1223 17.3649 9.37297 17.3649 8.91078 16.9027L4.96578 12.9577C4.50359 12.4955 4.50359 11.7462 4.96578 11.284C5.42797 10.8218 6.17732 10.8218 6.6395 11.284L9.74764 14.3922L17.5898 6.55001C18.052 6.08782 18.8013 6.08782 19.2635 6.55001Z" fill="white"></path>
                    </svg>',
            'error' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="12" cy="12" r="12" fill="#F21B1B"></circle>
                        <path d="M6.75 17.25L12 12M12 12L17.25 6.75M12 12L6.75 6.75M12 12L17.25 17.25" stroke="white" stroke-width="2.625" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>',
            'warning' => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="12" fill="#FE8401"></circle>
                  <g clip-path="url(#clip0_769_87652)">
                     <path d="M18.1653 15.77L13.2953 6.80002C13.1664 6.56312 12.976 6.36537 12.7441 6.22759C12.5123 6.08981 12.2475 6.01709 11.9778 6.01709C11.7081 6.01709 11.4434 6.08981 11.2116 6.22759C10.9797 6.36537 10.7893 6.56312 10.6603 6.80002L5.78534 15.77C5.65774 15.999 5.5924 16.2575 5.59583 16.5197C5.59926 16.7818 5.67134 17.0385 5.80489 17.2641C5.93844 17.4897 6.12878 17.6764 6.35696 17.8055C6.58514 17.9346 6.84317 18.0017 7.10534 18H16.8453C17.1054 18.0003 17.361 17.9329 17.5871 17.8045C17.8133 17.6762 18.0021 17.4913 18.1353 17.2679C18.2684 17.0445 18.3411 16.7904 18.3464 16.5304C18.3517 16.2705 18.2893 16.0136 18.1653 15.785V15.77ZM11.2303 9.37002C11.2303 9.1711 11.3094 8.98034 11.45 8.83969C11.5907 8.69903 11.7814 8.62002 11.9803 8.62002C12.1793 8.62002 12.37 8.69903 12.5107 8.83969C12.6513 8.98034 12.7303 9.1711 12.7303 9.37002V12.815C12.7303 13.0139 12.6513 13.2047 12.5107 13.3453C12.37 13.486 12.1793 13.565 11.9803 13.565C11.7814 13.565 11.5907 13.486 11.45 13.3453C11.3094 13.2047 11.2303 13.0139 11.2303 12.815V9.37002ZM12.0003 16.125C11.8302 16.125 11.664 16.0746 11.5225 15.9801C11.3811 15.8856 11.2709 15.7513 11.2058 15.5941C11.1407 15.437 11.1237 15.2641 11.1569 15.0972C11.19 14.9304 11.272 14.7772 11.3922 14.6569C11.5125 14.5366 11.6657 14.4547 11.8326 14.4215C11.9994 14.3884 12.1723 14.4054 12.3294 14.4705C12.4866 14.5356 12.6209 14.6458 12.7154 14.7872C12.8099 14.9287 12.8603 15.0949 12.8603 15.265C12.8603 15.4931 12.7697 15.7118 12.6084 15.8731C12.4472 16.0344 12.2284 16.125 12.0003 16.125Z" fill="white"></path>
                  </g>
                  <defs>
                     <clipPath id="clip0_769_87652">
                        <rect width="18" height="18" fill="white" transform="translate(3 3)"></rect>
                     </clipPath>
                  </defs>
               </svg>',
            default => '<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
               <circle cx="12" cy="12" r="12" fill="#2E87E8"></circle>
               <g clip-path="url(#clip0_769_61177)">
                  <path d="M13.375 8C14.4796 8 15.375 7.10457 15.375 6C15.375 4.89543 14.4796 4 13.375 4C12.2704 4 11.375 4.89543 11.375 6C11.375 7.10457 12.2704 8 13.375 8Z" fill="white"></path>
                  <path d="M15.42 16.1149C15.3647 16.0777 15.3026 16.052 15.2373 16.0392C15.1719 16.0263 15.1047 16.0267 15.0395 16.0402C14.9743 16.0537 14.9124 16.0801 14.8575 16.1178C14.8027 16.1555 14.7559 16.2039 14.72 16.2599C14.2366 16.9757 13.6445 17.6117 12.965 18.1449C12.8 18.2699 12.185 18.7449 11.925 18.6449C11.745 18.5899 11.85 18.2349 11.885 18.0849L12.15 17.2999C12.26 16.9799 14.175 11.2999 14.385 10.6499C14.695 9.69991 14.56 8.76491 13.145 8.98991C12.76 9.02991 8.85496 9.53491 8.78496 9.53991C8.7193 9.54418 8.65512 9.56134 8.59609 9.59041C8.53706 9.61948 8.48434 9.65989 8.44093 9.70934C8.39752 9.75879 8.36427 9.8163 8.34308 9.8786C8.3219 9.94089 8.31319 10.0068 8.31746 10.0724C8.32173 10.1381 8.33889 10.2023 8.36796 10.2613C8.39703 10.3203 8.43744 10.373 8.48689 10.4164C8.53634 10.4599 8.59385 10.4931 8.65615 10.5143C8.71844 10.5355 8.7843 10.5442 8.84996 10.5399C8.84996 10.5399 10.35 10.3449 10.515 10.3299C10.5995 10.3217 10.6846 10.338 10.76 10.3771C10.8354 10.4161 10.8979 10.4761 10.94 10.5499C11.0306 10.83 11.0181 11.1332 10.905 11.4049C10.775 11.9049 8.71996 17.6949 8.65496 18.0299C8.58525 18.3102 8.6046 18.6053 8.71034 18.8742C8.81608 19.143 9.00294 19.3722 9.24496 19.5299C9.69922 19.8327 10.2401 19.9784 10.785 19.9449C11.3148 19.9388 11.8388 19.8337 12.33 19.6349C13.575 19.1349 14.875 17.8049 15.56 16.7599C15.6189 16.6537 15.6373 16.5297 15.6115 16.411C15.5857 16.2923 15.5177 16.1871 15.42 16.1149Z" fill="white"></path>
               </g>
               <defs>
                  <clipPath id="clip0_769_61177">
                     <rect width="18" height="18" fill="white" transform="translate(3 3)"></rect>
                  </clipPath>
               </defs>
            </svg>',
        };
        $timeId = time();
    ?>

    <div class="alert-show-status" id="<?php echo e($timeId); ?>">
        <div class="td-alert-box <?php echo e($notificationClass); ?>">
           <div class="alert-content">
              <span class="alert-icon">
                 <?php echo $notificationSvg; ?>

              </span>
              <div class="contents">
                 <p class="alert-message"><?php echo e($notification['message']); ?></p>
              </div>
           </div>
           <button class="close-btn">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                 <path d="M2.5 9.5L6 6M6 6L9.5 2.5M6 6L2.5 2.5M6 6L9.5 9.5" stroke="#FB405A" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
           </button>
        </div>
     </div>
     <?php $__env->startPush('script'); ?>
        <script>

         setTimeout(() => {
            $('#<?php echo e($timeId); ?>').fadeOut('slow', function () {
               $(this).remove();
            });
         }, 3500);

        </script>
     <?php $__env->stopPush(); ?>
<?php endif; ?>

<?php if($errors->any()): ?>
    <?php
        $timeId = time();
    ?>
    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <?php
        $timeId = rand(1000, 9999);
    ?>
    <div class="alert-show-status" id="<?php echo e($timeId); ?>">
        <div class="td-alert-box has-danger">
           <div class="alert-content">
              <span class="alert-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="12" fill="#F21B1B"></circle>
                    <path d="M6.75 17.25L12 12M12 12L17.25 6.75M12 12L6.75 6.75M12 12L17.25 17.25" stroke="white" stroke-width="2.625" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </span>
              <div class="contents">
                 <p class="alert-message"><?php echo e($error); ?></p>
              </div>
           </div>
           <button class="close-btn">
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                 <path d="M2.5 9.5L6 6M6 6L9.5 2.5M6 6L2.5 2.5M6 6L9.5 9.5" stroke="#FB405A" stroke-width="1.75" stroke-linecap="round" stroke-linejoin="round"></path>
              </svg>
           </button>
        </div>
     </div>
     <?php $__env->startPush('script'); ?>
        <script>

         setTimeout(() => {
            $('#<?php echo e($timeId); ?>').fadeOut('slow', function () {
               $(this).remove();
            });
         }, 3500);

        </script>
     <?php $__env->stopPush(); ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>
<?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/include/_notify.blade.php ENDPATH**/ ?>