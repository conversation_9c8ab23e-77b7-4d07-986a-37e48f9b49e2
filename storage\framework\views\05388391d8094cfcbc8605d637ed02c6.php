

<?php $__env->startSection('title'); ?>
   <?php echo e(__('Mining')); ?>

<?php $__env->stopSection(); ?>
<?php use \App\Enums\TxnType; ?>
<?php use \App\Models\Coin; ?>
<?php use \App\Enums\UserMiningStatus; ?>
<?php $__env->startSection('content'); ?>
   <div class="col-xxl-12">
      <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
          <h3 class="page-title"><?php echo e(__('Mining')); ?></h3>
        </div>
      </div>
      <?php echo $__env->make('frontend::user.mining.include.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
   </div>
   <div class="col-xxl-12">
        <form action="<?php echo e(route('user.mining.history')); ?>" method="get">
            <div class="filter-bar">
                <!-- Search Field -->
                <div class="filter-item">
                    <div class="clip-path-inner">
                        <div class="filter-bar-search">
                            <span class="search-icon">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_389_9256)">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.66668 1.83398C4.44502 1.83398 1.83334 4.44566 1.83334 7.66732C1.83334 10.889 4.44502 13.5007 7.66668 13.5007C10.8883 13.5007 13.5 10.889 13.5 7.66732C13.5 4.44566 10.8883 1.83398 7.66668 1.83398ZM0.833344 7.66732C0.833344 3.89337 3.89273 0.833984 7.66668 0.833984C11.4406 0.833984 14.5 3.89337 14.5 7.66732C14.5 9.37433 13.8741 10.9351 12.8393 12.1328L15.0202 14.3138C15.2155 14.509 15.2155 14.8256 15.0202 15.0209C14.825 15.2161 14.5084 15.2161 14.3131 15.0209L12.1321 12.8399C10.9345 13.8747 9.37368 14.5007 7.66668 14.5007C3.89273 14.5007 0.833344 11.4413 0.833344 7.66732Z" fill="#999999"></path>
                                    </g>
                                    <defs>
                                    <clipPath id="clip0_389_9256">
                                    <rect width="16" height="16" fill="white"></rect>
                                    </clipPath>
                                    </defs>
                                 </svg>
                            </span>
                            <input value="<?php echo e(request('txn')); ?>" class="input-box" name="txn" type="text" placeholder="Transaction ID">
                        </div>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="clip-path-inner">
                       <div class="td-form-group has-multiple">
                          <div class="input-field-inner">
                             <div class="input-field">
                                <select name="status" class="defaultselect2">
                                    <option value=""><?php echo e(__('All')); ?></option>
                                    <?php $__currentLoopData = UserMiningStatus::cases(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($status->value); ?>" <?php echo e(request('status') == $status->value ? 'selected' : ''); ?>>
                                           <?php echo e(str($status->value)->headline()); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                             </div>
                          </div>
                       </div>
                    </div>
                 </div>
                <div class="filter-item">
                    <div class="clip-path-inner">
                       <div class="td-form-group has-multiple">
                          <div class="input-field-inner">
                             <div class="input-field">
                                <select name="wallet" class="defaultselect2">
                                    <option value=""><?php echo e(__('All')); ?></option>
                                    <?php $__currentLoopData = $user->wallets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wallet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($wallet->coin_id); ?>" <?php echo e(request('wallet') == $wallet->coin_id ? 'selected' : ''); ?>>
                                           <?php echo e($wallet->coin->name); ?> (<?php echo e(str($wallet->coin->code)->trim()); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                             </div>
                          </div>
                       </div>
                    </div>
                 </div>
                <!-- Date Picker -->
                <div class="filter-item">
                    <div class="td-form-group has-right-icon">
                        <div class="input-field">
                            <input type="text" value="<?php echo e(request('date',today())); ?>" name="date" id="datePicker" class="flatpickr form-control" placeholder="Select date" readonly>
                        </div>
                    </div>
                </div>
        
                <!-- Filter Button -->
                <div class="filter-item">
                    <button type="submit" class="td-btn btn-sm btn-chip grd-fill-btn-primary">
                        <span class="btn-text"><?php echo e(__('Search')); ?></span>
                    </button>
                </div>
            </div>
        </form>
        <div class="recent-history-table">
            <div class="table-container table-responsive">
                <table class="td-table recent-table">
                    <thead>
                        <tr>
                            <th><?php echo e(__('Transaction ID')); ?></th>
                            <th><?php echo e(__('Amount')); ?></th>
                            <th><?php echo e(__('Plan')); ?></th>
                            <th><?php echo e(__('Status')); ?></th>
                            <th><?php echo e(__('Miner')); ?></th>
                            <th><?php echo e(__('Total Mined Amount')); ?></th>
                            <th><?php echo e(__('Total Mined Count')); ?></th>
                            <th><?php echo e(__('Mining Time')); ?></th>
                            <th>
                                <?php echo e(__('Details')); ?>

                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><span>#<?php echo e($transaction->tnx); ?></span></td>
                                <td>
                                    <?php echo e(formatAmount($transaction->amount, $transaction->wallet?->coin, true)); ?>

                                </td>
                                <td>
                                    <?php echo e($transaction->scheme?->name); ?>

                                </td>
                                <td>
                                    <?php
                                        $statusClass = match ($transaction->userMining?->status) {
                                            'Success' => 'badge-success',
                                            'Pending' => 'badge-warning',
                                            'Failed' => 'badge-danger',
                                            default => 'badge-default',
                                        };
                                    ?>
                                    <span class="td-badge <?php echo e($statusClass); ?>"><?php echo e(ucwords($transaction->userMining?->status)); ?></span>
                                </td>
                                <td>
                                    <?php echo e($transaction->scheme?->miner?->name); ?>

                                </td>

                                <td>
                                    <span class=""><?php echo e($transaction->userMining?->total_mined_amount.' '.$transaction->scheme?->miner?->coin?->code); ?></span>
                                </td>
                                <td>
                                    <?php echo e($transaction->userMining->mining_count ?? 0); ?> of <?php echo e($transaction->scheme?->return_period_type == 'period' ? $transaction->scheme?->return_period_max_number : 'Unlimited'); ?>

                                </td>
                                <td>
                                    <?php echo e(__('Last Mining: ') .($transaction->userMining?->last_mining_time ? now()->parse($transaction->userMining?->last_mining_time)->diffForHumans() : 'N/A')); ?>

                                    <br>
                                    <?php echo e(__('Next Mining: ') .($transaction->userMining?->next_mining_time ? now()->parse($transaction->userMining?->next_mining_time)->diffForHumans() : 'N/A')); ?>

                                </td>
                                <td>
                                    <a href="<?php echo e(route('user.mining.details', $transaction->id)); ?>" class="td-btn btn-xs grd-fill-btn-primary radius-30" data-bs-toggle="tooltip" title="Details">
                                        <span class="btn-icon">
                                            <iconify-icon icon="tabler:send"></iconify-icon>
                                        </span>
                                        <span class="btn-text">Details</span>
                                    </a>
                                </td>
            
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="<?php echo e(Route::is('user.mining.history') ? 11 : 10); ?>" class="text-center">
                                        <?php if (isset($component)) { $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.no-data-found','data' => ['class' => 'mt-10','module' => ''.e(Route::is('user.mining.history') ? __('Mining') : __('Transaction')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('no-data-found'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mt-10','module' => ''.e(Route::is('user.mining.history') ? __('Mining') : __('Transaction')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $attributes = $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $component = $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            <?php if(method_exists($transactions, 'links')): ?>
            <?php echo e($transactions->links()); ?>

            <?php endif; ?>
        </div>
   </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
<link rel="stylesheet" href="https://unpkg.com/flatpickr@4.6.13/dist/themes/dark.css">
<script src="<?php echo e(frontendAsset('js/flatpickr.js')); ?>"></script>
    <script>
         (function ($) {
         'use strict';
         flatpickr("#datePicker", {
            mode: "range",
            dateFormat: "Y-m-d",
            defaultDate: "<?php echo e(request('date',today())); ?>"
         });
      })(jQuery);
    </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/mining/history.blade.php ENDPATH**/ ?>