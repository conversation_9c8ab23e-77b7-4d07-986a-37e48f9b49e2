<?php use \App\Models\Language; ?>
<?php
   $user = Auth::user();
   $notifications = App\Models\Notification::query()
      ->where('for', 'user')
      ->where('user_id', $user->id)
      ->latest()
      ->get();
   $totalUnreadCount = App\Models\Notification::query()
      ->where('for', 'user')
      ->where('user_id', $user->id)
      ->where('read', 0)
      ->count();
?>

<div class="app-page-header">
   <div class="app-dashboard-header">
      <div class="left-contents">
         <div class="user-welcome-info">
            <button class="toggle-sidebar d-xl-none">
               <span class="bar-icon">
                  <svg width="7" height="10" viewBox="0 0 7 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                     <path d="M5.25977 8.53022L1.73977 5.00022L5.25977 1.47021" fill="white" />
                     <path d="M5.25977 8.53022L1.73977 5.00021L5.25977 1.47021" stroke="#171717" stroke-width="1.8"
                        stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
               </span>
            </button>
            <h3 class="welcome-text"><?php echo e(__('Welcome in')); ?>, <?php echo e(setting('site_title', 'global')); ?></h3>
         </div>
      </div>
      <div class="right-contents">
         <div class="header-right">
            <div class="header-quick-actions d-flex align-items-center">
               <?php if (isset($component)) { $__componentOriginalb388998fa29a74bb9d572a8c2cd9164d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb388998fa29a74bb9d572a8c2cd9164d = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.theme-lang-switcher','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('theme-lang-switcher'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb388998fa29a74bb9d572a8c2cd9164d)): ?>
<?php $attributes = $__attributesOriginalb388998fa29a74bb9d572a8c2cd9164d; ?>
<?php unset($__attributesOriginalb388998fa29a74bb9d572a8c2cd9164d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb388998fa29a74bb9d572a8c2cd9164d)): ?>
<?php $component = $__componentOriginalb388998fa29a74bb9d572a8c2cd9164d; ?>
<?php unset($__componentOriginalb388998fa29a74bb9d572a8c2cd9164d); ?>
<?php endif; ?>
               <div class="others-actions">
                  <!-- Notification dropdown -->
                  <div class="notification-panel-box">
                     <div class="quick-action-item">
                        <button type="button" class="action-icon notification-btn <?php echo e($totalUnreadCount > 0 ? 'active' : ''); ?>">
                           <iconify-icon icon="tabler:bell">
                        </button>
                     </div>

                     <!-- Notification content here -->
                     <!-- Notification content here -->
                     <div class="notification-panel">
                        <div class="notification-header">
                           <div class="heading-top">
                              <h3 class="title"><?php echo e(__('Notifications')); ?></h3>
                              <a href="<?php echo e(route('user.notification.all')); ?>"
                                 class="td-btn btn-xs btn-chip grd-fill-btn-primary">
                                 <span class="btn-text"><?php echo e(__('All')); ?></span>
                              </a>
                           </div>
                           <div class="notifications-middle">
                              <div class="middle-buttons">
                                 <button type="button" class="notification-btn">
                                    <a href="<?php echo e(route('user.notification.all')); ?>" class="text"><?php echo e(__('View All')); ?></a>
                                    <span class="count"><?php echo e($totalUnreadCount); ?></span>
                                 </button>
                              </div>
                              <?php if($totalUnreadCount > 0): ?>
                              <button type="button" class="notification-btn">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:checks"></iconify-icon>
                                 </span>
                                 <a href="<?php echo e(route('user.read-notification', 0)); ?>"
                                    class="text"><?php echo e(__('Mark all as read')); ?> </a>
                              </button>
                              <?php endif; ?>
                           </div>
                        </div>
                        <div class="notifications-inner">
                           <div class="notifications-lists">
                              <?php $__empty_1 = true; $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                 <a href="<?php echo e(route('user.read-notification', $notification->id)); ?>">
                                    <div class="notification-list">
                                       <div class="notification-item">
                                          <div class="icon">
                                          <span class="icon"><iconify-icon icon="tabler:bell"></iconify-icon></span>
                                          </div>
                                          <div class="contents">
                                          <h5 class="title"><?php echo e($notification->title); ?></h5>
                                          <p class="message"><?php echo e($notification->message); ?></p>
                                          <small class="time"><?php echo e($notification->created_at->diffForHumans()); ?></small>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                 <?php if (isset($component)) { $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.no-data-found','data' => ['class' => 'mt-10','module' => ''.e(__('New Notification')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('no-data-found'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mt-10','module' => ''.e(__('New Notification')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $attributes = $__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__attributesOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09)): ?>
<?php $component = $__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09; ?>
<?php unset($__componentOriginale68a44eee86a1e00bdfa66b1bce4fc09); ?>
<?php endif; ?>
                              <?php endif; ?>
                           </div>
                        </div>
                     </div>
                  </div>

                  <!-- User Profile -->
                  <!-- User Profile -->
                  <div class="user-profile-drop">
                     <div class="quick-action-item">
                        <button type="button" class="action-icon">
                           <iconify-icon icon="tabler:user-circle"></iconify-icon>
                        </button>
                     </div>
                     <div class="dropdown-menu">
                        <div class="profile-info-wrapper">
                           <div class="profile-info-list">
                              <a class="profile-info-item" href="<?php echo e(route('user.setting.index')); ?>">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:user"></iconify-icon>
                                 </span>
                                 <span class="text"><?php echo e(__('Profile Setting')); ?></span>
                              </a>
                              <a class="profile-info-item" href="<?php echo e(route('user.setting.index', ['type' => 'password'])); ?>">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                 </span>
                                 <span class="text"><?php echo e(__('Change Password')); ?></span>
                              </a>
                              <?php if(setting('fa_verification', 'permission')): ?>
                              <a class="profile-info-item" href="<?php echo e(route('user.setting.index',['type' => 'security'])); ?>">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:shield-lock"></iconify-icon>
                                 </span>
                                 <span class="text"><?php echo e(__('Security Setting')); ?></span>
                              </a>
                              <?php endif; ?>
                              <a class="profile-info-item" href="<?php echo e(route('user.tickets')); ?>">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:headset"></iconify-icon>
                                 </span>
                                 <span class="text"><?php echo e(__('Support Tickets')); ?></span>
                              </a>
                              <a class="profile-info-item profile-log-out" href="<?php echo e(route('logout')); ?>">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                 </span>
                                 <span class="text"><?php echo e(__('Log Out')); ?></span>
                              </a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/layouts/user_header.blade.php ENDPATH**/ ?>