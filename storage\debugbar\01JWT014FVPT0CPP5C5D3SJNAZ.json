{"__meta": {"id": "01JWT014FVPT0CPP5C5D3SJNAZ", "datetime": "2025-06-03 10:15:42", "utime": **********.075857, "method": "POST", "uri": "/user/plan-purchase/pay", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.732741, "end": **********.075872, "duration": 0.34313082695007324, "duration_str": "343ms", "measures": [{"label": "Booting", "start": **********.732741, "relative_start": 0, "end": **********.95278, "relative_end": **********.95278, "duration": 0.*****************, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.952789, "relative_start": 0.****************, "end": **********.075874, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "123ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.964973, "relative_start": 0.*****************, "end": **********.966708, "relative_end": **********.966708, "duration": 0.0017349720001220703, "duration_str": "1.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.074055, "relative_start": 0.*****************, "end": **********.074337, "relative_end": **********.074337, "duration": 0.00028204917907714844, "duration_str": "282μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 22, "nb_statements": 18, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01041, "accumulated_duration_str": "10.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'oGjwXvI2S05jRt9urdB3N1br7wpMijx1NNUSl9lh' limit 1", "type": "query", "params": [], "bindings": ["oGjwXvI2S05jRt9urdB3N1br7wpMijx1NNUSl9lh"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.971101, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 6.628}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 179}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.978913, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php:58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "orexcoin", "explain": null, "start_percent": 6.628, "width_percent": 7.973}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 63}], "start": **********.981334, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 14.601, "width_percent": 4.035}, {"sql": "select count(*) as aggregate from `schemes` where `id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 984}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 955}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 685}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 480}], "start": **********.00604, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:53", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 53}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php:53", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "53"}, "connection": "orexcoin", "explain": null, "start_percent": 18.636, "width_percent": 13.449}, {"sql": "select * from `schemes` where `schemes`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 92}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 228}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.013495, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:92", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 92}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:92", "ajax": false, "filename": "PlanPurchaseController.php", "line": "92"}, "connection": "orexcoin", "explain": null, "start_percent": 32.085, "width_percent": 6.244}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 95}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 228}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.0147629, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:95", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 95}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:95", "ajax": false, "filename": "PlanPurchaseController.php", "line": "95"}, "connection": "orexcoin", "explain": null, "start_percent": 38.329, "width_percent": 3.939}, {"sql": "select * from `portfolios` where `portfolios`.`id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 228}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.016619, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:96", "source": {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:96", "ajax": false, "filename": "PlanPurchaseController.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 42.267, "width_percent": 3.362}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` in (23)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 228}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.018528, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:96", "source": {"index": 25, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:96", "ajax": false, "filename": "PlanPurchaseController.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 45.629, "width_percent": 3.17}, {"sql": "select * from `user_wallets` where `user_wallets`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 242}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.019525, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:242", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 242}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:242", "ajax": false, "filename": "PlanPurchaseController.php", "line": "242"}, "connection": "orexcoin", "explain": null, "start_percent": 48.799, "width_percent": 2.978}, {"sql": "select * from `coins` where `coins`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 244}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.035084, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:244", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 244}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:244", "ajax": false, "filename": "PlanPurchaseController.php", "line": "244"}, "connection": "orexcoin", "explain": null, "start_percent": 51.777, "width_percent": 7.109}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 257}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.037129, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:257", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 257}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:257", "ajax": false, "filename": "PlanPurchaseController.php", "line": "257"}, "connection": "orexcoin", "explain": null, "start_percent": 58.886, "width_percent": 0}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `scheme_id`, `pay_currency`, `pay_amount`, `tnx`, `updated_at`, `created_at`) values (1, 'Payment for Plan Purchase: Bitcoin Basic Plan', 'Plan_Purchase', 0.0009177, '1', 0, 0.0009177, 'Bitcoin Wallet Balance', 'Success', 2, 'BTC', 0.0009177, 'TRX844O4PHF43', '2025-06-03 10:15:42', '2025-06-03 10:15:42')", "type": "query", "params": [], "bindings": [1, "Payment for Plan Purchase: Bitcoin Basic Plan", "Plan_Purchase", 0.0009177, "1", 0, 0.0009177, "Bitcoin Wallet Balance", "Success", 2, "BTC", 0.0009177, "TRX844O4PHF43", "2025-06-03 10:15:42", "2025-06-03 10:15:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 259}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.040996, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:259", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 259}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:259", "ajax": false, "filename": "PlanPurchaseController.php", "line": "259"}, "connection": "orexcoin", "explain": null, "start_percent": 58.886, "width_percent": 10.279}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 24}, {"index": 10, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.043445, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:24", "source": {"index": 9, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 24}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:24", "ajax": false, "filename": "TransactionServices.php", "line": "24"}, "connection": "orexcoin", "explain": null, "start_percent": 69.164, "width_percent": 0}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 30}, {"index": 22, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.043662, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:30", "source": {"index": 21, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 30}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:30", "ajax": false, "filename": "TransactionServices.php", "line": "30"}, "connection": "orexcoin", "explain": null, "start_percent": 69.164, "width_percent": 4.419}, {"sql": "update `user_wallets` set `balance` = `balance` - 0.0009177, `user_wallets`.`updated_at` = '2025-06-03 10:15:42' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `id` = '1'", "type": "query", "params": [], "bindings": ["2025-06-03 10:15:42", 1, "1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 30}, {"index": 18, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.045374, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:30", "source": {"index": 17, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 30}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:30", "ajax": false, "filename": "TransactionServices.php", "line": "30"}, "connection": "orexcoin", "explain": null, "start_percent": 73.583, "width_percent": 5.283}, {"sql": "select * from `schemes` where `schemes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 39}, {"index": 22, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.0468779, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:39", "source": {"index": 21, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 39}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:39", "ajax": false, "filename": "TransactionServices.php", "line": "39"}, "connection": "orexcoin", "explain": null, "start_percent": 78.866, "width_percent": 3.65}, {"sql": "insert into `user_minings` (`scheme_id`, `transaction_id`, `plan_price`, `total_mined_amount`, `next_mining_time`, `status`, `mining_count`, `user_id`, `updated_at`, `created_at`) values (2, 266, 0.0009177, 0, '2025-06-04 10:15:42', 'ongoing', 0, 1, '2025-06-03 10:15:42', '2025-06-03 10:15:42')", "type": "query", "params": [], "bindings": [2, 266, 0.0009177, 0, "2025-06-04 10:15:42", "ongoing", 0, 1, "2025-06-03 10:15:42", "2025-06-03 10:15:42"], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 34}, {"index": 19, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.048091, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:34", "source": {"index": 18, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 34}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:34", "ajax": false, "filename": "TransactionServices.php", "line": "34"}, "connection": "orexcoin", "explain": null, "start_percent": 82.517, "width_percent": 6.916}, {"sql": "update `transactions` set `user_mining_id` = 35, `transactions`.`updated_at` = '2025-06-03 10:15:42' where `id` = 266", "type": "query", "params": [], "bindings": [35, "2025-06-03 10:15:42", 266], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 43}, {"index": 16, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.049646, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:43", "source": {"index": 15, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 43}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:43", "ajax": false, "filename": "TransactionServices.php", "line": "43"}, "connection": "orexcoin", "explain": null, "start_percent": 89.433, "width_percent": 4.035}, {"sql": "select * from `miners` where `miners`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 44}, {"index": 22, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.051204, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:44", "source": {"index": 21, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 44}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:44", "ajax": false, "filename": "TransactionServices.php", "line": "44"}, "connection": "orexcoin", "explain": null, "start_percent": 93.468, "width_percent": 3.842}, {"sql": "select exists(select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6) as `exists`", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 44}, {"index": 16, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.0520961, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:44", "source": {"index": 15, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 44}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:44", "ajax": false, "filename": "TransactionServices.php", "line": "44"}, "connection": "orexcoin", "explain": null, "start_percent": 97.31, "width_percent": 2.69}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 66}, {"index": 10, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 18}, {"index": 11, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 274}, {"index": 12, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}], "start": **********.064612, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "TransactionServices.php:66", "source": {"index": 9, "namespace": null, "name": "app/Services/TransactionServices.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Services\\TransactionServices.php", "line": 66}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FServices%2FTransactionServices.php:66", "ajax": false, "filename": "TransactionServices.php", "line": "66"}, "connection": "orexcoin", "explain": null, "start_percent": 100, "width_percent": 0}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 276}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 145}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.069619, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "PlanPurchaseController.php:276", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Frontend/PlanPurchaseController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\Frontend\\PlanPurchaseController.php", "line": 276}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:276", "ajax": false, "filename": "PlanPurchaseController.php", "line": "276"}, "connection": "orexcoin", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Scheme": {"value": 2, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FScheme.php:1", "ajax": false, "filename": "Scheme.php", "line": "?"}}, "App\\Models\\Portfolio": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FPortfolio.php:1", "ajax": false, "filename": "Portfolio.php", "line": "?"}}, "App\\Models\\PortfolioFeature": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FPortfolioFeature.php:1", "ajax": false, "filename": "PortfolioFeature.php", "line": "?"}}, "App\\Models\\UserWallet": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\Coin": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FCoin.php:1", "ajax": false, "filename": "Coin.php", "line": "?"}}, "App\\Models\\Miner": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FMiner.php:1", "ajax": false, "filename": "Miner.php", "line": "?"}}}, "count": 10, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://orexcoin.test/user/plan-purchase/pay", "action_name": "user.plan-purchase.pay", "controller_action": "App\\Http\\Controllers\\Frontend\\PlanPurchaseController@planPurchaseNow", "uri": "POST user/plan-purchase/pay", "controller": "App\\Http\\Controllers\\Frontend\\PlanPurchaseController@planPurchaseNow<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:128\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "user/plan-purchase", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FFrontend%2FPlanPurchaseController.php:128\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/PlanPurchaseController.php:128-150</a>", "middleware": "web, auth:web, 2fa, check_deactivation, verified, check_feature:plan_purchase,kyc_plan_purchase", "duration": "344ms", "peak_memory": "34MB", "response": "Redirect to https://orexcoin.test/user/mining", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1737327889 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1737327889\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-228721160 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BH5IPda4hzHNSzJjHWxHu92ZkCc7CfFowEonEkF</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>scheme_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228721160\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1218157149 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"734 characters\">acceptCookies=true; XSRF-TOKEN=eyJpdiI6Ik9hNkQ4bEsyRGJJSG1zT29SRGdVV1E9PSIsInZhbHVlIjoicG9nT0tXaDZrT2JRd1R5S0RFcmFSdDd0eFNvLzhUWW1JR1ZNaVpVb3lyNTBtVUNQRkh3VlV0bEFPOWRKQVdPNlRDNGdnR2RrRDUzeFFzSFB2VVdmUGVvYmlqcjg2azhFV2xGZ2dSVERqQUNrNzRQdEUvd3prYlE2dHdwV3pEd1giLCJtYWMiOiJjZjYyMDk4MmU2ZGNiZGNhZDdmYTdjYmEyZjczMmRiYmE0ZjYxZDI0MDYzMTNkZWI3MzhiNWY0NmYyYjNjNjIzIiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6Ik1GMG5WOFUzdWVHY00yV2RHZFRHL0E9PSIsInZhbHVlIjoiSE85TDVQZTg5RnFXd3J1S0wyK2x5T3RaZnk4RzMvWnlCajBmUmxvaWtMTkF6UCsxanFpRy9FNWI1QjBVU0N0dDNpY2NLdThZV2xORHk3bVNVNXJWaTRCbzluSnVoeFF3VDMwYWh3ODErL3NsMGN0UjZjNHBsem1KNFFPbTFSeGMiLCJtYWMiOiJiYTc3ZGM1YzY3Yjg2MzM1MTYxOWQ2NGQ3ZmZlNzhmZWQyZjQ3NzU3MzQ0NWMwYTc2ODI0MGI1ZGE3MjdiY2RlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">https://orexcoin.test/user/plan-purchase/checkout/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundary5YaRA0t5SLfkS58n</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">https://orexcoin.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">376</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218157149\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-826457308 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>acceptCookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BH5IPda4hzHNSzJjHWxHu92ZkCc7CfFowEonEkF</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oGjwXvI2S05jRt9urdB3N1br7wpMijx1NNUSl9lh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826457308\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1517333757 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 04:15:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">https://orexcoin.test/user/mining</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1517333757\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1722048240 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BH5IPda4hzHNSzJjHWxHu92ZkCc7CfFowEonEkF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">notify</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">https://orexcoin.test/user/plan-purchase/checkout/2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">dark</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>notify</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Plan purchased successfully</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1722048240\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://orexcoin.test/user/plan-purchase/pay", "action_name": "user.plan-purchase.pay", "controller_action": "App\\Http\\Controllers\\Frontend\\PlanPurchaseController@planPurchaseNow"}, "badge": "302 Found"}}