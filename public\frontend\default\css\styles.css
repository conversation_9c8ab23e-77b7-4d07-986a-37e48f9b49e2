@charset "UTF-8";
/*-----------------------------------------------------------------------------------

  Project Name: Orex<PERSON>oin – Modern Cryptocurrency Mining & Earnings System
  Author: Tdevs
  Support: <EMAIL>
  Description: OrexCoin – Modern Cryptocurrency Mining & Earnings System
  Version: 1.0

-----------------------------------------------------------------------------------

/*----------------------------------------*/
/*   Globals Default
/*----------------------------------------*/
@import url("https://fonts.googleapis.com/css2?family=Bai+Jamjuree:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Outfit:wght@100..900&display=swap");
.mt-1 {
  margin-top: 1px;
}

.mb-1 {
  margin-bottom: 1px;
}

.ml-1 {
  margin-inline-start: 1px;
}

.mr-1 {
  margin-inline-end: 1px;
}

.mt-2 {
  margin-top: 2px;
}

.mb-2 {
  margin-bottom: 2px;
}

.ml-2 {
  margin-inline-start: 2px;
}

.mr-2 {
  margin-inline-end: 2px;
}

.mt-3 {
  margin-top: 3px;
}

.mb-3 {
  margin-bottom: 3px;
}

.ml-3 {
  margin-inline-start: 3px;
}

.mr-3 {
  margin-inline-end: 3px;
}

.mt-4 {
  margin-top: 4px;
}

.mb-4 {
  margin-bottom: 4px;
}

.ml-4 {
  margin-inline-start: 4px;
}

.mr-4 {
  margin-inline-end: 4px;
}

.mt-5 {
  margin-top: 5px;
}

.mb-5 {
  margin-bottom: 5px;
}

.ml-5 {
  margin-inline-start: 5px;
}

.mr-5 {
  margin-inline-end: 5px;
}

.mt-6 {
  margin-top: 6px;
}

.mb-6 {
  margin-bottom: 6px;
}

.ml-6 {
  margin-inline-start: 6px;
}

.mr-6 {
  margin-inline-end: 6px;
}

.mt-7 {
  margin-top: 7px;
}

.mb-7 {
  margin-bottom: 7px;
}

.ml-7 {
  margin-inline-start: 7px;
}

.mr-7 {
  margin-inline-end: 7px;
}

.mt-8 {
  margin-top: 8px;
}

.mb-8 {
  margin-bottom: 8px;
}

.ml-8 {
  margin-inline-start: 8px;
}

.mr-8 {
  margin-inline-end: 8px;
}

.mt-9 {
  margin-top: 9px;
}

.mb-9 {
  margin-bottom: 9px;
}

.ml-9 {
  margin-inline-start: 9px;
}

.mr-9 {
  margin-inline-end: 9px;
}

.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-inline-start: 10px;
}

.mr-10 {
  margin-inline-end: 10px;
}

.mt-11 {
  margin-top: 11px;
}

.mb-11 {
  margin-bottom: 11px;
}

.ml-11 {
  margin-inline-start: 11px;
}

.mr-11 {
  margin-inline-end: 11px;
}

.mt-12 {
  margin-top: 12px;
}

.mb-12 {
  margin-bottom: 12px;
}

.ml-12 {
  margin-inline-start: 12px;
}

.mr-12 {
  margin-inline-end: 12px;
}

.mt-13 {
  margin-top: 13px;
}

.mb-13 {
  margin-bottom: 13px;
}

.ml-13 {
  margin-inline-start: 13px;
}

.mr-13 {
  margin-inline-end: 13px;
}

.mt-14 {
  margin-top: 14px;
}

.mb-14 {
  margin-bottom: 14px;
}

.ml-14 {
  margin-inline-start: 14px;
}

.mr-14 {
  margin-inline-end: 14px;
}

.mt-15 {
  margin-top: 15px;
}

.mb-15 {
  margin-bottom: 15px;
}

.ml-15 {
  margin-inline-start: 15px;
}

.mr-15 {
  margin-inline-end: 15px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-16 {
  margin-inline-start: 16px;
}

.mr-16 {
  margin-inline-end: 16px;
}

.mt-17 {
  margin-top: 17px;
}

.mb-17 {
  margin-bottom: 17px;
}

.ml-17 {
  margin-inline-start: 17px;
}

.mr-17 {
  margin-inline-end: 17px;
}

.mt-18 {
  margin-top: 18px;
}

.mb-18 {
  margin-bottom: 18px;
}

.ml-18 {
  margin-inline-start: 18px;
}

.mr-18 {
  margin-inline-end: 18px;
}

.mt-19 {
  margin-top: 19px;
}

.mb-19 {
  margin-bottom: 19px;
}

.ml-19 {
  margin-inline-start: 19px;
}

.mr-19 {
  margin-inline-end: 19px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.ml-20 {
  margin-inline-start: 20px;
}

.mr-20 {
  margin-inline-end: 20px;
}

.mt-21 {
  margin-top: 21px;
}

.mb-21 {
  margin-bottom: 21px;
}

.ml-21 {
  margin-inline-start: 21px;
}

.mr-21 {
  margin-inline-end: 21px;
}

.mt-22 {
  margin-top: 22px;
}

.mb-22 {
  margin-bottom: 22px;
}

.ml-22 {
  margin-inline-start: 22px;
}

.mr-22 {
  margin-inline-end: 22px;
}

.mt-23 {
  margin-top: 23px;
}

.mb-23 {
  margin-bottom: 23px;
}

.ml-23 {
  margin-inline-start: 23px;
}

.mr-23 {
  margin-inline-end: 23px;
}

.mt-24 {
  margin-top: 24px;
}

.mb-24 {
  margin-bottom: 24px;
}

.ml-24 {
  margin-inline-start: 24px;
}

.mr-24 {
  margin-inline-end: 24px;
}

.mt-25 {
  margin-top: 25px;
}

.mb-25 {
  margin-bottom: 25px;
}

.ml-25 {
  margin-inline-start: 25px;
}

.mr-25 {
  margin-inline-end: 25px;
}

.mt-26 {
  margin-top: 26px;
}

.mb-26 {
  margin-bottom: 26px;
}

.ml-26 {
  margin-inline-start: 26px;
}

.mr-26 {
  margin-inline-end: 26px;
}

.mt-27 {
  margin-top: 27px;
}

.mb-27 {
  margin-bottom: 27px;
}

.ml-27 {
  margin-inline-start: 27px;
}

.mr-27 {
  margin-inline-end: 27px;
}

.mt-28 {
  margin-top: 28px;
}

.mb-28 {
  margin-bottom: 28px;
}

.ml-28 {
  margin-inline-start: 28px;
}

.mr-28 {
  margin-inline-end: 28px;
}

.mt-29 {
  margin-top: 29px;
}

.mb-29 {
  margin-bottom: 29px;
}

.ml-29 {
  margin-inline-start: 29px;
}

.mr-29 {
  margin-inline-end: 29px;
}

.mt-30 {
  margin-top: 30px;
}

.mb-30 {
  margin-bottom: 30px;
}

.ml-30 {
  margin-inline-start: 30px;
}

.mr-30 {
  margin-inline-end: 30px;
}

.mt-31 {
  margin-top: 31px;
}

.mb-31 {
  margin-bottom: 31px;
}

.ml-31 {
  margin-inline-start: 31px;
}

.mr-31 {
  margin-inline-end: 31px;
}

.mt-32 {
  margin-top: 32px;
}

.mb-32 {
  margin-bottom: 32px;
}

.ml-32 {
  margin-inline-start: 32px;
}

.mr-32 {
  margin-inline-end: 32px;
}

.mt-33 {
  margin-top: 33px;
}

.mb-33 {
  margin-bottom: 33px;
}

.ml-33 {
  margin-inline-start: 33px;
}

.mr-33 {
  margin-inline-end: 33px;
}

.mt-34 {
  margin-top: 34px;
}

.mb-34 {
  margin-bottom: 34px;
}

.ml-34 {
  margin-inline-start: 34px;
}

.mr-34 {
  margin-inline-end: 34px;
}

.mt-35 {
  margin-top: 35px;
}

.mb-35 {
  margin-bottom: 35px;
}

.ml-35 {
  margin-inline-start: 35px;
}

.mr-35 {
  margin-inline-end: 35px;
}

.mt-36 {
  margin-top: 36px;
}

.mb-36 {
  margin-bottom: 36px;
}

.ml-36 {
  margin-inline-start: 36px;
}

.mr-36 {
  margin-inline-end: 36px;
}

.mt-37 {
  margin-top: 37px;
}

.mb-37 {
  margin-bottom: 37px;
}

.ml-37 {
  margin-inline-start: 37px;
}

.mr-37 {
  margin-inline-end: 37px;
}

.mt-38 {
  margin-top: 38px;
}

.mb-38 {
  margin-bottom: 38px;
}

.ml-38 {
  margin-inline-start: 38px;
}

.mr-38 {
  margin-inline-end: 38px;
}

.mt-39 {
  margin-top: 39px;
}

.mb-39 {
  margin-bottom: 39px;
}

.ml-39 {
  margin-inline-start: 39px;
}

.mr-39 {
  margin-inline-end: 39px;
}

.mt-40 {
  margin-top: 40px;
}

.mb-40 {
  margin-bottom: 40px;
}

.ml-40 {
  margin-inline-start: 40px;
}

.mr-40 {
  margin-inline-end: 40px;
}

.mt-41 {
  margin-top: 41px;
}

.mb-41 {
  margin-bottom: 41px;
}

.ml-41 {
  margin-inline-start: 41px;
}

.mr-41 {
  margin-inline-end: 41px;
}

.mt-42 {
  margin-top: 42px;
}

.mb-42 {
  margin-bottom: 42px;
}

.ml-42 {
  margin-inline-start: 42px;
}

.mr-42 {
  margin-inline-end: 42px;
}

.mt-43 {
  margin-top: 43px;
}

.mb-43 {
  margin-bottom: 43px;
}

.ml-43 {
  margin-inline-start: 43px;
}

.mr-43 {
  margin-inline-end: 43px;
}

.mt-44 {
  margin-top: 44px;
}

.mb-44 {
  margin-bottom: 44px;
}

.ml-44 {
  margin-inline-start: 44px;
}

.mr-44 {
  margin-inline-end: 44px;
}

.mt-45 {
  margin-top: 45px;
}

.mb-45 {
  margin-bottom: 45px;
}

.ml-45 {
  margin-inline-start: 45px;
}

.mr-45 {
  margin-inline-end: 45px;
}

.mt-46 {
  margin-top: 46px;
}

.mb-46 {
  margin-bottom: 46px;
}

.ml-46 {
  margin-inline-start: 46px;
}

.mr-46 {
  margin-inline-end: 46px;
}

.mt-47 {
  margin-top: 47px;
}

.mb-47 {
  margin-bottom: 47px;
}

.ml-47 {
  margin-inline-start: 47px;
}

.mr-47 {
  margin-inline-end: 47px;
}

.mt-48 {
  margin-top: 48px;
}

.mb-48 {
  margin-bottom: 48px;
}

.ml-48 {
  margin-inline-start: 48px;
}

.mr-48 {
  margin-inline-end: 48px;
}

.mt-49 {
  margin-top: 49px;
}

.mb-49 {
  margin-bottom: 49px;
}

.ml-49 {
  margin-inline-start: 49px;
}

.mr-49 {
  margin-inline-end: 49px;
}

.mt-50 {
  margin-top: 50px;
}

.mb-50 {
  margin-bottom: 50px;
}

.ml-50 {
  margin-inline-start: 50px;
}

.mr-50 {
  margin-inline-end: 50px;
}

.mt-51 {
  margin-top: 51px;
}

.mb-51 {
  margin-bottom: 51px;
}

.ml-51 {
  margin-inline-start: 51px;
}

.mr-51 {
  margin-inline-end: 51px;
}

.mt-52 {
  margin-top: 52px;
}

.mb-52 {
  margin-bottom: 52px;
}

.ml-52 {
  margin-inline-start: 52px;
}

.mr-52 {
  margin-inline-end: 52px;
}

.mt-53 {
  margin-top: 53px;
}

.mb-53 {
  margin-bottom: 53px;
}

.ml-53 {
  margin-inline-start: 53px;
}

.mr-53 {
  margin-inline-end: 53px;
}

.mt-54 {
  margin-top: 54px;
}

.mb-54 {
  margin-bottom: 54px;
}

.ml-54 {
  margin-inline-start: 54px;
}

.mr-54 {
  margin-inline-end: 54px;
}

.mt-55 {
  margin-top: 55px;
}

.mb-55 {
  margin-bottom: 55px;
}

.ml-55 {
  margin-inline-start: 55px;
}

.mr-55 {
  margin-inline-end: 55px;
}

.mt-56 {
  margin-top: 56px;
}

.mb-56 {
  margin-bottom: 56px;
}

.ml-56 {
  margin-inline-start: 56px;
}

.mr-56 {
  margin-inline-end: 56px;
}

.mt-57 {
  margin-top: 57px;
}

.mb-57 {
  margin-bottom: 57px;
}

.ml-57 {
  margin-inline-start: 57px;
}

.mr-57 {
  margin-inline-end: 57px;
}

.mt-58 {
  margin-top: 58px;
}

.mb-58 {
  margin-bottom: 58px;
}

.ml-58 {
  margin-inline-start: 58px;
}

.mr-58 {
  margin-inline-end: 58px;
}

.mt-59 {
  margin-top: 59px;
}

.mb-59 {
  margin-bottom: 59px;
}

.ml-59 {
  margin-inline-start: 59px;
}

.mr-59 {
  margin-inline-end: 59px;
}

.mt-60 {
  margin-top: 60px;
}

.mb-60 {
  margin-bottom: 60px;
}

.ml-60 {
  margin-inline-start: 60px;
}

.mr-60 {
  margin-inline-end: 60px;
}

.mt-61 {
  margin-top: 61px;
}

.mb-61 {
  margin-bottom: 61px;
}

.ml-61 {
  margin-inline-start: 61px;
}

.mr-61 {
  margin-inline-end: 61px;
}

.mt-62 {
  margin-top: 62px;
}

.mb-62 {
  margin-bottom: 62px;
}

.ml-62 {
  margin-inline-start: 62px;
}

.mr-62 {
  margin-inline-end: 62px;
}

.mt-63 {
  margin-top: 63px;
}

.mb-63 {
  margin-bottom: 63px;
}

.ml-63 {
  margin-inline-start: 63px;
}

.mr-63 {
  margin-inline-end: 63px;
}

.mt-64 {
  margin-top: 64px;
}

.mb-64 {
  margin-bottom: 64px;
}

.ml-64 {
  margin-inline-start: 64px;
}

.mr-64 {
  margin-inline-end: 64px;
}

.mt-65 {
  margin-top: 65px;
}

.mb-65 {
  margin-bottom: 65px;
}

.ml-65 {
  margin-inline-start: 65px;
}

.mr-65 {
  margin-inline-end: 65px;
}

.mt-66 {
  margin-top: 66px;
}

.mb-66 {
  margin-bottom: 66px;
}

.ml-66 {
  margin-inline-start: 66px;
}

.mr-66 {
  margin-inline-end: 66px;
}

.mt-67 {
  margin-top: 67px;
}

.mb-67 {
  margin-bottom: 67px;
}

.ml-67 {
  margin-inline-start: 67px;
}

.mr-67 {
  margin-inline-end: 67px;
}

.mt-68 {
  margin-top: 68px;
}

.mb-68 {
  margin-bottom: 68px;
}

.ml-68 {
  margin-inline-start: 68px;
}

.mr-68 {
  margin-inline-end: 68px;
}

.mt-69 {
  margin-top: 69px;
}

.mb-69 {
  margin-bottom: 69px;
}

.ml-69 {
  margin-inline-start: 69px;
}

.mr-69 {
  margin-inline-end: 69px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-70 {
  margin-bottom: 70px;
}

.ml-70 {
  margin-inline-start: 70px;
}

.mr-70 {
  margin-inline-end: 70px;
}

.mt-71 {
  margin-top: 71px;
}

.mb-71 {
  margin-bottom: 71px;
}

.ml-71 {
  margin-inline-start: 71px;
}

.mr-71 {
  margin-inline-end: 71px;
}

.mt-72 {
  margin-top: 72px;
}

.mb-72 {
  margin-bottom: 72px;
}

.ml-72 {
  margin-inline-start: 72px;
}

.mr-72 {
  margin-inline-end: 72px;
}

.mt-73 {
  margin-top: 73px;
}

.mb-73 {
  margin-bottom: 73px;
}

.ml-73 {
  margin-inline-start: 73px;
}

.mr-73 {
  margin-inline-end: 73px;
}

.mt-74 {
  margin-top: 74px;
}

.mb-74 {
  margin-bottom: 74px;
}

.ml-74 {
  margin-inline-start: 74px;
}

.mr-74 {
  margin-inline-end: 74px;
}

.mt-75 {
  margin-top: 75px;
}

.mb-75 {
  margin-bottom: 75px;
}

.ml-75 {
  margin-inline-start: 75px;
}

.mr-75 {
  margin-inline-end: 75px;
}

.mt-76 {
  margin-top: 76px;
}

.mb-76 {
  margin-bottom: 76px;
}

.ml-76 {
  margin-inline-start: 76px;
}

.mr-76 {
  margin-inline-end: 76px;
}

.mt-77 {
  margin-top: 77px;
}

.mb-77 {
  margin-bottom: 77px;
}

.ml-77 {
  margin-inline-start: 77px;
}

.mr-77 {
  margin-inline-end: 77px;
}

.mt-78 {
  margin-top: 78px;
}

.mb-78 {
  margin-bottom: 78px;
}

.ml-78 {
  margin-inline-start: 78px;
}

.mr-78 {
  margin-inline-end: 78px;
}

.mt-79 {
  margin-top: 79px;
}

.mb-79 {
  margin-bottom: 79px;
}

.ml-79 {
  margin-inline-start: 79px;
}

.mr-79 {
  margin-inline-end: 79px;
}

.mt-80 {
  margin-top: 80px;
}

.mb-80 {
  margin-bottom: 80px;
}

.ml-80 {
  margin-inline-start: 80px;
}

.mr-80 {
  margin-inline-end: 80px;
}

.mt-81 {
  margin-top: 81px;
}

.mb-81 {
  margin-bottom: 81px;
}

.ml-81 {
  margin-inline-start: 81px;
}

.mr-81 {
  margin-inline-end: 81px;
}

.mt-82 {
  margin-top: 82px;
}

.mb-82 {
  margin-bottom: 82px;
}

.ml-82 {
  margin-inline-start: 82px;
}

.mr-82 {
  margin-inline-end: 82px;
}

.mt-83 {
  margin-top: 83px;
}

.mb-83 {
  margin-bottom: 83px;
}

.ml-83 {
  margin-inline-start: 83px;
}

.mr-83 {
  margin-inline-end: 83px;
}

.mt-84 {
  margin-top: 84px;
}

.mb-84 {
  margin-bottom: 84px;
}

.ml-84 {
  margin-inline-start: 84px;
}

.mr-84 {
  margin-inline-end: 84px;
}

.mt-85 {
  margin-top: 85px;
}

.mb-85 {
  margin-bottom: 85px;
}

.ml-85 {
  margin-inline-start: 85px;
}

.mr-85 {
  margin-inline-end: 85px;
}

.mt-86 {
  margin-top: 86px;
}

.mb-86 {
  margin-bottom: 86px;
}

.ml-86 {
  margin-inline-start: 86px;
}

.mr-86 {
  margin-inline-end: 86px;
}

.mt-87 {
  margin-top: 87px;
}

.mb-87 {
  margin-bottom: 87px;
}

.ml-87 {
  margin-inline-start: 87px;
}

.mr-87 {
  margin-inline-end: 87px;
}

.mt-88 {
  margin-top: 88px;
}

.mb-88 {
  margin-bottom: 88px;
}

.ml-88 {
  margin-inline-start: 88px;
}

.mr-88 {
  margin-inline-end: 88px;
}

.mt-89 {
  margin-top: 89px;
}

.mb-89 {
  margin-bottom: 89px;
}

.ml-89 {
  margin-inline-start: 89px;
}

.mr-89 {
  margin-inline-end: 89px;
}

.mt-90 {
  margin-top: 90px;
}

.mb-90 {
  margin-bottom: 90px;
}

.ml-90 {
  margin-inline-start: 90px;
}

.mr-90 {
  margin-inline-end: 90px;
}

.mt-91 {
  margin-top: 91px;
}

.mb-91 {
  margin-bottom: 91px;
}

.ml-91 {
  margin-inline-start: 91px;
}

.mr-91 {
  margin-inline-end: 91px;
}

.mt-92 {
  margin-top: 92px;
}

.mb-92 {
  margin-bottom: 92px;
}

.ml-92 {
  margin-inline-start: 92px;
}

.mr-92 {
  margin-inline-end: 92px;
}

.mt-93 {
  margin-top: 93px;
}

.mb-93 {
  margin-bottom: 93px;
}

.ml-93 {
  margin-inline-start: 93px;
}

.mr-93 {
  margin-inline-end: 93px;
}

.mt-94 {
  margin-top: 94px;
}

.mb-94 {
  margin-bottom: 94px;
}

.ml-94 {
  margin-inline-start: 94px;
}

.mr-94 {
  margin-inline-end: 94px;
}

.mt-95 {
  margin-top: 95px;
}

.mb-95 {
  margin-bottom: 95px;
}

.ml-95 {
  margin-inline-start: 95px;
}

.mr-95 {
  margin-inline-end: 95px;
}

.mt-96 {
  margin-top: 96px;
}

.mb-96 {
  margin-bottom: 96px;
}

.ml-96 {
  margin-inline-start: 96px;
}

.mr-96 {
  margin-inline-end: 96px;
}

.mt-97 {
  margin-top: 97px;
}

.mb-97 {
  margin-bottom: 97px;
}

.ml-97 {
  margin-inline-start: 97px;
}

.mr-97 {
  margin-inline-end: 97px;
}

.mt-98 {
  margin-top: 98px;
}

.mb-98 {
  margin-bottom: 98px;
}

.ml-98 {
  margin-inline-start: 98px;
}

.mr-98 {
  margin-inline-end: 98px;
}

.mt-99 {
  margin-top: 99px;
}

.mb-99 {
  margin-bottom: 99px;
}

.ml-99 {
  margin-inline-start: 99px;
}

.mr-99 {
  margin-inline-end: 99px;
}

.mt-100 {
  margin-top: 100px;
}

.mb-100 {
  margin-bottom: 100px;
}

.ml-100 {
  margin-inline-start: 100px;
}

.mr-100 {
  margin-inline-end: 100px;
}

.mt-101 {
  margin-top: 101px;
}

.mb-101 {
  margin-bottom: 101px;
}

.ml-101 {
  margin-inline-start: 101px;
}

.mr-101 {
  margin-inline-end: 101px;
}

.mt-102 {
  margin-top: 102px;
}

.mb-102 {
  margin-bottom: 102px;
}

.ml-102 {
  margin-inline-start: 102px;
}

.mr-102 {
  margin-inline-end: 102px;
}

.mt-103 {
  margin-top: 103px;
}

.mb-103 {
  margin-bottom: 103px;
}

.ml-103 {
  margin-inline-start: 103px;
}

.mr-103 {
  margin-inline-end: 103px;
}

.mt-104 {
  margin-top: 104px;
}

.mb-104 {
  margin-bottom: 104px;
}

.ml-104 {
  margin-inline-start: 104px;
}

.mr-104 {
  margin-inline-end: 104px;
}

.mt-105 {
  margin-top: 105px;
}

.mb-105 {
  margin-bottom: 105px;
}

.ml-105 {
  margin-inline-start: 105px;
}

.mr-105 {
  margin-inline-end: 105px;
}

.mt-106 {
  margin-top: 106px;
}

.mb-106 {
  margin-bottom: 106px;
}

.ml-106 {
  margin-inline-start: 106px;
}

.mr-106 {
  margin-inline-end: 106px;
}

.mt-107 {
  margin-top: 107px;
}

.mb-107 {
  margin-bottom: 107px;
}

.ml-107 {
  margin-inline-start: 107px;
}

.mr-107 {
  margin-inline-end: 107px;
}

.mt-108 {
  margin-top: 108px;
}

.mb-108 {
  margin-bottom: 108px;
}

.ml-108 {
  margin-inline-start: 108px;
}

.mr-108 {
  margin-inline-end: 108px;
}

.mt-109 {
  margin-top: 109px;
}

.mb-109 {
  margin-bottom: 109px;
}

.ml-109 {
  margin-inline-start: 109px;
}

.mr-109 {
  margin-inline-end: 109px;
}

.mt-110 {
  margin-top: 110px;
}

.mb-110 {
  margin-bottom: 110px;
}

.ml-110 {
  margin-inline-start: 110px;
}

.mr-110 {
  margin-inline-end: 110px;
}

.mt-111 {
  margin-top: 111px;
}

.mb-111 {
  margin-bottom: 111px;
}

.ml-111 {
  margin-inline-start: 111px;
}

.mr-111 {
  margin-inline-end: 111px;
}

.mt-112 {
  margin-top: 112px;
}

.mb-112 {
  margin-bottom: 112px;
}

.ml-112 {
  margin-inline-start: 112px;
}

.mr-112 {
  margin-inline-end: 112px;
}

.mt-113 {
  margin-top: 113px;
}

.mb-113 {
  margin-bottom: 113px;
}

.ml-113 {
  margin-inline-start: 113px;
}

.mr-113 {
  margin-inline-end: 113px;
}

.mt-114 {
  margin-top: 114px;
}

.mb-114 {
  margin-bottom: 114px;
}

.ml-114 {
  margin-inline-start: 114px;
}

.mr-114 {
  margin-inline-end: 114px;
}

.mt-115 {
  margin-top: 115px;
}

.mb-115 {
  margin-bottom: 115px;
}

.ml-115 {
  margin-inline-start: 115px;
}

.mr-115 {
  margin-inline-end: 115px;
}

.mt-116 {
  margin-top: 116px;
}

.mb-116 {
  margin-bottom: 116px;
}

.ml-116 {
  margin-inline-start: 116px;
}

.mr-116 {
  margin-inline-end: 116px;
}

.mt-117 {
  margin-top: 117px;
}

.mb-117 {
  margin-bottom: 117px;
}

.ml-117 {
  margin-inline-start: 117px;
}

.mr-117 {
  margin-inline-end: 117px;
}

.mt-118 {
  margin-top: 118px;
}

.mb-118 {
  margin-bottom: 118px;
}

.ml-118 {
  margin-inline-start: 118px;
}

.mr-118 {
  margin-inline-end: 118px;
}

.mt-119 {
  margin-top: 119px;
}

.mb-119 {
  margin-bottom: 119px;
}

.ml-119 {
  margin-inline-start: 119px;
}

.mr-119 {
  margin-inline-end: 119px;
}

.mt-120 {
  margin-top: 120px;
}

.mb-120 {
  margin-bottom: 120px;
}

.ml-120 {
  margin-inline-start: 120px;
}

.mr-120 {
  margin-inline-end: 120px;
}

.mt-121 {
  margin-top: 121px;
}

.mb-121 {
  margin-bottom: 121px;
}

.ml-121 {
  margin-inline-start: 121px;
}

.mr-121 {
  margin-inline-end: 121px;
}

.mt-122 {
  margin-top: 122px;
}

.mb-122 {
  margin-bottom: 122px;
}

.ml-122 {
  margin-inline-start: 122px;
}

.mr-122 {
  margin-inline-end: 122px;
}

.mt-123 {
  margin-top: 123px;
}

.mb-123 {
  margin-bottom: 123px;
}

.ml-123 {
  margin-inline-start: 123px;
}

.mr-123 {
  margin-inline-end: 123px;
}

.mt-124 {
  margin-top: 124px;
}

.mb-124 {
  margin-bottom: 124px;
}

.ml-124 {
  margin-inline-start: 124px;
}

.mr-124 {
  margin-inline-end: 124px;
}

.mt-125 {
  margin-top: 125px;
}

.mb-125 {
  margin-bottom: 125px;
}

.ml-125 {
  margin-inline-start: 125px;
}

.mr-125 {
  margin-inline-end: 125px;
}

.mt-126 {
  margin-top: 126px;
}

.mb-126 {
  margin-bottom: 126px;
}

.ml-126 {
  margin-inline-start: 126px;
}

.mr-126 {
  margin-inline-end: 126px;
}

.mt-127 {
  margin-top: 127px;
}

.mb-127 {
  margin-bottom: 127px;
}

.ml-127 {
  margin-inline-start: 127px;
}

.mr-127 {
  margin-inline-end: 127px;
}

.mt-128 {
  margin-top: 128px;
}

.mb-128 {
  margin-bottom: 128px;
}

.ml-128 {
  margin-inline-start: 128px;
}

.mr-128 {
  margin-inline-end: 128px;
}

.mt-129 {
  margin-top: 129px;
}

.mb-129 {
  margin-bottom: 129px;
}

.ml-129 {
  margin-inline-start: 129px;
}

.mr-129 {
  margin-inline-end: 129px;
}

.mt-130 {
  margin-top: 130px;
}

.mb-130 {
  margin-bottom: 130px;
}

.ml-130 {
  margin-inline-start: 130px;
}

.mr-130 {
  margin-inline-end: 130px;
}

.mt-131 {
  margin-top: 131px;
}

.mb-131 {
  margin-bottom: 131px;
}

.ml-131 {
  margin-inline-start: 131px;
}

.mr-131 {
  margin-inline-end: 131px;
}

.mt-132 {
  margin-top: 132px;
}

.mb-132 {
  margin-bottom: 132px;
}

.ml-132 {
  margin-inline-start: 132px;
}

.mr-132 {
  margin-inline-end: 132px;
}

.mt-133 {
  margin-top: 133px;
}

.mb-133 {
  margin-bottom: 133px;
}

.ml-133 {
  margin-inline-start: 133px;
}

.mr-133 {
  margin-inline-end: 133px;
}

.mt-134 {
  margin-top: 134px;
}

.mb-134 {
  margin-bottom: 134px;
}

.ml-134 {
  margin-inline-start: 134px;
}

.mr-134 {
  margin-inline-end: 134px;
}

.mt-135 {
  margin-top: 135px;
}

.mb-135 {
  margin-bottom: 135px;
}

.ml-135 {
  margin-inline-start: 135px;
}

.mr-135 {
  margin-inline-end: 135px;
}

.mt-136 {
  margin-top: 136px;
}

.mb-136 {
  margin-bottom: 136px;
}

.ml-136 {
  margin-inline-start: 136px;
}

.mr-136 {
  margin-inline-end: 136px;
}

.mt-137 {
  margin-top: 137px;
}

.mb-137 {
  margin-bottom: 137px;
}

.ml-137 {
  margin-inline-start: 137px;
}

.mr-137 {
  margin-inline-end: 137px;
}

.mt-138 {
  margin-top: 138px;
}

.mb-138 {
  margin-bottom: 138px;
}

.ml-138 {
  margin-inline-start: 138px;
}

.mr-138 {
  margin-inline-end: 138px;
}

.mt-139 {
  margin-top: 139px;
}

.mb-139 {
  margin-bottom: 139px;
}

.ml-139 {
  margin-inline-start: 139px;
}

.mr-139 {
  margin-inline-end: 139px;
}

.mt-140 {
  margin-top: 140px;
}

.mb-140 {
  margin-bottom: 140px;
}

.ml-140 {
  margin-inline-start: 140px;
}

.mr-140 {
  margin-inline-end: 140px;
}

.mt-141 {
  margin-top: 141px;
}

.mb-141 {
  margin-bottom: 141px;
}

.ml-141 {
  margin-inline-start: 141px;
}

.mr-141 {
  margin-inline-end: 141px;
}

.mt-142 {
  margin-top: 142px;
}

.mb-142 {
  margin-bottom: 142px;
}

.ml-142 {
  margin-inline-start: 142px;
}

.mr-142 {
  margin-inline-end: 142px;
}

.mt-143 {
  margin-top: 143px;
}

.mb-143 {
  margin-bottom: 143px;
}

.ml-143 {
  margin-inline-start: 143px;
}

.mr-143 {
  margin-inline-end: 143px;
}

.mt-144 {
  margin-top: 144px;
}

.mb-144 {
  margin-bottom: 144px;
}

.ml-144 {
  margin-inline-start: 144px;
}

.mr-144 {
  margin-inline-end: 144px;
}

.mt-145 {
  margin-top: 145px;
}

.mb-145 {
  margin-bottom: 145px;
}

.ml-145 {
  margin-inline-start: 145px;
}

.mr-145 {
  margin-inline-end: 145px;
}

.mt-146 {
  margin-top: 146px;
}

.mb-146 {
  margin-bottom: 146px;
}

.ml-146 {
  margin-inline-start: 146px;
}

.mr-146 {
  margin-inline-end: 146px;
}

.mt-147 {
  margin-top: 147px;
}

.mb-147 {
  margin-bottom: 147px;
}

.ml-147 {
  margin-inline-start: 147px;
}

.mr-147 {
  margin-inline-end: 147px;
}

.mt-148 {
  margin-top: 148px;
}

.mb-148 {
  margin-bottom: 148px;
}

.ml-148 {
  margin-inline-start: 148px;
}

.mr-148 {
  margin-inline-end: 148px;
}

.mt-149 {
  margin-top: 149px;
}

.mb-149 {
  margin-bottom: 149px;
}

.ml-149 {
  margin-inline-start: 149px;
}

.mr-149 {
  margin-inline-end: 149px;
}

.mt-150 {
  margin-top: 150px;
}

.mb-150 {
  margin-bottom: 150px;
}

.ml-150 {
  margin-inline-start: 150px;
}

.mr-150 {
  margin-inline-end: 150px;
}

.mt-151 {
  margin-top: 151px;
}

.mb-151 {
  margin-bottom: 151px;
}

.ml-151 {
  margin-inline-start: 151px;
}

.mr-151 {
  margin-inline-end: 151px;
}

.mt-152 {
  margin-top: 152px;
}

.mb-152 {
  margin-bottom: 152px;
}

.ml-152 {
  margin-inline-start: 152px;
}

.mr-152 {
  margin-inline-end: 152px;
}

.mt-153 {
  margin-top: 153px;
}

.mb-153 {
  margin-bottom: 153px;
}

.ml-153 {
  margin-inline-start: 153px;
}

.mr-153 {
  margin-inline-end: 153px;
}

.mt-154 {
  margin-top: 154px;
}

.mb-154 {
  margin-bottom: 154px;
}

.ml-154 {
  margin-inline-start: 154px;
}

.mr-154 {
  margin-inline-end: 154px;
}

.mt-155 {
  margin-top: 155px;
}

.mb-155 {
  margin-bottom: 155px;
}

.ml-155 {
  margin-inline-start: 155px;
}

.mr-155 {
  margin-inline-end: 155px;
}

.mt-156 {
  margin-top: 156px;
}

.mb-156 {
  margin-bottom: 156px;
}

.ml-156 {
  margin-inline-start: 156px;
}

.mr-156 {
  margin-inline-end: 156px;
}

.mt-157 {
  margin-top: 157px;
}

.mb-157 {
  margin-bottom: 157px;
}

.ml-157 {
  margin-inline-start: 157px;
}

.mr-157 {
  margin-inline-end: 157px;
}

.mt-158 {
  margin-top: 158px;
}

.mb-158 {
  margin-bottom: 158px;
}

.ml-158 {
  margin-inline-start: 158px;
}

.mr-158 {
  margin-inline-end: 158px;
}

.mt-159 {
  margin-top: 159px;
}

.mb-159 {
  margin-bottom: 159px;
}

.ml-159 {
  margin-inline-start: 159px;
}

.mr-159 {
  margin-inline-end: 159px;
}

.mt-160 {
  margin-top: 160px;
}

.mb-160 {
  margin-bottom: 160px;
}

.ml-160 {
  margin-inline-start: 160px;
}

.mr-160 {
  margin-inline-end: 160px;
}

.mt-161 {
  margin-top: 161px;
}

.mb-161 {
  margin-bottom: 161px;
}

.ml-161 {
  margin-inline-start: 161px;
}

.mr-161 {
  margin-inline-end: 161px;
}

.mt-162 {
  margin-top: 162px;
}

.mb-162 {
  margin-bottom: 162px;
}

.ml-162 {
  margin-inline-start: 162px;
}

.mr-162 {
  margin-inline-end: 162px;
}

.mt-163 {
  margin-top: 163px;
}

.mb-163 {
  margin-bottom: 163px;
}

.ml-163 {
  margin-inline-start: 163px;
}

.mr-163 {
  margin-inline-end: 163px;
}

.mt-164 {
  margin-top: 164px;
}

.mb-164 {
  margin-bottom: 164px;
}

.ml-164 {
  margin-inline-start: 164px;
}

.mr-164 {
  margin-inline-end: 164px;
}

.mt-165 {
  margin-top: 165px;
}

.mb-165 {
  margin-bottom: 165px;
}

.ml-165 {
  margin-inline-start: 165px;
}

.mr-165 {
  margin-inline-end: 165px;
}

.mt-166 {
  margin-top: 166px;
}

.mb-166 {
  margin-bottom: 166px;
}

.ml-166 {
  margin-inline-start: 166px;
}

.mr-166 {
  margin-inline-end: 166px;
}

.mt-167 {
  margin-top: 167px;
}

.mb-167 {
  margin-bottom: 167px;
}

.ml-167 {
  margin-inline-start: 167px;
}

.mr-167 {
  margin-inline-end: 167px;
}

.mt-168 {
  margin-top: 168px;
}

.mb-168 {
  margin-bottom: 168px;
}

.ml-168 {
  margin-inline-start: 168px;
}

.mr-168 {
  margin-inline-end: 168px;
}

.mt-169 {
  margin-top: 169px;
}

.mb-169 {
  margin-bottom: 169px;
}

.ml-169 {
  margin-inline-start: 169px;
}

.mr-169 {
  margin-inline-end: 169px;
}

.mt-170 {
  margin-top: 170px;
}

.mb-170 {
  margin-bottom: 170px;
}

.ml-170 {
  margin-inline-start: 170px;
}

.mr-170 {
  margin-inline-end: 170px;
}

.mt-171 {
  margin-top: 171px;
}

.mb-171 {
  margin-bottom: 171px;
}

.ml-171 {
  margin-inline-start: 171px;
}

.mr-171 {
  margin-inline-end: 171px;
}

.mt-172 {
  margin-top: 172px;
}

.mb-172 {
  margin-bottom: 172px;
}

.ml-172 {
  margin-inline-start: 172px;
}

.mr-172 {
  margin-inline-end: 172px;
}

.mt-173 {
  margin-top: 173px;
}

.mb-173 {
  margin-bottom: 173px;
}

.ml-173 {
  margin-inline-start: 173px;
}

.mr-173 {
  margin-inline-end: 173px;
}

.mt-174 {
  margin-top: 174px;
}

.mb-174 {
  margin-bottom: 174px;
}

.ml-174 {
  margin-inline-start: 174px;
}

.mr-174 {
  margin-inline-end: 174px;
}

.mt-175 {
  margin-top: 175px;
}

.mb-175 {
  margin-bottom: 175px;
}

.ml-175 {
  margin-inline-start: 175px;
}

.mr-175 {
  margin-inline-end: 175px;
}

.mt-176 {
  margin-top: 176px;
}

.mb-176 {
  margin-bottom: 176px;
}

.ml-176 {
  margin-inline-start: 176px;
}

.mr-176 {
  margin-inline-end: 176px;
}

.mt-177 {
  margin-top: 177px;
}

.mb-177 {
  margin-bottom: 177px;
}

.ml-177 {
  margin-inline-start: 177px;
}

.mr-177 {
  margin-inline-end: 177px;
}

.mt-178 {
  margin-top: 178px;
}

.mb-178 {
  margin-bottom: 178px;
}

.ml-178 {
  margin-inline-start: 178px;
}

.mr-178 {
  margin-inline-end: 178px;
}

.mt-179 {
  margin-top: 179px;
}

.mb-179 {
  margin-bottom: 179px;
}

.ml-179 {
  margin-inline-start: 179px;
}

.mr-179 {
  margin-inline-end: 179px;
}

.mt-180 {
  margin-top: 180px;
}

.mb-180 {
  margin-bottom: 180px;
}

.ml-180 {
  margin-inline-start: 180px;
}

.mr-180 {
  margin-inline-end: 180px;
}

.mt-181 {
  margin-top: 181px;
}

.mb-181 {
  margin-bottom: 181px;
}

.ml-181 {
  margin-inline-start: 181px;
}

.mr-181 {
  margin-inline-end: 181px;
}

.mt-182 {
  margin-top: 182px;
}

.mb-182 {
  margin-bottom: 182px;
}

.ml-182 {
  margin-inline-start: 182px;
}

.mr-182 {
  margin-inline-end: 182px;
}

.mt-183 {
  margin-top: 183px;
}

.mb-183 {
  margin-bottom: 183px;
}

.ml-183 {
  margin-inline-start: 183px;
}

.mr-183 {
  margin-inline-end: 183px;
}

.mt-184 {
  margin-top: 184px;
}

.mb-184 {
  margin-bottom: 184px;
}

.ml-184 {
  margin-inline-start: 184px;
}

.mr-184 {
  margin-inline-end: 184px;
}

.mt-185 {
  margin-top: 185px;
}

.mb-185 {
  margin-bottom: 185px;
}

.ml-185 {
  margin-inline-start: 185px;
}

.mr-185 {
  margin-inline-end: 185px;
}

.mt-186 {
  margin-top: 186px;
}

.mb-186 {
  margin-bottom: 186px;
}

.ml-186 {
  margin-inline-start: 186px;
}

.mr-186 {
  margin-inline-end: 186px;
}

.mt-187 {
  margin-top: 187px;
}

.mb-187 {
  margin-bottom: 187px;
}

.ml-187 {
  margin-inline-start: 187px;
}

.mr-187 {
  margin-inline-end: 187px;
}

.mt-188 {
  margin-top: 188px;
}

.mb-188 {
  margin-bottom: 188px;
}

.ml-188 {
  margin-inline-start: 188px;
}

.mr-188 {
  margin-inline-end: 188px;
}

.mt-189 {
  margin-top: 189px;
}

.mb-189 {
  margin-bottom: 189px;
}

.ml-189 {
  margin-inline-start: 189px;
}

.mr-189 {
  margin-inline-end: 189px;
}

.mt-190 {
  margin-top: 190px;
}

.mb-190 {
  margin-bottom: 190px;
}

.ml-190 {
  margin-inline-start: 190px;
}

.mr-190 {
  margin-inline-end: 190px;
}

.mt-191 {
  margin-top: 191px;
}

.mb-191 {
  margin-bottom: 191px;
}

.ml-191 {
  margin-inline-start: 191px;
}

.mr-191 {
  margin-inline-end: 191px;
}

.mt-192 {
  margin-top: 192px;
}

.mb-192 {
  margin-bottom: 192px;
}

.ml-192 {
  margin-inline-start: 192px;
}

.mr-192 {
  margin-inline-end: 192px;
}

.mt-193 {
  margin-top: 193px;
}

.mb-193 {
  margin-bottom: 193px;
}

.ml-193 {
  margin-inline-start: 193px;
}

.mr-193 {
  margin-inline-end: 193px;
}

.mt-194 {
  margin-top: 194px;
}

.mb-194 {
  margin-bottom: 194px;
}

.ml-194 {
  margin-inline-start: 194px;
}

.mr-194 {
  margin-inline-end: 194px;
}

.mt-195 {
  margin-top: 195px;
}

.mb-195 {
  margin-bottom: 195px;
}

.ml-195 {
  margin-inline-start: 195px;
}

.mr-195 {
  margin-inline-end: 195px;
}

.mt-196 {
  margin-top: 196px;
}

.mb-196 {
  margin-bottom: 196px;
}

.ml-196 {
  margin-inline-start: 196px;
}

.mr-196 {
  margin-inline-end: 196px;
}

.mt-197 {
  margin-top: 197px;
}

.mb-197 {
  margin-bottom: 197px;
}

.ml-197 {
  margin-inline-start: 197px;
}

.mr-197 {
  margin-inline-end: 197px;
}

.mt-198 {
  margin-top: 198px;
}

.mb-198 {
  margin-bottom: 198px;
}

.ml-198 {
  margin-inline-start: 198px;
}

.mr-198 {
  margin-inline-end: 198px;
}

.mt-199 {
  margin-top: 199px;
}

.mb-199 {
  margin-bottom: 199px;
}

.ml-199 {
  margin-inline-start: 199px;
}

.mr-199 {
  margin-inline-end: 199px;
}

.mt-200 {
  margin-top: 200px;
}

.mb-200 {
  margin-bottom: 200px;
}

.ml-200 {
  margin-inline-start: 200px;
}

.mr-200 {
  margin-inline-end: 200px;
}

.pt-1 {
  padding-top: 1px;
}

.pb-1 {
  padding-bottom: 1px;
}

.pl-1 {
  padding-inline-start: 1px;
}

.pr-1 {
  padding-inline-end: 1px;
}

.pt-2 {
  padding-top: 2px;
}

.pb-2 {
  padding-bottom: 2px;
}

.pl-2 {
  padding-inline-start: 2px;
}

.pr-2 {
  padding-inline-end: 2px;
}

.pt-3 {
  padding-top: 3px;
}

.pb-3 {
  padding-bottom: 3px;
}

.pl-3 {
  padding-inline-start: 3px;
}

.pr-3 {
  padding-inline-end: 3px;
}

.pt-4 {
  padding-top: 4px;
}

.pb-4 {
  padding-bottom: 4px;
}

.pl-4 {
  padding-inline-start: 4px;
}

.pr-4 {
  padding-inline-end: 4px;
}

.pt-5 {
  padding-top: 5px;
}

.pb-5 {
  padding-bottom: 5px;
}

.pl-5 {
  padding-inline-start: 5px;
}

.pr-5 {
  padding-inline-end: 5px;
}

.pt-6 {
  padding-top: 6px;
}

.pb-6 {
  padding-bottom: 6px;
}

.pl-6 {
  padding-inline-start: 6px;
}

.pr-6 {
  padding-inline-end: 6px;
}

.pt-7 {
  padding-top: 7px;
}

.pb-7 {
  padding-bottom: 7px;
}

.pl-7 {
  padding-inline-start: 7px;
}

.pr-7 {
  padding-inline-end: 7px;
}

.pt-8 {
  padding-top: 8px;
}

.pb-8 {
  padding-bottom: 8px;
}

.pl-8 {
  padding-inline-start: 8px;
}

.pr-8 {
  padding-inline-end: 8px;
}

.pt-9 {
  padding-top: 9px;
}

.pb-9 {
  padding-bottom: 9px;
}

.pl-9 {
  padding-inline-start: 9px;
}

.pr-9 {
  padding-inline-end: 9px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-inline-start: 10px;
}

.pr-10 {
  padding-inline-end: 10px;
}

.pt-11 {
  padding-top: 11px;
}

.pb-11 {
  padding-bottom: 11px;
}

.pl-11 {
  padding-inline-start: 11px;
}

.pr-11 {
  padding-inline-end: 11px;
}

.pt-12 {
  padding-top: 12px;
}

.pb-12 {
  padding-bottom: 12px;
}

.pl-12 {
  padding-inline-start: 12px;
}

.pr-12 {
  padding-inline-end: 12px;
}

.pt-13 {
  padding-top: 13px;
}

.pb-13 {
  padding-bottom: 13px;
}

.pl-13 {
  padding-inline-start: 13px;
}

.pr-13 {
  padding-inline-end: 13px;
}

.pt-14 {
  padding-top: 14px;
}

.pb-14 {
  padding-bottom: 14px;
}

.pl-14 {
  padding-inline-start: 14px;
}

.pr-14 {
  padding-inline-end: 14px;
}

.pt-15 {
  padding-top: 15px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pl-15 {
  padding-inline-start: 15px;
}

.pr-15 {
  padding-inline-end: 15px;
}

.pt-16 {
  padding-top: 16px;
}

.pb-16 {
  padding-bottom: 16px;
}

.pl-16 {
  padding-inline-start: 16px;
}

.pr-16 {
  padding-inline-end: 16px;
}

.pt-17 {
  padding-top: 17px;
}

.pb-17 {
  padding-bottom: 17px;
}

.pl-17 {
  padding-inline-start: 17px;
}

.pr-17 {
  padding-inline-end: 17px;
}

.pt-18 {
  padding-top: 18px;
}

.pb-18 {
  padding-bottom: 18px;
}

.pl-18 {
  padding-inline-start: 18px;
}

.pr-18 {
  padding-inline-end: 18px;
}

.pt-19 {
  padding-top: 19px;
}

.pb-19 {
  padding-bottom: 19px;
}

.pl-19 {
  padding-inline-start: 19px;
}

.pr-19 {
  padding-inline-end: 19px;
}

.pt-20 {
  padding-top: 20px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pl-20 {
  padding-inline-start: 20px;
}

.pr-20 {
  padding-inline-end: 20px;
}

.pt-21 {
  padding-top: 21px;
}

.pb-21 {
  padding-bottom: 21px;
}

.pl-21 {
  padding-inline-start: 21px;
}

.pr-21 {
  padding-inline-end: 21px;
}

.pt-22 {
  padding-top: 22px;
}

.pb-22 {
  padding-bottom: 22px;
}

.pl-22 {
  padding-inline-start: 22px;
}

.pr-22 {
  padding-inline-end: 22px;
}

.pt-23 {
  padding-top: 23px;
}

.pb-23 {
  padding-bottom: 23px;
}

.pl-23 {
  padding-inline-start: 23px;
}

.pr-23 {
  padding-inline-end: 23px;
}

.pt-24 {
  padding-top: 24px;
}

.pb-24 {
  padding-bottom: 24px;
}

.pl-24 {
  padding-inline-start: 24px;
}

.pr-24 {
  padding-inline-end: 24px;
}

.pt-25 {
  padding-top: 25px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pl-25 {
  padding-inline-start: 25px;
}

.pr-25 {
  padding-inline-end: 25px;
}

.pt-26 {
  padding-top: 26px;
}

.pb-26 {
  padding-bottom: 26px;
}

.pl-26 {
  padding-inline-start: 26px;
}

.pr-26 {
  padding-inline-end: 26px;
}

.pt-27 {
  padding-top: 27px;
}

.pb-27 {
  padding-bottom: 27px;
}

.pl-27 {
  padding-inline-start: 27px;
}

.pr-27 {
  padding-inline-end: 27px;
}

.pt-28 {
  padding-top: 28px;
}

.pb-28 {
  padding-bottom: 28px;
}

.pl-28 {
  padding-inline-start: 28px;
}

.pr-28 {
  padding-inline-end: 28px;
}

.pt-29 {
  padding-top: 29px;
}

.pb-29 {
  padding-bottom: 29px;
}

.pl-29 {
  padding-inline-start: 29px;
}

.pr-29 {
  padding-inline-end: 29px;
}

.pt-30 {
  padding-top: 30px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pl-30 {
  padding-inline-start: 30px;
}

.pr-30 {
  padding-inline-end: 30px;
}

.pt-31 {
  padding-top: 31px;
}

.pb-31 {
  padding-bottom: 31px;
}

.pl-31 {
  padding-inline-start: 31px;
}

.pr-31 {
  padding-inline-end: 31px;
}

.pt-32 {
  padding-top: 32px;
}

.pb-32 {
  padding-bottom: 32px;
}

.pl-32 {
  padding-inline-start: 32px;
}

.pr-32 {
  padding-inline-end: 32px;
}

.pt-33 {
  padding-top: 33px;
}

.pb-33 {
  padding-bottom: 33px;
}

.pl-33 {
  padding-inline-start: 33px;
}

.pr-33 {
  padding-inline-end: 33px;
}

.pt-34 {
  padding-top: 34px;
}

.pb-34 {
  padding-bottom: 34px;
}

.pl-34 {
  padding-inline-start: 34px;
}

.pr-34 {
  padding-inline-end: 34px;
}

.pt-35 {
  padding-top: 35px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pl-35 {
  padding-inline-start: 35px;
}

.pr-35 {
  padding-inline-end: 35px;
}

.pt-36 {
  padding-top: 36px;
}

.pb-36 {
  padding-bottom: 36px;
}

.pl-36 {
  padding-inline-start: 36px;
}

.pr-36 {
  padding-inline-end: 36px;
}

.pt-37 {
  padding-top: 37px;
}

.pb-37 {
  padding-bottom: 37px;
}

.pl-37 {
  padding-inline-start: 37px;
}

.pr-37 {
  padding-inline-end: 37px;
}

.pt-38 {
  padding-top: 38px;
}

.pb-38 {
  padding-bottom: 38px;
}

.pl-38 {
  padding-inline-start: 38px;
}

.pr-38 {
  padding-inline-end: 38px;
}

.pt-39 {
  padding-top: 39px;
}

.pb-39 {
  padding-bottom: 39px;
}

.pl-39 {
  padding-inline-start: 39px;
}

.pr-39 {
  padding-inline-end: 39px;
}

.pt-40 {
  padding-top: 40px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pl-40 {
  padding-inline-start: 40px;
}

.pr-40 {
  padding-inline-end: 40px;
}

.pt-41 {
  padding-top: 41px;
}

.pb-41 {
  padding-bottom: 41px;
}

.pl-41 {
  padding-inline-start: 41px;
}

.pr-41 {
  padding-inline-end: 41px;
}

.pt-42 {
  padding-top: 42px;
}

.pb-42 {
  padding-bottom: 42px;
}

.pl-42 {
  padding-inline-start: 42px;
}

.pr-42 {
  padding-inline-end: 42px;
}

.pt-43 {
  padding-top: 43px;
}

.pb-43 {
  padding-bottom: 43px;
}

.pl-43 {
  padding-inline-start: 43px;
}

.pr-43 {
  padding-inline-end: 43px;
}

.pt-44 {
  padding-top: 44px;
}

.pb-44 {
  padding-bottom: 44px;
}

.pl-44 {
  padding-inline-start: 44px;
}

.pr-44 {
  padding-inline-end: 44px;
}

.pt-45 {
  padding-top: 45px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pl-45 {
  padding-inline-start: 45px;
}

.pr-45 {
  padding-inline-end: 45px;
}

.pt-46 {
  padding-top: 46px;
}

.pb-46 {
  padding-bottom: 46px;
}

.pl-46 {
  padding-inline-start: 46px;
}

.pr-46 {
  padding-inline-end: 46px;
}

.pt-47 {
  padding-top: 47px;
}

.pb-47 {
  padding-bottom: 47px;
}

.pl-47 {
  padding-inline-start: 47px;
}

.pr-47 {
  padding-inline-end: 47px;
}

.pt-48 {
  padding-top: 48px;
}

.pb-48 {
  padding-bottom: 48px;
}

.pl-48 {
  padding-inline-start: 48px;
}

.pr-48 {
  padding-inline-end: 48px;
}

.pt-49 {
  padding-top: 49px;
}

.pb-49 {
  padding-bottom: 49px;
}

.pl-49 {
  padding-inline-start: 49px;
}

.pr-49 {
  padding-inline-end: 49px;
}

.pt-50 {
  padding-top: 50px;
}

.pb-50 {
  padding-bottom: 50px;
}

.pl-50 {
  padding-inline-start: 50px;
}

.pr-50 {
  padding-inline-end: 50px;
}

.pt-51 {
  padding-top: 51px;
}

.pb-51 {
  padding-bottom: 51px;
}

.pl-51 {
  padding-inline-start: 51px;
}

.pr-51 {
  padding-inline-end: 51px;
}

.pt-52 {
  padding-top: 52px;
}

.pb-52 {
  padding-bottom: 52px;
}

.pl-52 {
  padding-inline-start: 52px;
}

.pr-52 {
  padding-inline-end: 52px;
}

.pt-53 {
  padding-top: 53px;
}

.pb-53 {
  padding-bottom: 53px;
}

.pl-53 {
  padding-inline-start: 53px;
}

.pr-53 {
  padding-inline-end: 53px;
}

.pt-54 {
  padding-top: 54px;
}

.pb-54 {
  padding-bottom: 54px;
}

.pl-54 {
  padding-inline-start: 54px;
}

.pr-54 {
  padding-inline-end: 54px;
}

.pt-55 {
  padding-top: 55px;
}

.pb-55 {
  padding-bottom: 55px;
}

.pl-55 {
  padding-inline-start: 55px;
}

.pr-55 {
  padding-inline-end: 55px;
}

.pt-56 {
  padding-top: 56px;
}

.pb-56 {
  padding-bottom: 56px;
}

.pl-56 {
  padding-inline-start: 56px;
}

.pr-56 {
  padding-inline-end: 56px;
}

.pt-57 {
  padding-top: 57px;
}

.pb-57 {
  padding-bottom: 57px;
}

.pl-57 {
  padding-inline-start: 57px;
}

.pr-57 {
  padding-inline-end: 57px;
}

.pt-58 {
  padding-top: 58px;
}

.pb-58 {
  padding-bottom: 58px;
}

.pl-58 {
  padding-inline-start: 58px;
}

.pr-58 {
  padding-inline-end: 58px;
}

.pt-59 {
  padding-top: 59px;
}

.pb-59 {
  padding-bottom: 59px;
}

.pl-59 {
  padding-inline-start: 59px;
}

.pr-59 {
  padding-inline-end: 59px;
}

.pt-60 {
  padding-top: 60px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pl-60 {
  padding-inline-start: 60px;
}

.pr-60 {
  padding-inline-end: 60px;
}

.pt-61 {
  padding-top: 61px;
}

.pb-61 {
  padding-bottom: 61px;
}

.pl-61 {
  padding-inline-start: 61px;
}

.pr-61 {
  padding-inline-end: 61px;
}

.pt-62 {
  padding-top: 62px;
}

.pb-62 {
  padding-bottom: 62px;
}

.pl-62 {
  padding-inline-start: 62px;
}

.pr-62 {
  padding-inline-end: 62px;
}

.pt-63 {
  padding-top: 63px;
}

.pb-63 {
  padding-bottom: 63px;
}

.pl-63 {
  padding-inline-start: 63px;
}

.pr-63 {
  padding-inline-end: 63px;
}

.pt-64 {
  padding-top: 64px;
}

.pb-64 {
  padding-bottom: 64px;
}

.pl-64 {
  padding-inline-start: 64px;
}

.pr-64 {
  padding-inline-end: 64px;
}

.pt-65 {
  padding-top: 65px;
}

.pb-65 {
  padding-bottom: 65px;
}

.pl-65 {
  padding-inline-start: 65px;
}

.pr-65 {
  padding-inline-end: 65px;
}

.pt-66 {
  padding-top: 66px;
}

.pb-66 {
  padding-bottom: 66px;
}

.pl-66 {
  padding-inline-start: 66px;
}

.pr-66 {
  padding-inline-end: 66px;
}

.pt-67 {
  padding-top: 67px;
}

.pb-67 {
  padding-bottom: 67px;
}

.pl-67 {
  padding-inline-start: 67px;
}

.pr-67 {
  padding-inline-end: 67px;
}

.pt-68 {
  padding-top: 68px;
}

.pb-68 {
  padding-bottom: 68px;
}

.pl-68 {
  padding-inline-start: 68px;
}

.pr-68 {
  padding-inline-end: 68px;
}

.pt-69 {
  padding-top: 69px;
}

.pb-69 {
  padding-bottom: 69px;
}

.pl-69 {
  padding-inline-start: 69px;
}

.pr-69 {
  padding-inline-end: 69px;
}

.pt-70 {
  padding-top: 70px;
}

.pb-70 {
  padding-bottom: 70px;
}

.pl-70 {
  padding-inline-start: 70px;
}

.pr-70 {
  padding-inline-end: 70px;
}

.pt-71 {
  padding-top: 71px;
}

.pb-71 {
  padding-bottom: 71px;
}

.pl-71 {
  padding-inline-start: 71px;
}

.pr-71 {
  padding-inline-end: 71px;
}

.pt-72 {
  padding-top: 72px;
}

.pb-72 {
  padding-bottom: 72px;
}

.pl-72 {
  padding-inline-start: 72px;
}

.pr-72 {
  padding-inline-end: 72px;
}

.pt-73 {
  padding-top: 73px;
}

.pb-73 {
  padding-bottom: 73px;
}

.pl-73 {
  padding-inline-start: 73px;
}

.pr-73 {
  padding-inline-end: 73px;
}

.pt-74 {
  padding-top: 74px;
}

.pb-74 {
  padding-bottom: 74px;
}

.pl-74 {
  padding-inline-start: 74px;
}

.pr-74 {
  padding-inline-end: 74px;
}

.pt-75 {
  padding-top: 75px;
}

.pb-75 {
  padding-bottom: 75px;
}

.pl-75 {
  padding-inline-start: 75px;
}

.pr-75 {
  padding-inline-end: 75px;
}

.pt-76 {
  padding-top: 76px;
}

.pb-76 {
  padding-bottom: 76px;
}

.pl-76 {
  padding-inline-start: 76px;
}

.pr-76 {
  padding-inline-end: 76px;
}

.pt-77 {
  padding-top: 77px;
}

.pb-77 {
  padding-bottom: 77px;
}

.pl-77 {
  padding-inline-start: 77px;
}

.pr-77 {
  padding-inline-end: 77px;
}

.pt-78 {
  padding-top: 78px;
}

.pb-78 {
  padding-bottom: 78px;
}

.pl-78 {
  padding-inline-start: 78px;
}

.pr-78 {
  padding-inline-end: 78px;
}

.pt-79 {
  padding-top: 79px;
}

.pb-79 {
  padding-bottom: 79px;
}

.pl-79 {
  padding-inline-start: 79px;
}

.pr-79 {
  padding-inline-end: 79px;
}

.pt-80 {
  padding-top: 80px;
}

.pb-80 {
  padding-bottom: 80px;
}

.pl-80 {
  padding-inline-start: 80px;
}

.pr-80 {
  padding-inline-end: 80px;
}

.pt-81 {
  padding-top: 81px;
}

.pb-81 {
  padding-bottom: 81px;
}

.pl-81 {
  padding-inline-start: 81px;
}

.pr-81 {
  padding-inline-end: 81px;
}

.pt-82 {
  padding-top: 82px;
}

.pb-82 {
  padding-bottom: 82px;
}

.pl-82 {
  padding-inline-start: 82px;
}

.pr-82 {
  padding-inline-end: 82px;
}

.pt-83 {
  padding-top: 83px;
}

.pb-83 {
  padding-bottom: 83px;
}

.pl-83 {
  padding-inline-start: 83px;
}

.pr-83 {
  padding-inline-end: 83px;
}

.pt-84 {
  padding-top: 84px;
}

.pb-84 {
  padding-bottom: 84px;
}

.pl-84 {
  padding-inline-start: 84px;
}

.pr-84 {
  padding-inline-end: 84px;
}

.pt-85 {
  padding-top: 85px;
}

.pb-85 {
  padding-bottom: 85px;
}

.pl-85 {
  padding-inline-start: 85px;
}

.pr-85 {
  padding-inline-end: 85px;
}

.pt-86 {
  padding-top: 86px;
}

.pb-86 {
  padding-bottom: 86px;
}

.pl-86 {
  padding-inline-start: 86px;
}

.pr-86 {
  padding-inline-end: 86px;
}

.pt-87 {
  padding-top: 87px;
}

.pb-87 {
  padding-bottom: 87px;
}

.pl-87 {
  padding-inline-start: 87px;
}

.pr-87 {
  padding-inline-end: 87px;
}

.pt-88 {
  padding-top: 88px;
}

.pb-88 {
  padding-bottom: 88px;
}

.pl-88 {
  padding-inline-start: 88px;
}

.pr-88 {
  padding-inline-end: 88px;
}

.pt-89 {
  padding-top: 89px;
}

.pb-89 {
  padding-bottom: 89px;
}

.pl-89 {
  padding-inline-start: 89px;
}

.pr-89 {
  padding-inline-end: 89px;
}

.pt-90 {
  padding-top: 90px;
}

.pb-90 {
  padding-bottom: 90px;
}

.pl-90 {
  padding-inline-start: 90px;
}

.pr-90 {
  padding-inline-end: 90px;
}

.pt-91 {
  padding-top: 91px;
}

.pb-91 {
  padding-bottom: 91px;
}

.pl-91 {
  padding-inline-start: 91px;
}

.pr-91 {
  padding-inline-end: 91px;
}

.pt-92 {
  padding-top: 92px;
}

.pb-92 {
  padding-bottom: 92px;
}

.pl-92 {
  padding-inline-start: 92px;
}

.pr-92 {
  padding-inline-end: 92px;
}

.pt-93 {
  padding-top: 93px;
}

.pb-93 {
  padding-bottom: 93px;
}

.pl-93 {
  padding-inline-start: 93px;
}

.pr-93 {
  padding-inline-end: 93px;
}

.pt-94 {
  padding-top: 94px;
}

.pb-94 {
  padding-bottom: 94px;
}

.pl-94 {
  padding-inline-start: 94px;
}

.pr-94 {
  padding-inline-end: 94px;
}

.pt-95 {
  padding-top: 95px;
}

.pb-95 {
  padding-bottom: 95px;
}

.pl-95 {
  padding-inline-start: 95px;
}

.pr-95 {
  padding-inline-end: 95px;
}

.pt-96 {
  padding-top: 96px;
}

.pb-96 {
  padding-bottom: 96px;
}

.pl-96 {
  padding-inline-start: 96px;
}

.pr-96 {
  padding-inline-end: 96px;
}

.pt-97 {
  padding-top: 97px;
}

.pb-97 {
  padding-bottom: 97px;
}

.pl-97 {
  padding-inline-start: 97px;
}

.pr-97 {
  padding-inline-end: 97px;
}

.pt-98 {
  padding-top: 98px;
}

.pb-98 {
  padding-bottom: 98px;
}

.pl-98 {
  padding-inline-start: 98px;
}

.pr-98 {
  padding-inline-end: 98px;
}

.pt-99 {
  padding-top: 99px;
}

.pb-99 {
  padding-bottom: 99px;
}

.pl-99 {
  padding-inline-start: 99px;
}

.pr-99 {
  padding-inline-end: 99px;
}

.pt-100 {
  padding-top: 100px;
}

.pb-100 {
  padding-bottom: 100px;
}

.pl-100 {
  padding-inline-start: 100px;
}

.pr-100 {
  padding-inline-end: 100px;
}

.pt-101 {
  padding-top: 101px;
}

.pb-101 {
  padding-bottom: 101px;
}

.pl-101 {
  padding-inline-start: 101px;
}

.pr-101 {
  padding-inline-end: 101px;
}

.pt-102 {
  padding-top: 102px;
}

.pb-102 {
  padding-bottom: 102px;
}

.pl-102 {
  padding-inline-start: 102px;
}

.pr-102 {
  padding-inline-end: 102px;
}

.pt-103 {
  padding-top: 103px;
}

.pb-103 {
  padding-bottom: 103px;
}

.pl-103 {
  padding-inline-start: 103px;
}

.pr-103 {
  padding-inline-end: 103px;
}

.pt-104 {
  padding-top: 104px;
}

.pb-104 {
  padding-bottom: 104px;
}

.pl-104 {
  padding-inline-start: 104px;
}

.pr-104 {
  padding-inline-end: 104px;
}

.pt-105 {
  padding-top: 105px;
}

.pb-105 {
  padding-bottom: 105px;
}

.pl-105 {
  padding-inline-start: 105px;
}

.pr-105 {
  padding-inline-end: 105px;
}

.pt-106 {
  padding-top: 106px;
}

.pb-106 {
  padding-bottom: 106px;
}

.pl-106 {
  padding-inline-start: 106px;
}

.pr-106 {
  padding-inline-end: 106px;
}

.pt-107 {
  padding-top: 107px;
}

.pb-107 {
  padding-bottom: 107px;
}

.pl-107 {
  padding-inline-start: 107px;
}

.pr-107 {
  padding-inline-end: 107px;
}

.pt-108 {
  padding-top: 108px;
}

.pb-108 {
  padding-bottom: 108px;
}

.pl-108 {
  padding-inline-start: 108px;
}

.pr-108 {
  padding-inline-end: 108px;
}

.pt-109 {
  padding-top: 109px;
}

.pb-109 {
  padding-bottom: 109px;
}

.pl-109 {
  padding-inline-start: 109px;
}

.pr-109 {
  padding-inline-end: 109px;
}

.pt-110 {
  padding-top: 110px;
}

.pb-110 {
  padding-bottom: 110px;
}

.pl-110 {
  padding-inline-start: 110px;
}

.pr-110 {
  padding-inline-end: 110px;
}

.pt-111 {
  padding-top: 111px;
}

.pb-111 {
  padding-bottom: 111px;
}

.pl-111 {
  padding-inline-start: 111px;
}

.pr-111 {
  padding-inline-end: 111px;
}

.pt-112 {
  padding-top: 112px;
}

.pb-112 {
  padding-bottom: 112px;
}

.pl-112 {
  padding-inline-start: 112px;
}

.pr-112 {
  padding-inline-end: 112px;
}

.pt-113 {
  padding-top: 113px;
}

.pb-113 {
  padding-bottom: 113px;
}

.pl-113 {
  padding-inline-start: 113px;
}

.pr-113 {
  padding-inline-end: 113px;
}

.pt-114 {
  padding-top: 114px;
}

.pb-114 {
  padding-bottom: 114px;
}

.pl-114 {
  padding-inline-start: 114px;
}

.pr-114 {
  padding-inline-end: 114px;
}

.pt-115 {
  padding-top: 115px;
}

.pb-115 {
  padding-bottom: 115px;
}

.pl-115 {
  padding-inline-start: 115px;
}

.pr-115 {
  padding-inline-end: 115px;
}

.pt-116 {
  padding-top: 116px;
}

.pb-116 {
  padding-bottom: 116px;
}

.pl-116 {
  padding-inline-start: 116px;
}

.pr-116 {
  padding-inline-end: 116px;
}

.pt-117 {
  padding-top: 117px;
}

.pb-117 {
  padding-bottom: 117px;
}

.pl-117 {
  padding-inline-start: 117px;
}

.pr-117 {
  padding-inline-end: 117px;
}

.pt-118 {
  padding-top: 118px;
}

.pb-118 {
  padding-bottom: 118px;
}

.pl-118 {
  padding-inline-start: 118px;
}

.pr-118 {
  padding-inline-end: 118px;
}

.pt-119 {
  padding-top: 119px;
}

.pb-119 {
  padding-bottom: 119px;
}

.pl-119 {
  padding-inline-start: 119px;
}

.pr-119 {
  padding-inline-end: 119px;
}

.pt-120 {
  padding-top: 120px;
}

.pb-120 {
  padding-bottom: 120px;
}

.pl-120 {
  padding-inline-start: 120px;
}

.pr-120 {
  padding-inline-end: 120px;
}

.pt-121 {
  padding-top: 121px;
}

.pb-121 {
  padding-bottom: 121px;
}

.pl-121 {
  padding-inline-start: 121px;
}

.pr-121 {
  padding-inline-end: 121px;
}

.pt-122 {
  padding-top: 122px;
}

.pb-122 {
  padding-bottom: 122px;
}

.pl-122 {
  padding-inline-start: 122px;
}

.pr-122 {
  padding-inline-end: 122px;
}

.pt-123 {
  padding-top: 123px;
}

.pb-123 {
  padding-bottom: 123px;
}

.pl-123 {
  padding-inline-start: 123px;
}

.pr-123 {
  padding-inline-end: 123px;
}

.pt-124 {
  padding-top: 124px;
}

.pb-124 {
  padding-bottom: 124px;
}

.pl-124 {
  padding-inline-start: 124px;
}

.pr-124 {
  padding-inline-end: 124px;
}

.pt-125 {
  padding-top: 125px;
}

.pb-125 {
  padding-bottom: 125px;
}

.pl-125 {
  padding-inline-start: 125px;
}

.pr-125 {
  padding-inline-end: 125px;
}

.pt-126 {
  padding-top: 126px;
}

.pb-126 {
  padding-bottom: 126px;
}

.pl-126 {
  padding-inline-start: 126px;
}

.pr-126 {
  padding-inline-end: 126px;
}

.pt-127 {
  padding-top: 127px;
}

.pb-127 {
  padding-bottom: 127px;
}

.pl-127 {
  padding-inline-start: 127px;
}

.pr-127 {
  padding-inline-end: 127px;
}

.pt-128 {
  padding-top: 128px;
}

.pb-128 {
  padding-bottom: 128px;
}

.pl-128 {
  padding-inline-start: 128px;
}

.pr-128 {
  padding-inline-end: 128px;
}

.pt-129 {
  padding-top: 129px;
}

.pb-129 {
  padding-bottom: 129px;
}

.pl-129 {
  padding-inline-start: 129px;
}

.pr-129 {
  padding-inline-end: 129px;
}

.pt-130 {
  padding-top: 130px;
}

.pb-130 {
  padding-bottom: 130px;
}

.pl-130 {
  padding-inline-start: 130px;
}

.pr-130 {
  padding-inline-end: 130px;
}

.pt-131 {
  padding-top: 131px;
}

.pb-131 {
  padding-bottom: 131px;
}

.pl-131 {
  padding-inline-start: 131px;
}

.pr-131 {
  padding-inline-end: 131px;
}

.pt-132 {
  padding-top: 132px;
}

.pb-132 {
  padding-bottom: 132px;
}

.pl-132 {
  padding-inline-start: 132px;
}

.pr-132 {
  padding-inline-end: 132px;
}

.pt-133 {
  padding-top: 133px;
}

.pb-133 {
  padding-bottom: 133px;
}

.pl-133 {
  padding-inline-start: 133px;
}

.pr-133 {
  padding-inline-end: 133px;
}

.pt-134 {
  padding-top: 134px;
}

.pb-134 {
  padding-bottom: 134px;
}

.pl-134 {
  padding-inline-start: 134px;
}

.pr-134 {
  padding-inline-end: 134px;
}

.pt-135 {
  padding-top: 135px;
}

.pb-135 {
  padding-bottom: 135px;
}

.pl-135 {
  padding-inline-start: 135px;
}

.pr-135 {
  padding-inline-end: 135px;
}

.pt-136 {
  padding-top: 136px;
}

.pb-136 {
  padding-bottom: 136px;
}

.pl-136 {
  padding-inline-start: 136px;
}

.pr-136 {
  padding-inline-end: 136px;
}

.pt-137 {
  padding-top: 137px;
}

.pb-137 {
  padding-bottom: 137px;
}

.pl-137 {
  padding-inline-start: 137px;
}

.pr-137 {
  padding-inline-end: 137px;
}

.pt-138 {
  padding-top: 138px;
}

.pb-138 {
  padding-bottom: 138px;
}

.pl-138 {
  padding-inline-start: 138px;
}

.pr-138 {
  padding-inline-end: 138px;
}

.pt-139 {
  padding-top: 139px;
}

.pb-139 {
  padding-bottom: 139px;
}

.pl-139 {
  padding-inline-start: 139px;
}

.pr-139 {
  padding-inline-end: 139px;
}

.pt-140 {
  padding-top: 140px;
}

.pb-140 {
  padding-bottom: 140px;
}

.pl-140 {
  padding-inline-start: 140px;
}

.pr-140 {
  padding-inline-end: 140px;
}

.pt-141 {
  padding-top: 141px;
}

.pb-141 {
  padding-bottom: 141px;
}

.pl-141 {
  padding-inline-start: 141px;
}

.pr-141 {
  padding-inline-end: 141px;
}

.pt-142 {
  padding-top: 142px;
}

.pb-142 {
  padding-bottom: 142px;
}

.pl-142 {
  padding-inline-start: 142px;
}

.pr-142 {
  padding-inline-end: 142px;
}

.pt-143 {
  padding-top: 143px;
}

.pb-143 {
  padding-bottom: 143px;
}

.pl-143 {
  padding-inline-start: 143px;
}

.pr-143 {
  padding-inline-end: 143px;
}

.pt-144 {
  padding-top: 144px;
}

.pb-144 {
  padding-bottom: 144px;
}

.pl-144 {
  padding-inline-start: 144px;
}

.pr-144 {
  padding-inline-end: 144px;
}

.pt-145 {
  padding-top: 145px;
}

.pb-145 {
  padding-bottom: 145px;
}

.pl-145 {
  padding-inline-start: 145px;
}

.pr-145 {
  padding-inline-end: 145px;
}

.pt-146 {
  padding-top: 146px;
}

.pb-146 {
  padding-bottom: 146px;
}

.pl-146 {
  padding-inline-start: 146px;
}

.pr-146 {
  padding-inline-end: 146px;
}

.pt-147 {
  padding-top: 147px;
}

.pb-147 {
  padding-bottom: 147px;
}

.pl-147 {
  padding-inline-start: 147px;
}

.pr-147 {
  padding-inline-end: 147px;
}

.pt-148 {
  padding-top: 148px;
}

.pb-148 {
  padding-bottom: 148px;
}

.pl-148 {
  padding-inline-start: 148px;
}

.pr-148 {
  padding-inline-end: 148px;
}

.pt-149 {
  padding-top: 149px;
}

.pb-149 {
  padding-bottom: 149px;
}

.pl-149 {
  padding-inline-start: 149px;
}

.pr-149 {
  padding-inline-end: 149px;
}

.pt-150 {
  padding-top: 150px;
}

.pb-150 {
  padding-bottom: 150px;
}

.pl-150 {
  padding-inline-start: 150px;
}

.pr-150 {
  padding-inline-end: 150px;
}

.pt-151 {
  padding-top: 151px;
}

.pb-151 {
  padding-bottom: 151px;
}

.pl-151 {
  padding-inline-start: 151px;
}

.pr-151 {
  padding-inline-end: 151px;
}

.pt-152 {
  padding-top: 152px;
}

.pb-152 {
  padding-bottom: 152px;
}

.pl-152 {
  padding-inline-start: 152px;
}

.pr-152 {
  padding-inline-end: 152px;
}

.pt-153 {
  padding-top: 153px;
}

.pb-153 {
  padding-bottom: 153px;
}

.pl-153 {
  padding-inline-start: 153px;
}

.pr-153 {
  padding-inline-end: 153px;
}

.pt-154 {
  padding-top: 154px;
}

.pb-154 {
  padding-bottom: 154px;
}

.pl-154 {
  padding-inline-start: 154px;
}

.pr-154 {
  padding-inline-end: 154px;
}

.pt-155 {
  padding-top: 155px;
}

.pb-155 {
  padding-bottom: 155px;
}

.pl-155 {
  padding-inline-start: 155px;
}

.pr-155 {
  padding-inline-end: 155px;
}

.pt-156 {
  padding-top: 156px;
}

.pb-156 {
  padding-bottom: 156px;
}

.pl-156 {
  padding-inline-start: 156px;
}

.pr-156 {
  padding-inline-end: 156px;
}

.pt-157 {
  padding-top: 157px;
}

.pb-157 {
  padding-bottom: 157px;
}

.pl-157 {
  padding-inline-start: 157px;
}

.pr-157 {
  padding-inline-end: 157px;
}

.pt-158 {
  padding-top: 158px;
}

.pb-158 {
  padding-bottom: 158px;
}

.pl-158 {
  padding-inline-start: 158px;
}

.pr-158 {
  padding-inline-end: 158px;
}

.pt-159 {
  padding-top: 159px;
}

.pb-159 {
  padding-bottom: 159px;
}

.pl-159 {
  padding-inline-start: 159px;
}

.pr-159 {
  padding-inline-end: 159px;
}

.pt-160 {
  padding-top: 160px;
}

.pb-160 {
  padding-bottom: 160px;
}

.pl-160 {
  padding-inline-start: 160px;
}

.pr-160 {
  padding-inline-end: 160px;
}

.pt-161 {
  padding-top: 161px;
}

.pb-161 {
  padding-bottom: 161px;
}

.pl-161 {
  padding-inline-start: 161px;
}

.pr-161 {
  padding-inline-end: 161px;
}

.pt-162 {
  padding-top: 162px;
}

.pb-162 {
  padding-bottom: 162px;
}

.pl-162 {
  padding-inline-start: 162px;
}

.pr-162 {
  padding-inline-end: 162px;
}

.pt-163 {
  padding-top: 163px;
}

.pb-163 {
  padding-bottom: 163px;
}

.pl-163 {
  padding-inline-start: 163px;
}

.pr-163 {
  padding-inline-end: 163px;
}

.pt-164 {
  padding-top: 164px;
}

.pb-164 {
  padding-bottom: 164px;
}

.pl-164 {
  padding-inline-start: 164px;
}

.pr-164 {
  padding-inline-end: 164px;
}

.pt-165 {
  padding-top: 165px;
}

.pb-165 {
  padding-bottom: 165px;
}

.pl-165 {
  padding-inline-start: 165px;
}

.pr-165 {
  padding-inline-end: 165px;
}

.pt-166 {
  padding-top: 166px;
}

.pb-166 {
  padding-bottom: 166px;
}

.pl-166 {
  padding-inline-start: 166px;
}

.pr-166 {
  padding-inline-end: 166px;
}

.pt-167 {
  padding-top: 167px;
}

.pb-167 {
  padding-bottom: 167px;
}

.pl-167 {
  padding-inline-start: 167px;
}

.pr-167 {
  padding-inline-end: 167px;
}

.pt-168 {
  padding-top: 168px;
}

.pb-168 {
  padding-bottom: 168px;
}

.pl-168 {
  padding-inline-start: 168px;
}

.pr-168 {
  padding-inline-end: 168px;
}

.pt-169 {
  padding-top: 169px;
}

.pb-169 {
  padding-bottom: 169px;
}

.pl-169 {
  padding-inline-start: 169px;
}

.pr-169 {
  padding-inline-end: 169px;
}

.pt-170 {
  padding-top: 170px;
}

.pb-170 {
  padding-bottom: 170px;
}

.pl-170 {
  padding-inline-start: 170px;
}

.pr-170 {
  padding-inline-end: 170px;
}

.pt-171 {
  padding-top: 171px;
}

.pb-171 {
  padding-bottom: 171px;
}

.pl-171 {
  padding-inline-start: 171px;
}

.pr-171 {
  padding-inline-end: 171px;
}

.pt-172 {
  padding-top: 172px;
}

.pb-172 {
  padding-bottom: 172px;
}

.pl-172 {
  padding-inline-start: 172px;
}

.pr-172 {
  padding-inline-end: 172px;
}

.pt-173 {
  padding-top: 173px;
}

.pb-173 {
  padding-bottom: 173px;
}

.pl-173 {
  padding-inline-start: 173px;
}

.pr-173 {
  padding-inline-end: 173px;
}

.pt-174 {
  padding-top: 174px;
}

.pb-174 {
  padding-bottom: 174px;
}

.pl-174 {
  padding-inline-start: 174px;
}

.pr-174 {
  padding-inline-end: 174px;
}

.pt-175 {
  padding-top: 175px;
}

.pb-175 {
  padding-bottom: 175px;
}

.pl-175 {
  padding-inline-start: 175px;
}

.pr-175 {
  padding-inline-end: 175px;
}

.pt-176 {
  padding-top: 176px;
}

.pb-176 {
  padding-bottom: 176px;
}

.pl-176 {
  padding-inline-start: 176px;
}

.pr-176 {
  padding-inline-end: 176px;
}

.pt-177 {
  padding-top: 177px;
}

.pb-177 {
  padding-bottom: 177px;
}

.pl-177 {
  padding-inline-start: 177px;
}

.pr-177 {
  padding-inline-end: 177px;
}

.pt-178 {
  padding-top: 178px;
}

.pb-178 {
  padding-bottom: 178px;
}

.pl-178 {
  padding-inline-start: 178px;
}

.pr-178 {
  padding-inline-end: 178px;
}

.pt-179 {
  padding-top: 179px;
}

.pb-179 {
  padding-bottom: 179px;
}

.pl-179 {
  padding-inline-start: 179px;
}

.pr-179 {
  padding-inline-end: 179px;
}

.pt-180 {
  padding-top: 180px;
}

.pb-180 {
  padding-bottom: 180px;
}

.pl-180 {
  padding-inline-start: 180px;
}

.pr-180 {
  padding-inline-end: 180px;
}

.pt-181 {
  padding-top: 181px;
}

.pb-181 {
  padding-bottom: 181px;
}

.pl-181 {
  padding-inline-start: 181px;
}

.pr-181 {
  padding-inline-end: 181px;
}

.pt-182 {
  padding-top: 182px;
}

.pb-182 {
  padding-bottom: 182px;
}

.pl-182 {
  padding-inline-start: 182px;
}

.pr-182 {
  padding-inline-end: 182px;
}

.pt-183 {
  padding-top: 183px;
}

.pb-183 {
  padding-bottom: 183px;
}

.pl-183 {
  padding-inline-start: 183px;
}

.pr-183 {
  padding-inline-end: 183px;
}

.pt-184 {
  padding-top: 184px;
}

.pb-184 {
  padding-bottom: 184px;
}

.pl-184 {
  padding-inline-start: 184px;
}

.pr-184 {
  padding-inline-end: 184px;
}

.pt-185 {
  padding-top: 185px;
}

.pb-185 {
  padding-bottom: 185px;
}

.pl-185 {
  padding-inline-start: 185px;
}

.pr-185 {
  padding-inline-end: 185px;
}

.pt-186 {
  padding-top: 186px;
}

.pb-186 {
  padding-bottom: 186px;
}

.pl-186 {
  padding-inline-start: 186px;
}

.pr-186 {
  padding-inline-end: 186px;
}

.pt-187 {
  padding-top: 187px;
}

.pb-187 {
  padding-bottom: 187px;
}

.pl-187 {
  padding-inline-start: 187px;
}

.pr-187 {
  padding-inline-end: 187px;
}

.pt-188 {
  padding-top: 188px;
}

.pb-188 {
  padding-bottom: 188px;
}

.pl-188 {
  padding-inline-start: 188px;
}

.pr-188 {
  padding-inline-end: 188px;
}

.pt-189 {
  padding-top: 189px;
}

.pb-189 {
  padding-bottom: 189px;
}

.pl-189 {
  padding-inline-start: 189px;
}

.pr-189 {
  padding-inline-end: 189px;
}

.pt-190 {
  padding-top: 190px;
}

.pb-190 {
  padding-bottom: 190px;
}

.pl-190 {
  padding-inline-start: 190px;
}

.pr-190 {
  padding-inline-end: 190px;
}

.pt-191 {
  padding-top: 191px;
}

.pb-191 {
  padding-bottom: 191px;
}

.pl-191 {
  padding-inline-start: 191px;
}

.pr-191 {
  padding-inline-end: 191px;
}

.pt-192 {
  padding-top: 192px;
}

.pb-192 {
  padding-bottom: 192px;
}

.pl-192 {
  padding-inline-start: 192px;
}

.pr-192 {
  padding-inline-end: 192px;
}

.pt-193 {
  padding-top: 193px;
}

.pb-193 {
  padding-bottom: 193px;
}

.pl-193 {
  padding-inline-start: 193px;
}

.pr-193 {
  padding-inline-end: 193px;
}

.pt-194 {
  padding-top: 194px;
}

.pb-194 {
  padding-bottom: 194px;
}

.pl-194 {
  padding-inline-start: 194px;
}

.pr-194 {
  padding-inline-end: 194px;
}

.pt-195 {
  padding-top: 195px;
}

.pb-195 {
  padding-bottom: 195px;
}

.pl-195 {
  padding-inline-start: 195px;
}

.pr-195 {
  padding-inline-end: 195px;
}

.pt-196 {
  padding-top: 196px;
}

.pb-196 {
  padding-bottom: 196px;
}

.pl-196 {
  padding-inline-start: 196px;
}

.pr-196 {
  padding-inline-end: 196px;
}

.pt-197 {
  padding-top: 197px;
}

.pb-197 {
  padding-bottom: 197px;
}

.pl-197 {
  padding-inline-start: 197px;
}

.pr-197 {
  padding-inline-end: 197px;
}

.pt-198 {
  padding-top: 198px;
}

.pb-198 {
  padding-bottom: 198px;
}

.pl-198 {
  padding-inline-start: 198px;
}

.pr-198 {
  padding-inline-end: 198px;
}

.pt-199 {
  padding-top: 199px;
}

.pb-199 {
  padding-bottom: 199px;
}

.pl-199 {
  padding-inline-start: 199px;
}

.pr-199 {
  padding-inline-end: 199px;
}

.pt-200 {
  padding-top: 200px;
}

.pb-200 {
  padding-bottom: 200px;
}

.pl-200 {
  padding-inline-start: 200px;
}

.pr-200 {
  padding-inline-end: 200px;
}

:root {
  --td-ff-body: "Outfit", sans-serif;
  --td-ff-heading: "Bai Jamjuree", sans-serif;
  --td-ff-fontawesome: "Font Awesome 6 Pro";
  --td-white: #ffffff;
  --td-black: #000000;
  --td-placeholder: hsla(0, 0%, 0%, 0.5);
  --td-selection: hsl(0, 0%, 0%);
  --td-body: #010C1A;
  --td-heading: #222223;
  --td-primary: #FB405A;
  --td-deep-black: #1A1D1F;
  --td-text-primary: #47494E;
  --td-border-primary: #eaeaea;
  --td-warning: #FFA336;
  --td-success: #03A66D;
  --td-danger: #FB405A;
  --td-danger-alt: #eb4e5c;
  --td-green: #03A66D;
  --td-info: #2E87E8;
  --td-eerie-black: #091628;
  --td-dark-gunmetal: #142032;
  --td-chaos-black: #0F0F0F;
  --td-gradient-1: #C340C0;
  --td-gradient-2: #FB405A;
  --td-gradient-3: #F7A34A;
  --color-primary-gradient-start: #4776E6;
  --color-primary-gradient-end: #8E54E9;
  --td-fw-normal: normal;
  --td-fw-thin: 100;
  --td-fw-elight: 200;
  --td-fw-light: 300;
  --td-fw-regular: 400;
  --td-fw-medium: 500;
  --td-fw-sbold: 600;
  --td-fw-bold: 700;
  --td-fw-ebold: 800;
  --td-fw-black: 900;
  --td-fs-body: 16px;
  --td-fs-p: 16px;
  --td-fs-h1: 52px;
  --td-fs-h2: 42px;
  --td-fs-h3: 32px;
  --td-fs-h4: 24px;
  --td-fs-h5: 20px;
  --td-fs-h6: 16px;
}

:root .dark-theme {
  --td-heading: #fff;
  --td-text-primary: #9A9DA7;
}

/*----------------------------------------*/
/* Default Spacing
/*----------------------------------------*/
.section_space {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem);
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space_medium {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem);
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space_small {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 3.125rem);
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 3.125rem);
}

.section_space_top {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 3.125rem);
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 3.125rem);
}

.section_space_bottom {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 3.125rem);
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 3.125rem);
}

.section_space-top {
  padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space-bottom {
  padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_title_space {
  margin-bottom: 48px;
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .section_title_space {
    margin-bottom: 44px;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .section_title_space {
    margin-bottom: 38px;
  }
}

/*----------------------------------------*/
/* Common Classes
/*----------------------------------------*/
.w-img img {
  width: 100%;
}

.m-img img {
  max-width: 100%;
}

.fix {
  overflow: hidden;
}

.o-x-clip {
  overflow-x: clip;
}

.clear {
  clear: both;
}

.f-left {
  float: left;
}

.f-right {
  float: right;
}

.zi-1 {
  z-index: 1;
}

.zi-11 {
  z-index: 11;
}

.p-relative {
  position: relative;
}

.p-absolute {
  position: absolute;
}

.position-absolute {
  position: absolute;
}

.include-bg {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.hr-1 {
  border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
  overflow-x: clip;
}

.o-visible {
  overflow: visible;
}

.valign {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
}

/*----------------------------------------*/
/* Typography css
/*----------------------------------------*/
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

body {
  font-family: var(--td-ff-body);
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--td-text-primary);
  background-color: var(--td-white);
}
body.landing-page-one {
  background: #020A22;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--td-ff-heading);
  color: var(--td-heading);
  margin-top: 0px;
  line-height: 1.3;
  margin-bottom: 0;
  word-break: break-word;
}

h1,
.h1 {
  font-size: clamp(2.5rem, 4vw + 1rem, 2.986rem);
  /* ~47.78pt */
  line-height: 1.2;
  font-weight: 700;
}

h2,
.h2 {
  font-size: clamp(2rem, 3.5vw + 0.8rem, 2.488rem);
  /* ~38.22pt */
  line-height: 1.2;
  font-weight: 700;
}

h3,
.h3 {
  font-size: clamp(1.75rem, 3vw + 0.5rem, 2.074rem);
  /* ~30.56pt */
  line-height: 1.3;
  font-weight: 700;
}

h4,
.h4 {
  font-size: clamp(1.5rem, 2.5vw + 0.3rem, 1.728rem);
  /* ~24.44pt */
  line-height: 1.4;
  font-weight: 600;
}

h5,
.h5 {
  font-size: clamp(1.25rem, 2vw + 0.2rem, 1.44rem);
  /* ~19.56pt */
  line-height: 1.5;
  font-weight: 600;
}

h6,
.h6 {
  font-size: clamp(1.1rem, 1.5vw + 0.1rem, 1.2rem);
  /* ~16pt */
  line-height: 1.56;
  font-weight: 500;
}

ul {
  margin: 0px;
  padding: 0px;
}

p {
  font-size: 1rem;
  line-height: 1.5;
}
p.b1 {
  font-size: clamp(0.8125rem, 0.25vw + 0.7rem, 0.875rem);
  /* 13px–14px */
  line-height: 1.4;
}
p.b2 {
  font-size: clamp(0.875rem, 0.4vw + 0.7rem, 1rem);
  /* 14px–16px */
  line-height: 1.5;
}
p.b3 {
  font-size: clamp(1rem, 0.5vw + 0.8rem, 1.125rem);
  /* 16px–18px */
  line-height: 1.625;
}
p.b4 {
  font-size: clamp(1.125rem, 0.6vw + 0.8rem, 1.25rem);
  /* 18px–20px */
  line-height: 1.754;
}
p:last-child {
  margin-bottom: 0px;
}

a {
  text-decoration: none;
}

a,
.btn,
button,
img,
.transition-3,
h1,
h2,
h3,
h4,
h5,
h6 {
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}

a:focus,
.button:focus {
  text-decoration: none;
  outline: none;
}

a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
}

a,
button {
  color: inherit;
  outline: none;
  border: none;
  background: transparent;
}

img {
  max-width: 100%;
  object-fit: cover;
}

button:hover {
  cursor: pointer;
}

button:focus {
  outline: 0;
}

.uppercase {
  text-transform: uppercase;
}

.capitalize {
  text-transform: capitalize;
}

hr:not([size]) {
  border-color: rgba(0, 0, 0, 0.1);
  opacity: 1;
  border-width: 1px;
}

*::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::-moz-selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

::selection {
  background: var(--td-black);
  color: var(--td-white);
  text-shadow: none;
}

*::-moz-placeholder {
  opacity: 1;
  font-size: 14px;
}

*::placeholder {
  opacity: 1;
  font-size: 14px;
  font-weight: 400;
}

/*----------------------------------------
  Bootstrap customize
-----------------------------------------*/
.container,
.container-fluid,
.container-lg,
.container-md,
.container-sm,
.container-xl,
.container-xxl {
  --bs-gutter-x: 30px;
}

@media (min-width: 1601px) {
  .container,
  .container-lg,
  .container-md,
  .container-sm,
  .container-xl,
  .container-xxl {
    max-width: 1350px;
  }
}
.row {
  --bs-gutter-x: 30px;
}

.g-0,
.gx-0 {
  --bs-gutter-x: ;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0 ;
}

.g-10,
.gx-10 {
  --bs-gutter-x: 10px;
}

.gy-12 {
  --bs-gutter-y: 12px;
}

.gy-14 {
  --bs-gutter-y: 14px;
}

.gx-14 {
  --bs-gutter-x: 14px;
}

.g-1,
.gx-1 {
  --bs-gutter-x: 0.25rem;
}

.g-2,
.gx-2 {
  --bs-gutter-x: 0.5rem;
}

.g-3,
.gx-3 {
  --bs-gutter-x: 1rem;
}

.g-20 {
  --bs-gutter-x: 20px;
  --bs-gutter-y: 20px;
}

.gy-20 {
  --bs-gutter-y: 20px;
}

.gx-20 {
  --bs-gutter-x: 20px;
}

.gy-20 {
  --bs-gutter-y: 20px;
}

.gx-24 {
  --bs-gutter-x: 24px;
}

.gy-24 {
  --bs-gutter-y: 24px;
}

.g-30,
.gx-30 {
  --bs-gutter-x: 30px;
}

.g-30,
.gy-30 {
  --bs-gutter-y: 30px;
}

.g-40,
.gx-40 {
  --bs-gutter-x: 40px;
}

.g-40,
.gy-40 {
  --bs-gutter-y: 40px;
}

.g-50,
.gx-50 {
  --bs-gutter-x: 50px;
}

.g-50,
.gy-50 {
  --bs-gutter-y: 50px;
}

.g-60,
.gy-60 {
  --bs-gutter-y: 60px;
}

/*----------------------------------------
  Body Overlay 
-----------------------------------------*/
.body-overlay {
  background-color: hsl(0, 0%, 0%);
  height: 100%;
  width: 100%;
  position: fixed;
  top: 0;
  z-index: 9999;
  inset-inline-start: 0;
  opacity: 0;
  visibility: hidden;
  -webkit-transition: all 0.3s 0s ease-out;
  -moz-transition: all 0.3s 0s ease-out;
  -ms-transition: all 0.3s 0s ease-out;
  -o-transition: all 0.3s 0s ease-out;
  transition: all 0.3s 0s ease-out;
}
.body-overlay.opened {
  opacity: 0.7;
  visibility: visible;
}

.auth-overlay-bg {
  position: fixed;
  pointer-events: none;
  top: 0;
  opacity: 1;
  inset-inline-start: 0;
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-position: top left;
  background-image: url(../images/bg/auth-body-bg.png);
  z-index: -1;
  background-size: cover;
}

/*----------------------------------------
  Mfp customize
-----------------------------------------*/
.mfp-iframe-holder .mfp-content {
  line-height: 0;
  width: 100%;
  max-width: 1280px;
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem), only screen and (min-width: 100rem) and (max-width: 112.49875rem) {
  .mfp-iframe-holder .mfp-content {
    max-width: 1000px;
  }
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .mfp-iframe-holder .mfp-content {
    max-width: 850px;
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .mfp-iframe-holder .mfp-content {
    max-width: 820px;
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .mfp-iframe-holder .mfp-content {
    max-width: 750px;
  }
}

.mfp-close {
  -webkit-transform: rotate(0deg);
  -ms-transform: rotate(0deg);
  transform: rotate(0deg);
}
.mfp-close:hover {
  color: var(--td-white);
}
.mfp-close::after {
  position: absolute;
  content: "\f00d";
  height: 100%;
  width: 100%;
  font-family: var(--td-ff-fontawesome);
  font-size: 31px;
  font-weight: 200;
  inset-inline-end: -20px;
  margin-top: -25px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .mfp-close::after {
    inset-inline-end: 15px;
    margin-top: -30px;
  }
}

/*----------------------------------------*/
/*  Select2 customize
/*----------------------------------------*/
.select2-dropdown {
  background: #fff;
  box-shadow: none;
}
.dark-theme .select2-dropdown {
  background-color: #17213f;
}
.select2-dropdown.select2-dropdown--above {
  box-shadow: none !important;
  border: 1px solid rgba(8, 8, 8, 0.16) !important;
}
.dark-theme .select2-dropdown.select2-dropdown--above {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: var(--td-primary) !important;
  color: var(--td-white) !important;
}
.select2-container--default .select2-selection {
  background-color: transparent;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: white;
  border: 1px solid rgba(8, 8, 8, 0.16);
  border-radius: 30px;
}
.dark-theme .select2-container--default .select2-selection {
  background-color: rgb(22, 31, 59);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.select2-container--default .select2-selection:hover {
  border-color: rgba(8, 8, 8, 0.16) !important;
}
.dark-theme .select2-container--default .select2-selection:hover {
  border-color: rgba(255, 255, 255, 0.1) !important;
}
.select2-container--default .select2-selection--single {
  height: 50px;
  line-height: 50px;
  display: flex;
  align-items: center;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 6px;
  width: 20px !important;
  height: 100% !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  top: 0;
  inset-inline-end: 6px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
  position: absolute;
  background-repeat: no-repeat;
  background-size: 20px 19px;
  transform-origin: center;
  transition: transform 0.3s ease;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-inline-end: 1.5px solid var(--td-text-primary);
  position: absolute;
  top: 45%;
  transform: translateY(-50%) rotate(45deg);
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
  color: #999;
  font-weight: 600;
  font-size: 14px;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
  height: 40px;
  background-color: transparent;
  border-color: rgba(8, 8, 8, 0.16);
  padding: 0 12px;
}
.dark-theme .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: rgba(255, 255, 255, 0.1) !important;
  color: #999999;
}
.select2-container--default.select2-container--focus .select2-selection {
  border-color: rgba(251, 64, 90, 0.5) !important;
}
.select2-container--default.select2-container--open .select2-selection {
  border-color: rgba(251, 64, 90, 0.5) !important;
}
.select2-container--default .select2-results__option {
  color: #999999;
  font-weight: 600;
  font-size: 14px;
}
.select2-container--default.select2-container--above.select2-container--open .select2-selection__arrow b {
  transform: rotate(222deg) !important;
}

.select2-primary .select2-container--default .select2-selection--multiple .select2-selection__choice {
  background: rgba(115, 103, 240, 0.16) !important;
  color: var(--td-primary) !important;
}

.select2-search__field {
  height: 40px;
}

.defaults-select .select2-container--default.select2-container--focus .select2-selection {
  border-width: 0;
}
.defaults-select .select2-container--default .select2-selection {
  border: 0;
}
.defaults-select .select2-container--default .select2-selection--single {
  height: inherit;
}
.defaults-select .select2-container--default .select2-selection--single .select2-selection__arrow {
  top: 0px;
}
.defaults-select .select2-container--default .select2-selection--single .select2-selection__arrow b {
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" fill="none"><path d="M10.9999 12.0743L15.5374 7.53676L16.8336 8.83292L10.9999 14.6666L5.16626 8.83292L6.46243 7.53676L10.9999 12.0743Z" fill="%232f2b3d" fill-opacity="0.9"/></svg>');
  background-size: 20px;
  inset-inline-end: 10px;
}
.defaults-select .select2-dropdown {
  min-width: 200px;
}
.defaults-select .select2-results__options::-webkit-scrollbar {
  width: 5px;
}
.defaults-select .select2-results__options::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
}

.select2-results__options::-webkit-scrollbar {
  width: 6px;
  background-clip: padding-box;
}
.select2-results__options::-webkit-scrollbar-track {
  background-color: #1a1c23;
  height: 8px;
  background-clip: padding-box;
  border-inline-end: 10px solid rgba(0, 0, 0, 0);
  border-top: 10px solid rgba(0, 0, 0, 0);
  border-bottom: 10px solid rgba(0, 0, 0, 0);
  border-radius: 20px;
}
.select2-results__options::-webkit-scrollbar-thumb {
  background-clip: padding-box;
  background-color: #2C2F3B;
  border-inline-end: 10px solid rgba(0, 0, 0, 0);
  border-top: 10px solid rgba(0, 0, 0, 0);
  border-bottom: 10px solid rgba(0, 0, 0, 0);
  border-radius: 20px;
}

.select2-container--open .select2-dropdown--below {
  border: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .select2-container--open .select2-dropdown--below {
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/*----------------------------------------*/
/*  flatpickr customize
/*----------------------------------------*/
.flatpickr-day.selected,
.flatpickr-day.startRange,
.flatpickr-day.endRange,
.flatpickr-day.selected.inRange,
.flatpickr-day.startRange.inRange,
.flatpickr-day.endRange.inRange,
.flatpickr-day.selected:focus,
.flatpickr-day.startRange:focus,
.flatpickr-day.endRange:focus,
.flatpickr-day.selected:hover,
.flatpickr-day.startRange:hover,
.flatpickr-day.endRange:hover,
.flatpickr-day.selected.prevMonthDay,
.flatpickr-day.startRange.prevMonthDay,
.flatpickr-day.endRange.prevMonthDay,
.flatpickr-day.selected.nextMonthDay,
.flatpickr-day.startRange.nextMonthDay,
.flatpickr-day.endRange.nextMonthDay {
  background: var(--td-primary) !important;
  border-color: var(--td-primary) !important;
}

.flatpickr-day.selected.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.startRange.startRange + .endRange:not(:nth-child(7n+1)),
.flatpickr-day.endRange.startRange + .endRange:not(:nth-child(7n+1)) {
  -webkit-box-shadow: -10px 0 0 #0d6efd !important;
  box-shadow: -10px 0 0 #0d6efd !important;
}

/*----------------------------------------*/
/* cookies style
/*----------------------------------------*/
.caches-privacy {
  max-width: 1040px;
  position: fixed;
  bottom: 30px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  row-gap: 12px;
  column-gap: 12px;
  border-radius: 12px;
  border: 1px solid #26456F;
  background: #0E1B2C;
  box-shadow: 0px 23px 100px 0px rgba(166, 239, 103, 0.16);
  padding: 12px 24px 12px 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 90%;
  z-index: 111;
  transition: 0.3s;
}
[dir=rtl] .caches-privacy {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .caches-privacy {
    flex-direction: column;
    align-items: self-start;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .caches-privacy {
    padding: 12px 16px 12px 16px;
  }
  .caches-privacy .caches-btns .td-btn {
    font-size: 12px;
  }
}
.caches-privacy .caches-contents .title {
  font-size: 16px;
  margin-bottom: 8px;
  color: var(--td-white);
  font-weight: 600;
}
@media only screen and (max-width: 35.99875rem) {
  .caches-privacy .caches-contents .title {
    font-size: 18px;
  }
}
.caches-privacy .caches-contents p {
  font-size: 14px;
  margin-bottom: 0;
  color: var(--td-white);
}
.caches-privacy .caches-contents p a {
  color: #FB405A;
}
.caches-privacy .caches-btns {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 0 0 auto;
  flex-wrap: wrap;
  gap: 12px;
}

/*----------------------------------------*/
/* FAQ styles
/*----------------------------------------*/
.td-faq-section {
  background-color: #F5F5F5;
}
.dark-theme .td-faq-section {
  background-color: #020A22;
}

.td-faq-style .accordion .accordion-button {
  padding: 0;
  font-size: 1.125rem;
  background: transparent;
  box-shadow: none;
  color: var(--td-heading);
  font-weight: 700;
  font-family: var(--td-ff-body);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .td-faq-style .accordion .accordion-button {
    font-size: 1rem;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .td-faq-style .accordion .accordion-button {
    font-size: 0.875rem;
  }
}
.td-faq-style .accordion .accordion-button:not(.collapsed) {
  border-radius: 0;
}
.td-faq-style .accordion .accordion-button:not(.collapsed) span {
  color: var(--td-primary);
}
.td-faq-style .accordion .accordion-button:not(.collapsed) .accordion-body {
  background: var(--td-white);
}
.td-faq-style .accordion .accordion-button:not(.collapsed)::after {
  background-image: url(../images/icons/multiplication.svg);
}
.td-faq-style .accordion .accordion-button::after {
  position: absolute;
  inset-inline-end: 20px;
  content: "";
  font-family: var(--td-ff-fontawesome);
  font-size: 1.125rem;
  font-weight: 400;
  text-align: center;
  top: 50%;
  transform: translateY(-50%);
  background-image: url(../images/icons/addition.svg);
  filter: invert(1) brightness(1.2);
}
.dark-theme .td-faq-style .accordion .accordion-button::after {
  filter: brightness(0) invert(1);
}
.td-faq-style .accordion .accordion-button span {
  padding-inline-end: 0.625rem;
  display: inline-block;
  transition: none;
}
.td-faq-style .accordion .accordion-body {
  background: transparent;
  border-radius: 0rem;
  padding: 0px 30px 30px 30px;
}
@media only screen and (max-width: 30.06125rem) {
  .td-faq-style .accordion .accordion-body {
    padding-inline-start: 1.25rem;
    padding-inline-end: 1.25rem;
  }
}
.td-faq-style .accordion .accordion-body .description {
  color: var(--td-text-primary);
}
.td-faq-style .accordion .accordion-body .description strong {
  color: var(--td-heading);
}
.td-faq-style .accordion .accordion-body .accordion-body-list {
  margin-top: 0.875rem;
}
.td-faq-style .accordion .accordion-body .accordion-body-list ul li:not(:last-child) {
  margin-bottom: 0.4375rem;
}
.td-faq-style .accordion .accordion-item {
  position: relative;
  padding: 1px;
  display: block;
  background-color: transparent;
  border: 0;
}
.td-faq-style .accordion .accordion-item .clip-path {
  position: relative;
  z-index: 3;
  background: var(--td-white);
  clip-path: polygon(99.536% 0.685%, 99.536% 0.685%, 99.598% 0.73%, 99.658% 0.86%, 99.713% 1.067%, 99.764% 1.346%, 99.809% 1.688%, 99.848% 2.087%, 99.879% 2.536%, 99.903% 3.027%, 99.918% 3.554%, 99.923% 4.11%, 99.923% 80.814%, 99.923% 80.814%, 99.921% 81.115%, 99.917% 81.411%, 99.909% 81.7%, 99.899% 81.982%, 99.887% 82.255%, 99.871% 82.517%, 99.854% 82.766%, 99.833% 83.002%, 99.811% 83.222%, 99.786% 83.425%, 99.768% 83.551%, 97.504% 98.627%, 97.504% 98.627%, 97.483% 98.755%, 97.462% 98.87%, 97.44% 98.973%, 97.417% 99.062%, 97.394% 99.139%, 97.37% 99.202%, 97.346% 99.251%, 97.321% 99.286%, 97.296% 99.308%, 97.271% 99.315%, 0.464% 99.315%, 0.464% 99.315%, 0.402% 99.27%, 0.342% 99.14%, 0.287% 98.933%, 0.236% 98.654%, 0.191% 98.312%, 0.152% 97.913%, 0.121% 97.464%, 0.097% 96.973%, 0.082% 96.446%, 0.077% 95.89%, 0.077% 4.11%, 0.077% 4.11%, 0.082% 3.554%, 0.097% 3.027%, 0.121% 2.536%, 0.152% 2.087%, 0.191% 1.688%, 0.236% 1.346%, 0.287% 1.067%, 0.342% 0.86%, 0.402% 0.73%, 0.464% 0.685%, 99.536% 0.685%);
  width: 100%;
}
.dark-theme .td-faq-style .accordion .accordion-item .clip-path {
  background: #04060a;
}
.td-faq-style .accordion .accordion-item .clip-path::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) 68.94%, rgba(142, 84, 233, 0.1) 100%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.dark-theme .td-faq-style .accordion .accordion-item .clip-path::before {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
}
.td-faq-style .accordion .accordion-item::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  opacity: 0.4;
  content: "";
  clip-path: polygon(99.536% 0.685%, 99.536% 0.685%, 99.598% 0.73%, 99.658% 0.86%, 99.713% 1.067%, 99.764% 1.346%, 99.809% 1.688%, 99.848% 2.087%, 99.879% 2.536%, 99.903% 3.027%, 99.918% 3.554%, 99.923% 4.11%, 99.923% 80.814%, 99.923% 80.814%, 99.921% 81.115%, 99.917% 81.411%, 99.909% 81.7%, 99.899% 81.982%, 99.887% 82.255%, 99.871% 82.517%, 99.854% 82.766%, 99.833% 83.002%, 99.811% 83.222%, 99.786% 83.425%, 99.768% 83.551%, 97.504% 98.627%, 97.504% 98.627%, 97.483% 98.755%, 97.462% 98.87%, 97.44% 98.973%, 97.417% 99.062%, 97.394% 99.139%, 97.37% 99.202%, 97.346% 99.251%, 97.321% 99.286%, 97.296% 99.308%, 97.271% 99.315%, 0.464% 99.315%, 0.464% 99.315%, 0.402% 99.27%, 0.342% 99.14%, 0.287% 98.933%, 0.236% 98.654%, 0.191% 98.312%, 0.152% 97.913%, 0.121% 97.464%, 0.097% 96.973%, 0.082% 96.446%, 0.077% 95.89%, 0.077% 4.11%, 0.077% 4.11%, 0.082% 3.554%, 0.097% 3.027%, 0.121% 2.536%, 0.152% 2.087%, 0.191% 1.688%, 0.236% 1.346%, 0.287% 1.067%, 0.342% 0.86%, 0.402% 0.73%, 0.464% 0.685%, 99.536% 0.685%);
  border-radius: 2px;
}
.td-faq-style .accordion .accordion-item:not(:last-of-type) {
  margin-bottom: 1.25rem;
}
.td-faq-style .accordion .accordion-item:not(:first-of-type) {
  border-top: 0;
}
.td-faq-style .accordion .accordion-item:last-of-type > .accordion-collapse {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.td-faq-style .accordion .accordion-item:first-of-type .accordion-button {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.td-faq-style .accordion .accordion-item.accordion-active {
  box-shadow: none;
  border: 0;
}
.td-faq-style .accordion .accordion-item.accordion-active .accordion-button {
  color: var(--td-heading);
}
.td-faq-style .accordion .accordion-item.accordion-active:not(:first-of-type) {
  border-top: var(--bs-accordion-border-width) solid rgba(255, 255, 255, 0.16);
}
.td-faq-style .accordion .accordion-item.accordion-active .faq-shape-bg {
  opacity: 1;
}
.td-faq-style .accordion .accordion-header button {
  padding: 1.25rem 2.5rem 1.25rem 1.25rem;
  line-height: 1.5;
}
@media only screen and (max-width: 35.99875rem) {
  .td-faq-style .accordion .accordion-header button {
    padding: 1.125rem 2.1875rem 1.125rem 1.125rem;
  }
}

.faq-thumb {
  max-width: 532px;
  margin-inline-start: auto;
}

.faq-shapes .shape-one {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  z-index: -1;
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .faq-shapes .shape-one img {
    width: 200px;
  }
}

.td-faq-section-two {
  background: #F5F5F5;
}

/*----------------------------------------*/
/* Date range picker
/*----------------------------------------*/
.drp-buttons .cancelBtn.btn.btn-sm.td-btn.btn-xs.danger-btn {
  padding: 0 16px;
  height: 30px;
  font-size: 12px;
  gap: 4px;
}
.drp-buttons .applyBtn.btn.btn-sm.td-btn.btn-xs.primary-btn {
  padding: 0 16px;
  height: 30px;
  font-size: 12px;
  gap: 4px;
}

.daterangepicker td.in-range {
  background-color: rgba(251, 64, 90, 0.2) !important;
  border-color: transparent;
}

.daterangepicker td.active,
.daterangepicker td.active:hover {
  background-color: var(--td-primary);
  color: var(--td-heading);
}

/*----------------------------------------*/
/* Table styles
/*----------------------------------------*/
.table-container {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
  border: 1px solid rgba(255, 255, 255, 0.06);
  border-radius: 24px;
}
.table-container .table-heading {
  margin-bottom: 20px;
  padding: 0 16px;
}
.table-container .table-heading .title {
  font-size: 20px;
}

.table-description {
  display: flex;
  align-items: center;
  gap: 0.625rem;
}
.table-description .icon {
  display: flex;
  align-items: center;
  gap: 0.625rem;
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.16) 0%, rgba(142, 84, 233, 0.16) 100%);
  border-radius: 30px;
  justify-content: center;
  width: 44px;
  height: 44px;
  position: relative;
  font-size: 18px;
  color: var(--td-white);
  flex: 0 0 auto;
}
.table-description .icon::before {
  position: absolute;
  content: "";
  inset: 0;
  padding: 1px;
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  border-radius: 24px;
  z-index: -1;
}
.dark-theme .table-description .icon::before {
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
}
.table-description .icon span {
  display: inline-flex;
  align-items: center;
  color: #080808;
  font-size: 20px;
}
.dark-theme .table-description .icon span {
  color: white;
}
.table-description .contents .title {
  font-size: 14px;
  font-family: var(--td-ff-body);
  font-weight: 700;
}
.table-description .contents .date {
  font-size: 0.75rem;
}
.table-description .contents .date span {
  margin-inline-end: 4px;
}
.table-description.is-primary-10 .icon {
  background: rgba(251, 64, 90, 0.1);
  color: var(--td-danger);
}
.table-description.is-danger-10 .icon {
  background: rgba(251, 64, 90, 0.1);
  color: var(--td-danger);
}

.td-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}
.td-table.recent-table {
  min-width: 1280px;
  position: relative;
  z-index: 1;
}
.td-table.recent-table::before {
  position: absolute;
  content: "";
  inset: 0;
  padding: 1px;
  background: linear-gradient(90deg, rgba(8, 8, 8, 0.16) 0%, rgba(8, 8, 8, 0.16) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  border-radius: 24px;
  z-index: -1;
}
.dark-theme .td-table.recent-table::before {
  background: linear-gradient(180deg, rgb(11, 39, 122) 0%, rgb(0, 148, 255) 100%);
}
.td-table thead {
  background: rgba(221, 221, 221, 0.5);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  overflow: hidden;
}
.dark-theme .td-table thead {
  background: rgba(255, 255, 255, 0.08);
}
.td-table thead tr th {
  border-inline-start: 0;
  border-inline-end: 0;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #47494E;
}
.dark-theme .td-table thead tr th {
  color: #999999;
}
.td-table th {
  text-align: left;
  padding: 14px 16px;
  font-weight: 600;
  color: #999999;
}
.td-table td {
  text-align: left;
  padding: 13px 16px;
  border-bottom: 1px solid rgba(8, 8, 8, 0.16);
  font-weight: 500;
}
.dark-theme .td-table td {
  border-color: rgba(255, 255, 255, 0.06);
}
.td-table tr:last-child td {
  border-bottom: none;
}
.td-table.table-currency td {
  border-bottom: 0;
  padding: 10px 10px;
}
.td-table.table-currency td:last-child {
  text-align: end;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
}
.td-table.table-currency td:first-child {
  text-align: end;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}
.td-table.table-currency tr:hover {
  background: #142032;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
[dir=rtl] .table-actions {
  justify-content: end;
}
.table-actions .td-btn {
  min-width: 72px;
}
.table-actions .icon {
  background: var(--td-primary);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  color: var(--td-white);
}
.table-actions .icon.is-green {
  background: var(--td-green);
}
.table-actions .icon.is-outline {
  border: 1px solid #999;
  background-color: transparent;
  color: #999;
}
.table-actions .text {
  background: var(--td-primary);
  border-radius: 8px;
  padding: 7px 16px 7px 16px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
  line-height: 1;
}
.table-actions .text.is-danger {
  background: #eb4e5c;
}
.table-actions .text.is-primary {
  color: var(--td-heading);
  background: var(--td-primary);
}

.filter-bar {
  padding: 0 16px 16px;
  display: flex;
  align-items: center;
  gap: 16px 16px;
  flex-wrap: wrap;
}
@media only screen and (max-width: 35.99875rem) {
  .filter-bar {
    display: grid;
    grid-template-columns: 1fr;
  }
}
.filter-bar .clip-path {
  position: relative;
  display: inline-block;
  padding: 1px;
  width: 100%;
}
.filter-bar .clip-path::before, .filter-bar .clip-path::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  border-radius: 2px;
}
.filter-bar .clip-path::after {
  background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  opacity: 0;
  visibility: hidden;
}
.filter-bar .clip-path:hover::after {
  opacity: 1;
  visibility: visible;
}
.filter-bar .clip-path .inner {
  display: inline-flex;
  position: relative;
  z-index: 3;
  background: #020a22;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  gap: 12px;
  width: 100%;
}
.filter-bar .clip-path .inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.filter-bar .clip-path .filter-bar-search .input-box {
  border: 0;
}
.filter-bar .input-box {
  background-color: transparent;
  padding: 0px 16px 0px 16px;
  border: 1px solid rgba(8, 8, 8, 0.1);
  height: 44px;
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.dark-theme .filter-bar .input-box {
  border-color: rgba(255, 255, 255, 0.1);
}
.filter-bar .input-box::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.filter-bar .input-box::-moz-placeholder {
  /* Firefox 19+ */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.filter-bar .input-box:-moz-placeholder {
  /* Firefox 4-18 */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.filter-bar .input-box:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.filter-bar .input-box::placeholder {
  /* MODERN BROWSER */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.filter-bar .filter-bar-search {
  position: relative;
  width: 100%;
}
.filter-bar .filter-bar-search .input-box {
  padding-inline-start: 40px;
}
.filter-bar .filter-bar-search .search-icon {
  position: absolute;
  top: 50%;
  inset-inline-start: 16px;
  transform: translateY(-50%);
}
.filter-bar .td-form-group.has-right-icon .input-icon i {
  width: 18px;
  height: 18px;
}
.filter-bar .td-form-group .input-field .select2-container--default .select2-selection--single {
  height: 44px;
  line-height: 44px;
}
.filter-bar .td-form-group .input-field input {
  background-color: transparent;
  padding: 0px 16px 0px 16px;
  height: 44px;
  border: 1px solid rgba(8, 8, 8, 0.16);
  font-weight: 500;
  font-size: 14px;
}
.dark-theme .filter-bar .td-form-group .input-field input {
  border-color: rgba(255, 255, 255, 0.1);
  color: #999;
}
.dark-theme .filter-bar .td-form-group .input-field input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #999;
}
.dark-theme .filter-bar .td-form-group .input-field input::-moz-placeholder {
  /* Firefox 19+ */
  color: #999;
}
.dark-theme .filter-bar .td-form-group .input-field input:-moz-placeholder {
  /* Firefox 4-18 */
  color: #999;
}
.dark-theme .filter-bar .td-form-group .input-field input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #999;
}
.dark-theme .filter-bar .td-form-group .input-field input::placeholder {
  /* MODERN BROWSER */
  color: #999;
}
@media only screen and (max-width: 35.99875rem) {
  .filter-bar .filter-item .td-btn {
    width: 100%;
  }
}

.td-custom-table .clip-path {
  position: relative;
  padding: 1px;
}
.td-custom-table .clip-path .inner {
  display: flex;
  position: relative;
  z-index: 3;
  background: #020a22;
  clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
  gap: 12px;
  padding: 2px 40px;
}
.td-custom-table .clip-path .inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 68.94%, rgba(142, 84, 233, 0.2) 100%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.td-custom-table .clip-path::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 68.94%, rgba(142, 84, 233, 0.2) 100%);
  opacity: 1;
  content: "";
  clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 114%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
  border-radius: 2px;
}
.td-custom-table .clip-path::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 68.94%, rgba(142, 84, 233, 0.2) 100%);
  opacity: 1;
  content: "";
  clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 114%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
  border-radius: 2px;
}
.td-custom-table .contents {
  display: table;
  width: 100%;
  font-size: 14px;
}
.td-custom-table .contents .site-table-list {
  display: table-row;
}
.td-custom-table .contents .site-table-list .site-table-col {
  position: relative;
  display: table-cell;
  vertical-align: middle;
  padding: 20px 16px;
  padding-inline-start: 20px;
}
.td-custom-table .contents .site-table-list .site-table-col:last-child {
  text-align: end;
}
.td-custom-table .contents .site-table-list:last-child .site-table-col {
  border-bottom: 0;
}
.td-custom-table .contents .site-table-head .site-table-col {
  padding-top: 14px;
  padding-bottom: 14px;
  font-size: 14px;
  font-weight: 700;
  font-family: var(--td-ff-body);
}
.td-custom-table .right-two-grid {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  width: max-content;
  margin-inline-start: auto;
}
.td-custom-table .right-two-grid * {
  flex: 0 0 auto;
}

.site-table-list.site-table-head {
  position: relative;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

/*----------------------------------------*/
/* Alert Styles
/*----------------------------------------*/
.td-alert-box {
  background: var(--td-white);
  padding: 0.75rem 1.25rem 0.75rem 1rem;
  z-index: 1;
  position: relative;
  transition: 0.3s;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  column-gap: 0.75rem;
  align-items: center;
  width: 320px;
  border: 1px solid var(--td-border-primary);
  border-radius: 10px;
}
@media only screen and (max-width: 30.06125rem) {
  .td-alert-box {
    padding: 0.625rem 0.75rem 0.625rem;
    width: 300px;
  }
}
.td-alert-box.hidden {
  opacity: 0;
  transform: translateY(-50%, 1.25rem);
  pointer-events: none;
}
.td-alert-box.has-success {
  border-color: rgba(31, 197, 77, 0.3);
  background: #E9F9ED;
}
.dark-theme .td-alert-box.has-success {
  background: #061E27;
}
.td-alert-box.has-warning {
  border-color: rgba(254, 132, 1, 0.3);
  background: #FEF2E5;
}
.dark-theme .td-alert-box.has-warning {
  background: #393026;
}
.td-alert-box.has-danger {
  border-color: rgba(235, 78, 92, 0.3);
  background: #FCECEE;
}
.dark-theme .td-alert-box.has-danger {
  background-color: #191128;
}
.td-alert-box.has-info {
  border-color: rgba(46, 135, 232, 0.3);
  background: #EAF3FD;
}
.dark-theme .td-alert-box.has-info {
  background-color: #0B1935;
}
.td-alert-box .alert-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  column-gap: 0.75rem;
  flex-grow: 1;
}
@media only screen and (max-width: 35.99875rem) {
  .td-alert-box .alert-content {
    column-gap: 0.75rem;
  }
}
.td-alert-box .alert-content .alert-title {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--td-ff-body);
}
@media only screen and (max-width: 35.99875rem) {
  .td-alert-box .alert-content .alert-title {
    font-size: 0.875rem;
  }
}
.td-alert-box .alert-content .alert-message {
  font-size: 16px;
  position: relative;
  color: var(--td-heading);
  font-weight: 500;
}
.td-alert-box .alert-icon {
  flex: 0 0 auto;
}
.td-alert-box .alert-icon svg {
  width: 24px;
  height: 24px;
}
.td-alert-box .close-btn {
  padding: 5px;
  position: absolute;
  inset-inline-end: -12px;
  top: -12px;
  display: flex;
  width: 24px;
  height: 24px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  background-color: #FFE4E8;
  border-radius: 50%;
}
.dark-theme .td-alert-box .close-btn {
  background-color: var(--td-white);
}

.alert-show-status {
  position: fixed;
  top: 1rem;
  inset-inline-end: 1rem;
  z-index: 999;
}

/*----------------------------------------*/
/* Identity Alert Styles
/*----------------------------------------*/
.identity-alert {
  padding: 1.25rem;
  border: 2px dashed;
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px 12px;
  flex-wrap: wrap;
}
.identity-alert.fill-success {
  border-radius: 16px;
  border: 1px dashed rgba(0, 204, 0, 0.2);
  background: rgba(0, 204, 0, 0.19);
}
.identity-alert.fill-info {
  border-radius: 16px;
  border: 1px dashed rgba(65, 177, 252, 0.2);
  background: rgba(65, 177, 252, 0.07);
}
.identity-alert .left-contents {
  display: flex;
  align-items: center;
}
.identity-alert .right-actions {
  display: flex;
  align-items: center;
  gap: 12px 12px;
  flex-wrap: wrap;
}
@media only screen and (max-width: 35.99875rem) {
  .identity-alert {
    padding: 1rem;
  }
}
.identity-alert .icon {
  height: 3.125rem;
  width: 3.125rem;
  text-align: center;
  border-radius: 1.875rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-inline-end: 0.9375rem;
  font-size: 1.25rem;
  flex: 0 0 auto;
}
.identity-alert .icon svg {
  height: 1.375rem;
}
.identity-alert .contents .title {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.3125rem;
}
.identity-alert .contents .content p {
  display: inline-block;
  font-size: 0.875rem;
  margin-bottom: 0;
}
.dark-theme .identity-alert .contents .content p {
  color: rgba(255, 255, 255, 0.7);
}
.identity-alert .contents .content .underline-btn {
  font-weight: 500;
  position: relative;
  font-size: 0.875rem;
  color: var(--td-heading);
}
.identity-alert .contents .content .underline-btn::after {
  content: "";
  position: absolute;
  height: 0.0625rem;
  transition: 0.3s;
  inset-inline-start: auto;
  bottom: -2px;
  background: currentColor;
  width: 100%;
  inset-inline-end: 0;
}
.identity-alert .contents .content .underline-btn:hover {
  color: var(--td-heading);
}
.identity-alert .contents .content .underline-btn:hover::after {
  width: 0%;
  inset-inline-start: 0;
  inset-inline-end: auto;
}
.identity-alert.danger {
  border: 2px dashed rgba(243, 65, 65, 0.4);
  background: rgba(243, 65, 65, 0.1);
}
.identity-alert.danger .icon {
  background: #FFE5E5;
  color: var(--td-white);
}
.identity-alert.warning {
  background: rgba(255, 170, 0, 0.05);
  border-color: rgba(255, 170, 0, 0.3);
}
.identity-alert.warning .icon {
  background: #FFF6E5;
  color: #0C0C14;
}
.identity-alert.success {
  border: 2px dashed rgba(0, 204, 0, 0.3);
  background: rgba(0, 204, 0, 0.05);
}
.identity-alert.success .icon {
  background: #E5FFE5;
}
.identity-alert.info {
  background: rgba(65, 177, 252, 0.02);
  border: 2px dashed rgba(65, 177, 252, 0.2);
}
.identity-alert.info .icon {
  background: #E5EBFF;
}

/*----------------------------------------*/
/* Modal styles
/*----------------------------------------*/
.default-model .modal-dialog {
  max-width: 678px;
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .default-model .modal-dialog {
    max-width: 690px;
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .default-model .modal-dialog {
    max-width: 590px;
  }
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .default-model .modal-dialog {
    max-width: 540px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .default-model .modal-dialog {
    max-width: inherit;
  }
}
.default-model.modal {
  background: rgba(0, 0, 0, 0.8);
}
.default-model .modal-content {
  background: var(--td-white);
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
  position: relative;
  z-index: 1;
}
.dark-theme .default-model .modal-content {
  background: #0C1633;
}
.default-model .modal-content::before {
  position: absolute;
  content: "";
  inset: 0;
  padding: 1px;
  background: linear-gradient(180deg, rgba(8, 8, 8, 0.16) 0%, rgba(8, 8, 8, 0.16) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  border-radius: 16px;
  z-index: -1;
}
.dark-theme .default-model .modal-content::before {
  background: linear-gradient(180deg, rgb(11, 39, 122) 0%, rgb(0, 148, 255) 100%);
}
.default-model .modal-content .modal-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 15px 30px;
  background: #EDEDED;
  margin: 1px 1px 0;
  -webkit-border-radius: 16px 16px 0 0;
  -moz-border-radius: 16px 16px 0 0;
  -o-border-radius: 16px 16px 0 0;
  -ms-border-radius: 16px 16px 0 0;
  border-radius: 16px 16px 0 0;
}
.dark-theme .default-model .modal-content .modal-header {
  background: #16213F;
}
.default-model .modal-content .modal-body {
  padding: 30px 30px;
}

.modal-btn-close {
  width: 50px;
  height: 50px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: start;
  justify-content: end;
  font-size: 20px;
  color: var(--td-danger);
  position: absolute;
  inset-inline-end: -10px;
  top: -10px;
}
.modal-btn-close i {
  width: 30px;
  height: 30px;
}

.cryptocurrency-purchase-modal {
  display: grid;
  grid-template-columns: 1fr 450px;
  column-gap: 40px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .cryptocurrency-purchase-modal {
    grid-template-columns: 1fr 420px;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .cryptocurrency-purchase-modal {
    grid-template-columns: 1fr;
  }
}

.cryptocurrency-modal .modal-dialog {
  max-width: 1085px;
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .cryptocurrency-modal .modal-dialog {
    max-width: 960px;
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .cryptocurrency-modal .modal-dialog {
    max-width: 690px;
  }
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .cryptocurrency-modal .modal-dialog {
    max-width: 490px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .cryptocurrency-modal .modal-dialog {
    max-width: inherit;
  }
}
.cryptocurrency-modal .modal-content {
  background-color: #0E1B2C;
  border-radius: 24px;
}
.cryptocurrency-modal .currency-contents {
  padding: 40px 40px;
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .cryptocurrency-modal .currency-contents {
    padding: 25px 25px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .cryptocurrency-modal .currency-contents {
    padding: 20px 20px;
  }
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper {
  display: flex;
  align-items: center;
  row-gap: 20px;
  margin-top: 20px;
  flex-wrap: wrap;
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info:not(:last-child) {
  position: relative;
  padding-inline-end: 40px;
  margin-inline-end: 40px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (max-width: 35.99875rem) {
  .cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info:not(:last-child) {
    padding-inline-end: 25px;
    margin-inline-end: 25px;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info:not(:last-child) {
    padding-inline-end: 15px;
    margin-inline-end: 15px;
  }
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info::before {
  position: absolute;
  content: "";
  height: 26px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  top: 50%;
  transform: translateY(-50%);
  inset-inline-end: 0;
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info .status {
  font-size: 13px;
  font-weight: 500;
  display: block;
  margin-bottom: 0.25rem;
}
.cryptocurrency-modal .currency-contents .seller-info-wrapper .seller-info .status-title {
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents {
  margin-top: 45px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .heading {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 10px;
  margin-bottom: 18px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .heading h5 {
  font-size: 16px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .info ul {
  list-style-type: disc;
  padding-inline-start: 16px;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .info ul li {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 500;
}
.cryptocurrency-modal .currency-contents .dvertisers-terms-contents .info ul li:not(:last-child) {
  margin-bottom: 16px;
}
.cryptocurrency-modal .modal-forms {
  border-inline-start: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40px 40px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .cryptocurrency-modal .modal-forms {
    border-inline-start: 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .cryptocurrency-modal .modal-forms {
    padding: 25px 25px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .cryptocurrency-modal .modal-forms {
    padding: 20px 20px;
  }
}
.cryptocurrency-modal .modal-forms .forms-grid {
  display: grid;
  gap: 18px;
}
.cryptocurrency-modal .modal-forms .forms-grid .price {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 8px;
  padding: 5px 16px 5px 16px;
  display: inline-flex;
  gap: 10px;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 16px;
  letter-spacing: 0.03em;
  font-weight: 700;
  color: var(--td-green);
  width: max-content;
}
.cryptocurrency-modal .modal-forms .forms-grid .price span {
  color: var(--td-white);
}
.cryptocurrency-modal .modal-forms .forms-grid .input-box {
  border-radius: 16px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.1);
  border-width: 1px;
  padding: 12px 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cryptocurrency-modal .modal-forms .forms-grid .input-box .contents {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.cryptocurrency-modal .modal-forms .forms-grid .input-box .contents span {
  font-size: 20px;
  letter-spacing: 0.03em;
  font-weight: 700;
  color: var(--td-white);
}
.cryptocurrency-modal .modal-forms .forms-grid .input-box .contents .balance {
  font-size: 14px;
  font-weight: 500;
}
.cryptocurrency-modal .modal-forms .forms-grid .input-box .icon {
  border-inline-start: 1px solid rgba(255, 255, 255, 0.1);
  padding-inline-start: 10px;
}
.cryptocurrency-modal .modal-forms .forms-grid .input-box .icon img {
  width: 18px;
}
.cryptocurrency-modal .modal-forms .forms-grid .payment-method .td-form-group .input-field .select2-container--default .select2-selection {
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.cryptocurrency-modal .modal-forms .forms-grid .processing-fee {
  color: #999999;
  text-align: center;
  font-size: 14px;
  letter-spacing: 0.03em;
  font-weight: 500;
}
.cryptocurrency-modal .modal-forms .buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  margin-top: 1.5625rem;
}
.cryptocurrency-modal .modal-forms .buttons .payment-btn {
  font-size: 14px;
  line-height: 18px;
  font-weight: 700;
  padding: 0px 24px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}
.cryptocurrency-modal .modal-forms .buttons .payment-btn.buy {
  background-color: var(--td-green);
  color: var(--td-white);
}
.cryptocurrency-modal .modal-forms .buttons .payment-btn.sell {
  background-color: #EB4E5C;
  color: var(--td-white);
}
.cryptocurrency-modal .modal-forms .buttons .payment-btn.cancel {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.16);
  color: var(--td-white);
}
.cryptocurrency-modal.is-sell .modal-forms .forms-grid .price {
  color: var(--td-danger);
}

.default-model.is-verify-modal .modal-content {
  background: var(--td-white);
  border-radius: 16px;
}
.dark-theme .default-model.is-verify-modal .modal-content {
  background: #5A26FA;
  border: 1px solid #020A22;
}
.default-model.is-verify-modal .modal-content::before {
  display: none;
}
.default-model.is-verify-modal .modal-content .modal-body {
  padding: 50px 50px;
}
.default-model.is-verify-modal .modal-dialog {
  max-width: 600px;
}

.verify-modal-wrapper .verify-modal-icon {
  margin-bottom: 30px;
}
.verify-modal-wrapper .verify-modal-icon img {
  width: 60px;
  height: 60px;
}
.verify-modal-wrapper .verify-modal-contents .title {
  font-size: 30px;
  margin-bottom: 12px;
  color: var(--td-heading);
}

/*----------------------------------------*/
/* Breadcrumb styles
/*----------------------------------------*/
.breadcrumb-wrapper {
  backdrop-filter: blur(5px);
  padding: 30px 30px;
  background-color: #F2F2F2;
}
@media only screen and (max-width: 35.99875rem) {
  .breadcrumb-wrapper {
    padding: 25px 20px;
  }
}
.dark-theme .breadcrumb-wrapper {
  background-color: #0E1B42;
}

.breadcrumb-haeding .title {
  font-size: 30px;
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .breadcrumb-haeding .title {
    font-size: 28px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .breadcrumb-haeding .title {
    font-size: 24px;
  }
}

.breadcrumb-menu ul {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  gap: 20px;
  justify-content: center;
}
.breadcrumb-menu ul li {
  list-style: none;
  position: relative;
  line-height: 1;
}
.breadcrumb-menu ul li:last-child span {
  background: var(--Gradiant3, linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.breadcrumb-menu ul li:not(:last-child)::before {
  display: inline-block;
  content: "";
  position: absolute;
  top: 50%;
  height: 18px;
  width: 1px;
  inset-inline-end: -11px;
  font-size: 18px;
  background-color: rgba(0, 0, 0, 0.3);
  transform: translateY(-50%) rotate(15deg);
}
.dark-theme .breadcrumb-menu ul li:not(:last-child)::before {
  background-color: rgba(255, 255, 255, 0.3);
}
.breadcrumb-menu ul li.active span {
  color: var(--bd-theme-primary);
}
.breadcrumb-menu ul li span {
  font-size: 14px;
  font-family: var(--td-ff-heading);
  text-transform: capitalize;
  font-weight: 600;
}
.breadcrumb-menu ul li span a {
  font-weight: var(--bd-fw-medium);
}
.breadcrumb-menu ul li span a:hover {
  color: var(--bd-theme-primary);
}

/*----------------------------------------*/
/* Badge styles
/*----------------------------------------*/
.td-badge {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 7px 15px;
  line-height: 1;
  font-size: 14px;
  background: rgba(94, 167, 253, 0.2);
  -webkit-border-radius: 28px;
  -moz-border-radius: 28px;
  -o-border-radius: 28px;
  -ms-border-radius: 28px;
  border-radius: 28px;
  column-gap: 6px;
  min-width: 60px;
}
.td-badge.badge-success {
  color: #fff;
  background: #03A66D;
}
.td-badge.badge-warning {
  color: var(--td-white);
  background-color: #F7931A;
}
.td-badge.badge-danger {
  background: #e94e5b;
  color: #fff;
}
.td-badge.badge-green {
  background-color: #03a66d;
  color: var(--td-green);
}
.td-badge.badge-transparent-primary {
  background-color: #fb405a;
  color: var(--td-white);
}
.td-badge.badge-transparent-danger {
  background-color: #fb405a;
  color: var(--td-danger);
}
.td-badge.badge-transparent-green {
  background-color: #03a66d;
  color: var(--td-green);
}
.td-badge.badge-transparent-warning {
  background-color: #ffa336;
  color: var(--td-warning);
}
.td-badge.badge-outline-primary {
  background-color: #fb405a;
  color: var(--td-primary);
  border: 1px solid #fb405a;
}
.dark-theme .td-badge.badge-outline-primary {
  background-color: rgba(251, 64, 90, 0.16);
  color: var(--td-white);
}
.td-badge.badge-outline-danger {
  background-color: rgba(251, 64, 90, 0.16);
  color: var(--td-white);
  border: 1px solid #fb405a;
}
.dark-theme .td-badge.badge-outline-danger {
  background-color: rgba(251, 64, 90, 0.16);
  color: var(--td-danger);
}
.td-badge.badge-outline-warning {
  background-color: #ffa336;
  color: var(--td-white);
  border: 1px solid #ffa336;
}
.dark-theme .td-badge.badge-outline-warning {
  background-color: rgba(255, 163, 54, 0.16);
  color: var(--td-warning);
}
.td-badge.badge-outline-success {
  background-color: #03a66d;
  color: var(--td-white);
  border: 1px solid #03a66d;
}
.dark-theme .td-badge.badge-outline-success {
  background-color: rgba(3, 166, 109, 0.16);
  color: var(--td-white);
}
.td-badge.fill-badge-success {
  background-color: var(--td-green);
  color: var(--td-white);
}
.td-badge.fill-badge-warning {
  background: var(--td-warning);
  color: var(--td-white);
}
.td-badge.fill-badge-danger {
  background: var(--td-danger);
  color: var(--td-white);
}
.td-badge.fill-badge-green {
  background-color: var(--td-green);
  color: var(--td-white);
}
.td-badge.fill-badge-transparent-primary {
  background-color: var(--td-primary);
  color: var(--td-white);
}

.gradient-badge {
  position: relative;
  padding: 7px 14px;
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  z-index: 1;
  font-size: 14px;
  font-weight: 700;
  display: inline-block;
}
.gradient-badge::after {
  position: absolute;
  content: "";
  background: linear-gradient(90deg, rgba(195, 64, 192, 0.14) 0%, rgba(251, 64, 90, 0.14) 50%, rgba(247, 163, 74, 0.14) 100%);
  width: 100%;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
  border-radius: 30px;
}

.td-outline-badge {
  border-radius: 36px;
  padding: 5px 16px;
  font-size: 12px;
  font-weight: 500;
}
.td-outline-badge.badge-success {
  border: 1px solid #4CAF50;
  background: rgba(76, 175, 80, 0.16);
  color: #4CAF50;
}
.td-outline-badge.badge-warning {
  border: 1px solid #F7931A;
  background: rgba(247, 147, 26, 0.16);
  color: #F7931A;
}

.clip-badge {
  position: relative;
  z-index: 3;
  background: #282138;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
  border-radius: 2px;
  width: 100%;
  position: relative;
  display: inline-block;
  padding: 1px;
  background: transparent;
}
.clip-badge::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  border-radius: 2px;
  z-index: -1;
}
.clip-badge .inner-badge {
  background: #f4eefd;
  font-size: 14px;
  font-weight: 700;
  display: inline-flex;
  position: relative;
  z-index: 3;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  gap: 8px;
  padding: 0 20px;
  height: 42px;
  align-items: center;
  justify-content: center;
}
.dark-theme .clip-badge .inner-badge {
  background: #392467;
}
.clip-badge .inner-badge:before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) -1.59%, rgba(142, 84, 233, 0.2) 42.06%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.clip-badge .icon {
  width: 18px;
  display: inline-flex;
  align-items: center;
}

/*----------------------------------------*/
/* Buttons styles
/*----------------------------------------*/
.btn-wrap {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 15px 15px;
  flex-wrap: wrap;
}

.td-btn {
  padding: 0 26px;
  background: var(--td-primary);
  position: relative;
  z-index: 1;
  transition: all 0.4s ease-in-out;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  color: var(--td-white);
  font-size: 16px;
  font-weight: 600;
  gap: 8px;
}
.td-btn .btn-icon {
  display: flex;
  align-items: center;
}
.td-btn .btn-icon i {
  transition: 0.3s;
}
.td-btn:focus {
  color: var(--td-white);
}
.td-btn:hover {
  background-color: var(--td-primary);
  color: var(--td-heading);
  transform: translate3d(0, -2px, 0);
}
.td-btn.btn-primary-outline {
  border: 2px solid rgba(166, 239, 103, 0.4);
  background-color: transparent;
  color: var(--td-white);
}
.td-btn.btn-primary-outline:hover {
  background-color: var(--td-heading);
  color: var(--td-white);
}
.td-btn.outline-white-btn {
  background: var(--td-white);
  border: 1px solid #DBDBDB;
  color: var(--td-heading);
  gap: 8px;
}
.td-btn.outline-white-btn span svg {
  width: 20px;
  height: 20px;
}
.td-btn.outline-white-btn:hover {
  background-color: var(--td-heading);
  border-color: var(--td-heading);
  color: var(--td-white);
}
.td-btn.outline-danger-btn {
  border: 1px solid var(--td-danger);
  background-color: transparent;
  color: var(--td-danger);
  font-weight: 500;
}
.td-btn.outline-black-btn {
  border: 1px solid rgba(8, 8, 8, 0.6);
  color: rgba(8, 8, 8, 0.6);
  background-color: transparent;
}
.td-btn.outline-success-btn {
  border: 1px solid rgba(3, 166, 109, 0.6);
  color: rgba(3, 166, 109, 0.6);
  background-color: transparent;
}
.td-btn.btn-primary {
  background: var(--td-primary-alt);
  color: var(--td-white);
}
.td-btn.btn-primary span svg * {
  width: 16px;
  height: 16px;
  stroke: var(--td-white);
}
.td-btn.btn-primary:hover {
  background: var(--td-primary);
}
.td-btn.danger-btn {
  background: var(--td-danger);
  color: var(--td-white);
}
.td-btn.danger-btn span svg * {
  stroke: var(--td-white);
}
.td-btn.btn-white {
  background-color: var(--td-white);
  color: var(--td-heading);
}
.td-btn.btn-white:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
}
.td-btn.btn-gray {
  background: var(--td-alice-blue);
  border: 1px solid rgba(171, 178, 225, 0.3);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  color: var(--td-heading);
}
.td-btn.btn-gray .td-btn.btn-gray {
  background: rgba(255, 255, 255, 0.2);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.3);
  color: var(--td-white);
}
.td-btn.success-btn {
  background: var(--td-success);
}
.td-btn.btn-dark {
  background: #010c1a;
  border: 1px solid rgba(255, 255, 255, 0.16);
  color: var(--td-white);
}
.td-btn.white-btn-12 {
  background: rgba(26, 26, 26, 0.12);
  color: #47494E;
}
.dark-theme .td-btn.white-btn-12 {
  background: rgba(255, 255, 255, 0.12);
  color: #9A9DA7;
}
.td-btn.grd-fill-btn-primary {
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
  color: var(--td-white);
}
.td-btn.grd-fill-btn-secondary {
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
  color: var(--td-white);
}
.td-btn.grd-outline-fill-btn.primary-btn {
  background: transparent;
  color: var(--td-heading);
}
.td-btn.grd-outline-fill-btn.primary-btn:hover {
  color: var(--td-heading);
}
.td-btn.grd-outline-fill-btn {
  position: relative;
  padding: 1px;
  background-color: transparent;
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
  color: var(--td-white);
}
.td-btn.grd-outline-fill-btn.btn-secondary::before {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) -3.77%, rgba(142, 84, 233, 0.3) 100%);
}
.td-btn.grd-outline-fill-btn.btn-secondary .inner-btn {
  background: #edf1fd;
  color: var(--td-heading);
}
.dark-theme .td-btn.grd-outline-fill-btn.btn-secondary .inner-btn {
  background: #254083;
}
.td-btn.grd-outline-fill-btn .inner-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
  z-index: 3;
  background: #fbfbfb;
  padding: 0 30px;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
  border-radius: 2px;
  width: 100%;
  font-size: 16px;
  font-weight: 600;
  gap: 8px;
}
.dark-theme .td-btn.grd-outline-fill-btn .inner-btn {
  background: #282138;
}
.td-btn.grd-outline-fill-btn .inner-btn:before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) -3.77%, rgba(142, 84, 233, 0.1) 100%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.dark-theme .td-btn.grd-outline-fill-btn .inner-btn:before {
  background: linear-gradient(90deg, rgba(195, 64, 192, 0.1) 0%, rgba(251, 64, 90, 0.1) 50%, rgba(247, 163, 74, 0.1) 100%);
}
.td-btn.grd-outline-fill-btn::before {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
  border-radius: 2px;
}
.td-btn.grd-outline-fill-btn::after {
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
  opacity: 0;
  visibility: hidden;
}
.td-btn.grd-outline-fill-btn:hover {
  color: var(--td-white);
}
.td-btn.grd-outline-fill-btn:hover::after {
  opacity: 1;
  visibility: visible;
}
.td-btn.btn-chip {
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
  border-radius: 2px;
}
.td-btn.has-underline {
  background-color: transparent;
  height: auto;
  padding: 0;
}
.td-btn.has-underline .btn-text {
  color: var(--td-heading);
}
.td-btn.has-underline .btn-text::after {
  position: absolute;
  content: "";
  inset-inline-start: auto;
  bottom: -2px;
  background: currentColor;
  width: 0;
  height: 2px;
  transition: 0.3s;
  inset-inline-end: 0;
}
.td-btn.has-underline .btn-icon {
  width: 22px;
  height: 22px;
  background-color: var(--td-primary);
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.td-btn.has-underline .btn-icon svg * {
  stroke: var(--td-white);
}
.td-btn.has-underline:hover .btn-text::after {
  width: 100%;
  inset-inline-start: 0;
  inset-inline-end: auto;
}
.td-btn.btn-xs {
  padding: 0 16px;
  height: 30px;
  font-size: 14px;
  gap: 4px;
}
.td-btn.btn-xs .btn-icon i {
  width: 14px;
  height: 14px;
}
.td-btn.btn-h-36 {
  height: 36px;
  padding: 0 15px;
  font-weight: 500;
  font-size: 14px;
  gap: 4px;
}
.td-btn.btn-h-40 {
  height: 40px;
  padding: 0 24px;
  font-weight: 500;
  font-size: 14px;
  gap: 4px;
}
.td-btn.btn-sm {
  height: 44px;
  gap: 6px;
  font-size: 14px;
}
.td-btn.btn-sm svg {
  width: 16px;
  height: 16px;
}
.td-btn.btn-md {
  font-size: var(--font-size-b3);
  height: 50px;
}
.td-btn.btn-lg {
  height: 70px;
  font-size: 18px;
}
.td-btn.btn-xl {
  font-size: 20px;
  height: 75px;
}
.td-btn.btn-xxl {
  font-size: 22px;
  height: 100px;
}
.td-btn.btn-m-w {
  min-width: 7.5rem;
}

.td-underline-btn {
  font-weight: 500;
  position: relative;
  font-size: 16px;
}
.td-underline-btn::after {
  content: "";
  position: absolute;
  height: 1px;
  transition: 0.3s;
  inset-inline-start: auto;
  bottom: -1px;
  background: var(--td-primary);
  width: 0;
  inset-inline-end: 0;
}
.td-underline-btn:hover {
  color: var(--td-primary);
}
.td-underline-btn:hover::after {
  width: 100%;
  inset-inline-start: 0;
  inset-inline-end: auto;
}
.td-underline-btn.has-grad-one {
  background: var(--Gradiant3, linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.td-underline-btn.has-grad-one:after {
  background: var(--Gradiant3, linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%));
}

.td-form-btns {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.notification-btn {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
}
.notification-btn .icon {
  display: inline-flex;
  align-items: center;
}
.notification-btn .count {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
  width: 20px;
  font-size: 12px;
  gap: 2px;
  border-radius: 50%;
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
  color: var(--td-white);
}

/*----------------------------------------*/
/* Offcanvas styles
/*----------------------------------------*/
.offcanvas-area {
  background: var(--td-white) none repeat scroll 0 0;
  position: fixed;
  inset-inline-end: 0;
  top: 0;
  width: 360px;
  height: 100%;
  -webkit-transform: translateX(calc(100% + 80px));
  -moz-transform: translateX(calc(100% + 80px));
  -ms-transform: translateX(calc(100% + 80px));
  -o-transform: translateX(calc(100% + 80px));
  transform: translateX(calc(100% + 80px));
  -webkit-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  -moz-transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  transition: transform 0.45s ease-in-out, opacity 0.45s ease-in-out;
  z-index: 999;
  overflow-y: scroll;
  overscroll-behavior-y: contain;
  scrollbar-width: none;
}
.dark-theme .offcanvas-area {
  background-color: #171C35;
}
[dir=rtl] .offcanvas-area {
  left: 0;
  right: auto;
  transform: translateX(calc(-100% - 80px));
}
@media only screen and (max-width: 30.06125rem) {
  .offcanvas-area {
    width: 300px;
  }
}
.offcanvas-area ::-webkit-scrollbar {
  display: none;
}
.offcanvas-area.info-open {
  opacity: 1;
  transform: translateX(0);
}

.offcanvas-logo a img {
  height: 26px;
}

.offcanvas-content {
  padding-bottom: 45px;
}

.offcanva-wrapper {
  position: relative;
  height: 100%;
  padding: 28px 28px;
}

.offcanvas-top {
  margin-bottom: 25px;
}

.offcanvas-title {
  color: var(--td-white);
  font-size: 20px;
  margin-bottom: 20px;
}
@media only screen and (max-width: 30.06125rem) {
  .offcanvas-title {
    font-size: 20px;
  }
}

.offcanvas-overlay {
  position: fixed;
  height: 100%;
  width: 100%;
  background: hsl(0, 0%, 0%);
  z-index: 900;
  top: 0;
  opacity: 0;
  visibility: hidden;
  inset-inline-end: 0;
  transition: 0.3s;
  cursor: url("../images/icons/close.png"), pointer;
}
.offcanvas-overlay.overlay-open {
  opacity: 0.6;
  visibility: visible;
}

.sidebar-toggle {
  cursor: pointer;
}

.offcanvas-contact-icon {
  margin-inline-end: 15px;
}

.offcanvas-btn {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 15px;
}

.offcanvas-close-icon {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  height: 40px;
  width: 40px;
  line-height: 40px;
  border: 2px solid rgba(255, 250, 250, 0.1);
  background-color: var(--td-primary);
  border-radius: 50%;
}
.offcanvas-close-icon svg * {
  color: var(--td-heading);
}
.offcanvas-close-icon:hover {
  background-color: var(--td-primary);
  color: var(--td-white);
  border-color: transparent;
}

/*----------------------------------------*/
/* Section Title  
/*----------------------------------------*/
.section-title-wrapper .highlight {
  background: linear-gradient(90deg, #4776E6 56.79%, #8E54E9 97.46%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.section-title-wrapper .highlight-primary {
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 14.2%, #F7A34A 56.81%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.section-title-wrapper .section-title {
  font-size: 48px;
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .section-title-wrapper .section-title {
    font-size: 48px;
  }
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .section-title-wrapper .section-title {
    font-size: 44px;
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .section-title-wrapper .section-title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .section-title-wrapper .section-title {
    font-size: 34px;
  }
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .section-title-wrapper .section-title {
    font-size: 32px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .section-title-wrapper .section-title {
    font-size: 28px;
  }
}
.section-title-wrapper.is-white .section-title {
  color: var(--td-white);
}
.section-title-wrapper.is-white .description {
  color: rgba(255, 255, 255, 0.8);
}

.heading-chip-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 50px 50px;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.5);
}
.dark-theme .heading-chip-box {
  background-color: transparent;
}
.heading-chip-box .clip-border-bg {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  z-index: -1;
}

/*----------------------------------------*/
/* Tab customize
/*----------------------------------------*/
.td-tab .nav-tabs {
  padding: 0;
  margin: 0;
  border: 0;
}
.td-tab .nav-tabs .nav-link {
  padding: 0;
  margin: 0;
  border: 0;
  border-radius: 0;
}

/*----------------------------------------*/
/* Basic pagination styles
/*----------------------------------------*/
.td-pagination ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 10px 10px;
  flex-wrap: wrap;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-pagination ul {
    justify-content: start;
  }
}
.td-pagination ul li {
  list-style: none;
}
.td-pagination ul li .clip-path {
  position: relative;
  display: inline-block;
  padding: 1px;
}
.td-pagination ul li .clip-path::before, .td-pagination ul li .clip-path::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  border-radius: 2px;
}
.td-pagination ul li .clip-path::after {
  background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  opacity: 0;
  visibility: hidden;
}
.td-pagination ul li .clip-path:hover::after {
  opacity: 1;
  visibility: visible;
}
.td-pagination ul li a {
  width: 40px;
  height: 40px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  inset-inline-end: 0;
  top: 50%;
  font-size: 14px;
  line-height: 20px;
  font-weight: 900;
  position: relative;
  display: inline-flex;
  flex-wrap: wrap;
  z-index: 3;
  background: #f6f6f6;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  gap: 12px;
  padding: 6px;
  color: var(--td-text-primary);
}
.dark-theme .td-pagination ul li a {
  background: #0C142B;
}
.td-pagination ul li a:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
  color: var(--td-white);
  z-index: -1;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  opacity: 0;
}
.td-pagination ul li a i,
.td-pagination ul li a iconify-icon {
  font-size: 20px;
}
.td-pagination ul li .current::before {
  opacity: 0;
}
.td-pagination ul li .current a {
  border: 0;
  color: var(--td-white);
}
.td-pagination ul li .current a::after {
  opacity: 1;
}
.td-pagination ul li.disabled i,
.td-pagination ul li.disabled iconify-icon {
  color: rgba(8, 8, 8, 0.1);
}
.dark-theme .td-pagination ul li.disabled i,
.dark-theme .td-pagination ul li.disabled iconify-icon {
  color: rgba(255, 255, 255, 0.1);
}
.td-pagination ul li.disabled .clip-path a {
  color: rgba(255, 255, 255, 0.5);
  cursor: default;
}
.td-pagination ul li.disabled .clip-path:hover::after {
  display: none;
}

.td-pagination-two ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 10px 10px;
  flex-wrap: wrap;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-pagination-two ul {
    justify-content: start;
  }
}
.td-pagination-two ul li {
  list-style: none;
}
.td-pagination-two ul li .clip-path {
  position: relative;
  display: inline-block;
  padding: 1px;
}
.td-pagination-two ul li .clip-path::before, .td-pagination-two ul li .clip-path::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  border-radius: 2px;
}
.td-pagination-two ul li .clip-path::after {
  background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  opacity: 0;
  visibility: hidden;
}
.td-pagination-two ul li .clip-path:hover::after {
  opacity: 1;
  visibility: visible;
}
.td-pagination-two ul li a {
  width: 40px;
  height: 40px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
  inset-inline-end: 0;
  top: 50%;
  font-size: 14px;
  line-height: 20px;
  font-weight: 900;
  position: relative;
  display: inline-flex;
  flex-wrap: wrap;
  z-index: 3;
  background: #f6f6f6;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  gap: 12px;
  padding: 6px;
  color: var(--td-text-primary);
}
.dark-theme .td-pagination-two ul li a {
  background: #0C142B;
}
.td-pagination-two ul li a:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) -10%, rgba(142, 84, 233, 0.3) 265%);
  color: var(--td-white);
  z-index: -1;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  opacity: 0;
}
.td-pagination-two ul li a i,
.td-pagination-two ul li a iconify-icon {
  font-size: 20px;
}
.td-pagination-two ul li .current::before {
  opacity: 0;
}
.td-pagination-two ul li .current a {
  border: 0;
  color: var(--td-white);
}
.td-pagination-two ul li .current a::after {
  opacity: 1;
}
.td-pagination-two ul li.disabled i,
.td-pagination-two ul li.disabled iconify-icon {
  color: rgba(8, 8, 8, 0.1);
}
.dark-theme .td-pagination-two ul li.disabled i,
.dark-theme .td-pagination-two ul li.disabled iconify-icon {
  color: rgba(255, 255, 255, 0.1);
}
.td-pagination-two ul li.disabled .clip-path a {
  color: rgba(255, 255, 255, 0.5);
  cursor: default;
}
.td-pagination-two ul li.disabled .clip-path:hover::after {
  display: none;
}

/*----------------------------------------*/
/* Back to top 
/*----------------------------------------*/
.back-to-top-wrap {
  position: fixed;
  bottom: 30px;
  inset-inline-end: 30px;
  height: 46px;
  width: 46px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 100;
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
}
@media only screen and (max-width: 35.99875rem) {
  .back-to-top-wrap {
    height: 40px;
    width: 40px;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .back-to-top-wrap {
    bottom: 20px;
    inset-inline-end: 20px;
  }
}
.back-to-top-wrap.active-progress {
  opacity: 1;
  visibility: visible;
  transform: translateY(0px);
}
.back-to-top-wrap::after {
  position: absolute;
  content: "";
  background-image: url(../images/icons/up.png);
  background-repeat: no-repeat;
  background-size: 20px;
  background-position: center;
  text-align: center;
  font-size: 16px;
  font-weight: 400;
  color: var(--td-black);
  inset-inline-start: 0;
  top: 0;
  height: 100%;
  width: 100%;
  cursor: pointer;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
  filter: brightness(0) invert(1);
}
@media only screen and (max-width: 35.99875rem) {
  .back-to-top-wrap::after {
    font-size: 14px;
  }
}
.back-to-top-wrap svg path {
  fill: none;
}
.back-to-top-wrap svg.backtotop-circle path {
  stroke: #ccc;
  stroke-width: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: all 400ms linear;
  -o-transition: all 400ms linear;
  transition: all 400ms linear;
}

/*----------------------------------------*/
/* Forms styles
/*----------------------------------------*/
input[type=text],
input[type=search],
input[type=email],
input[type=tel],
input[type=number],
input[type=password],
input[type=date],
input[type=url],
textarea {
  outline: none;
  height: 50px;
  width: 100%;
  padding: 0 20px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  color: var(--td-black);
}
input[type=text]::-webkit-input-placeholder,
input[type=search]::-webkit-input-placeholder,
input[type=email]::-webkit-input-placeholder,
input[type=tel]::-webkit-input-placeholder,
input[type=number]::-webkit-input-placeholder,
input[type=password]::-webkit-input-placeholder,
input[type=date]::-webkit-input-placeholder,
input[type=url]::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #9A9DA7;
}
input[type=text]::-moz-placeholder,
input[type=search]::-moz-placeholder,
input[type=email]::-moz-placeholder,
input[type=tel]::-moz-placeholder,
input[type=number]::-moz-placeholder,
input[type=password]::-moz-placeholder,
input[type=date]::-moz-placeholder,
input[type=url]::-moz-placeholder,
textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: #9A9DA7;
}
input[type=text]:-moz-placeholder,
input[type=search]:-moz-placeholder,
input[type=email]:-moz-placeholder,
input[type=tel]:-moz-placeholder,
input[type=number]:-moz-placeholder,
input[type=password]:-moz-placeholder,
input[type=date]:-moz-placeholder,
input[type=url]:-moz-placeholder,
textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: #9A9DA7;
}
input[type=text]:-ms-input-placeholder,
input[type=search]:-ms-input-placeholder,
input[type=email]:-ms-input-placeholder,
input[type=tel]:-ms-input-placeholder,
input[type=number]:-ms-input-placeholder,
input[type=password]:-ms-input-placeholder,
input[type=date]:-ms-input-placeholder,
input[type=url]:-ms-input-placeholder,
textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #9A9DA7;
}
input[type=text]::placeholder,
input[type=search]::placeholder,
input[type=email]::placeholder,
input[type=tel]::placeholder,
input[type=number]::placeholder,
input[type=password]::placeholder,
input[type=date]::placeholder,
input[type=url]::placeholder,
textarea::placeholder {
  /* MODERN BROWSER */
  color: #9A9DA7;
}
input[type=text]:focus,
input[type=search]:focus,
input[type=email]:focus,
input[type=tel]:focus,
input[type=number]:focus,
input[type=password]:focus,
input[type=date]:focus,
input[type=url]:focus,
textarea:focus {
  border-color: rgba(251, 64, 90, 0.5);
}

textarea {
  padding: 14px 24px;
  border-radius: 8px !important;
}
textarea:focus {
  border-color: var(--td-heading);
}

.custom-checkbox input {
  opacity: 0;
  position: absolute;
}
.custom-checkbox input + label {
  position: relative;
  font-size: 15px;
  line-height: 25px;
  color: var(--td-text-primary);
  font-weight: 400;
  padding-inline-start: 20px;
  cursor: pointer;
  margin-bottom: 0;
}
.custom-checkbox input + label::before {
  content: " ";
  position: absolute;
  top: 6px;
  inset-inline-start: 0;
  width: 14px;
  height: 14px;
  background-color: var(--td-white);
  border: 1px solid var(--td-border-primary);
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transition: all 0.3s;
}
.custom-checkbox input + label::after {
  content: " ";
  position: absolute;
  top: 9px;
  inset-inline-start: 2px;
  width: 10px;
  height: 5px;
  background-color: transparent;
  border-bottom: 1px solid var(--td-white);
  border-inline-start: 1px solid var(--td-white);
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transform: rotate(-45deg);
  opacity: 0;
  transition: all 0.3s;
}
.custom-checkbox input:checked + label::before {
  background-color: rgba(251, 64, 90, 0.5);
  border-color: rgba(251, 64, 90, 0.5);
}
.custom-checkbox input:checked + label::after {
  opacity: 1;
}

.custom-radio input {
  opacity: 0;
  position: absolute;
}
.custom-radio input + label {
  position: relative;
  line-height: 25px;
  color: var(--td-text-primary);
  padding-inline-start: 22px;
  cursor: pointer;
  margin-bottom: 0;
  font-size: 16px;
  font-weight: 500;
}
.custom-radio input + label::before {
  content: " ";
  position: absolute;
  top: 5px;
  inset-inline-start: 0;
  width: 16px;
  height: 16px;
  background-color: transparent;
  border: 1px solid #999999;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transition: all 0.3s;
}
.custom-radio input + label::after {
  content: " ";
  position: absolute;
  top: 8px;
  inset-inline-start: 2px;
  width: 10px;
  height: 5px;
  background-color: transparent;
  border-bottom: 1px solid var(--td-white);
  border-inline-start: 1px solid var(--td-white);
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
  transform: rotate(-45deg);
  opacity: 0;
  transition: all 0.3s;
}
.custom-radio input:checked + label {
  color: rgba(251, 64, 90, 0.5);
}
.custom-radio input:checked + label::before {
  border-color: rgba(251, 64, 90, 0.5);
}
.custom-radio input:checked + label::after {
  opacity: 1;
}
.custom-radio input + label::before {
  border-radius: 50%;
}
.custom-radio input + label::after {
  width: 10px;
  height: 10px;
  inset-inline-start: 3px;
  background: rgba(251, 64, 90, 0.5);
  border-radius: 50%;
  border-color: rgba(251, 64, 90, 0.5);
}

.form-switch {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
}
.form-switch input[type=checkbox] {
  opacity: 1;
  position: relative;
  margin-inline-start: 0 !important;
  margin-top: 0;
  outline: none;
  margin-bottom: 0;
}
.form-switch input[type=checkbox]:checked {
  background-color: rgba(251, 64, 90, 0.5);
  border-color: rgba(251, 64, 90, 0.5);
}
.form-switch input[type=checkbox]:focus {
  outline: 0;
  box-shadow: none;
}
.form-switch input[type=checkbox] ~ label {
  padding-inline-start: 10px;
}
.form-switch input[type=checkbox] ~ label::before, .form-switch input[type=checkbox] ~ label::after {
  display: none;
}

.animate-custom .cbx {
  -webkit-user-select: none;
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  cursor: pointer;
}
.animate-custom .cbx::before {
  display: none;
}
.animate-custom .cbx span {
  display: inline-block;
  vertical-align: middle;
}
.animate-custom .cbx span:first-child {
  position: relative;
  width: 18px;
  height: 18px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}
.dark-theme .animate-custom .cbx span:first-child {
  border-color: #9A9DA7;
}
.animate-custom .cbx span:first-child svg {
  position: absolute;
  z-index: 1;
  top: 4px;
  inset-inline-start: 2px;
  fill: none;
  stroke: var(--td-white);
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}
.animate-custom .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: #FB405A;
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
  transition-delay: 0.2s;
}
.animate-custom .cbx span:last-child {
  margin-inline-start: 5px;
  color: var(--td-text-primary);
  font-weight: 500;
  font-size: 14px;
}
.animate-custom .cbx span:last-child:after {
  content: "";
  position: absolute;
  top: 8px;
  inset-inline-start: 0;
  height: 1px;
  width: 100%;
  background: #b9b8c3;
  transform-origin: 0 0;
  transform: scaleX(0);
}
.animate-custom .cbx:hover span:first-child {
  border-color: rgba(251, 64, 90, 0.5);
}
.animate-custom .inp-cbx:checked + .cbx span:first-child {
  border-color: #FB405A;
  background: #FB405A;
  animation: check-15 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}
.animate-custom .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(2.2);
  opacity: 0;
  transition: all 0.6s ease;
}
.animate-custom .inp-cbx:checked + .cbx span:last-child {
  transition: all 0.3s ease;
}
.animate-custom input[type=checkbox] ~ label::after {
  display: none;
}
.animate-custom input[type=checkbox] ~ label {
  padding-inline-start: 0;
}

@keyframes check-15 {
  50% {
    transform: scale(1.2);
  }
}
.was-not-validated .td-form-group .input-field {
  position: relative;
}
.was-not-validated .td-form-group .input-field input {
  border-color: var(--td-danger);
  background: rgba(220, 29, 75, 0.1);
}
.was-not-validated .td-form-group .input-field input:focus {
  background: rgba(220, 29, 75, 0.1);
}
.was-not-validated .td-form-group .input-field .input-group-text {
  border-color: var(--td-danger);
  background: rgba(220, 29, 75, 0.1);
}

[dir=rtl] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
}

.td-form-group.has-currency {
  position: relative;
}
.td-form-group.has-currency .input-field input {
  border-radius: 4px;
  border: 1px solid hsla(0, 0%, 100%, 0.1);
  padding: 10px 1px 10px 16px;
  height: 40px;
  position: relative;
  background-color: rgba(0, 0, 0, 0);
  color: hsla(0, 0%, 100%, 0.6);
  text-align: end;
  font-family: "Satoshi-Medium", sans-serif;
  line-height: 20px;
  padding-inline-end: 80px;
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input:focus {
  border: 1px solid rgba(251, 64, 90, 0.5);
}
.td-form-group.has-currency .input-field input::-webkit-inner-spin-button, .td-form-group.has-currency .input-field input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}
.td-form-group.has-currency .input-field input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field input::placeholder {
  /* MODERN BROWSER */
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
  font-weight: 500;
}
.td-form-group.has-currency .input-field .lable-currency {
  position: absolute;
  inset-inline-end: 10px;
  top: 5px;
}
.td-form-group.has-currency .input-field .lable-currency {
  position: absolute;
  inset-inline-end: 42px;
  top: 50%;
  font-size: 12px;
  font-weight: 500;
  transform: translateY(-50%);
}
.td-form-group.has-currency .numeric-controls-panel {
  display: flex;
  flex-direction: column;
  width: 32px;
  position: absolute;
  inset-inline-end: 0;
  top: 0;
  height: 100%;
  border-inline-start: 1px solid hsla(0, 0%, 100%, 0.1);
}
.td-form-group.has-currency .numeric-controls-panel button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50%;
}
.td-form-group.has-currency .numeric-controls-panel button:last-child {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.td-form-group.has-right-icon .form-control {
  padding-inline-end: 50px;
}
.td-form-group.has-right-icon .input-icon {
  position: absolute;
  inset-inline-end: 15px;
  top: 50%;
  transform: translateY(-50%);
}
.td-form-group.has-right-icon .input-icon i {
  font-size: 20px;
  display: flex;
  align-items: center;
}
.td-form-group.has-right-icon .input-icon.eyeicon {
  cursor: pointer;
  inset-inline-end: 20px !important;
  inset-inline-start: auto !important;
  line-height: 1;
}
.td-form-group.has-right-icon .input-icon.icon-selected svg * {
  stroke: rgba(8, 8, 8, 0.7);
  /* Change stroke color */
  fill: rgba(8, 8, 8, 0.7);
  /* Change stroke color */
  stroke-opacity: 1;
  /* Full opacity */
  transition: all 0.3s ease;
  /* Smooth animation */
}
.td-form-group.has-right-payment-method .form-control {
  padding-inline-end: 75px;
}
.td-form-group.has-right-payment-method .input-payment-method-img {
  width: 50px;
  height: 25px;
  position: absolute;
  inset-inline-end: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.04);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.td-form-group.selected_icon .input-icon {
  inset-inline-end: 33px;
  cursor: pointer;
}
.td-form-group.has-left-icon .form-control {
  padding-inline-start: 44px;
}
.td-form-group.has-left-icon .input-icon {
  position: absolute;
  inset-inline-start: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20px;
  width: max-content;
  display: inline-flex;
  align-items: center;
}
.td-form-group.has-left-icon .input-icon.eyeicon {
  cursor: pointer;
}
.td-form-group.has-left-icon .input-icon.eyeicon .eye-img {
  width: 18px;
}
.dark-theme .td-form-group.has-left-icon .input-icon.eyeicon .eye-img {
  filter: brightness(0) invert(1);
}
.td-form-group .input-field {
  position: relative;
  width: 100%;
}
.td-form-group .input-field.input-group {
  flex-wrap: nowrap;
}
.td-form-group .input-field .input-group-text {
  background: transparent;
  mix-blend-mode: normal;
  border: 1px solid #212a3d;
  color: var(--td-text-primary);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 700;
}
[dir=rtl] .td-form-group .input-field .input-group-text {
  border-top-right-radius: 8px !important;
  border-bottom-right-radius: 8px !important;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-inline-start: 0;
}
.td-form-group .input-field.disabled input,
.td-form-group .input-field.disabled textarea {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}
.td-form-group .input-field.disabled input:focus,
.td-form-group .input-field.disabled textarea:focus {
  border-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-field .text-content {
  background: var(--td-white);
  box-shadow: 0px 4px 10px rgba(0, 101, 255, 0.04);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  position: absolute;
  top: 50%;
  inset-inline-end: 5px;
  transform: translateY(-50%);
  padding: 5px 8px 6px;
  font-size: 14px;
  font-weight: 500;
  color: rgba(251, 64, 90, 0.5);
}
.td-form-group .input-field input,
.td-form-group .input-field textarea {
  font-size: 14px;
}
.td-form-group .input-field input::-webkit-input-placeholder,
.td-form-group .input-field textarea::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::-moz-placeholder,
.td-form-group .input-field textarea::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-moz-placeholder,
.td-form-group .input-field textarea:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input:-ms-input-placeholder,
.td-form-group .input-field textarea:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field input::placeholder,
.td-form-group .input-field textarea::placeholder {
  /* MODERN BROWSER */
  color: rgba(8, 8, 8, 0.65);
  font-size: 14px;
}
.td-form-group .input-field textarea {
  padding: 12px 15px;
  height: 150px;
  resize: none;
  line-height: 1.5;
}
.td-form-group .input-field textarea:focus {
  border-color: rgba(251, 64, 90, 0.5);
}
.td-form-group .input-field.height-large textarea {
  height: 237px;
}
.td-form-group .input-field .form-control {
  font-size: 14px;
  box-shadow: none;
  border-radius: 30px;
  color: var(--td-black);
  border: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .td-form-group .input-field .form-control {
  border-color: rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.04);
  color: var(--td-white);
}
.td-form-group .input-field .form-control:focus {
  border-color: rgba(251, 64, 90, 0.5);
}
.td-form-group .input-field .form-control:disabled {
  background-color: #f1f1f1;
  color: rgba(8, 8, 8, 0.7);
  cursor: not-allowed;
}
.dark-theme .td-form-group .input-field .form-control:disabled {
  background-color: rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.7);
}
.td-form-group .input-description {
  font-size: 12px;
  margin-top: 7px;
}
.td-form-group .input-label {
  font-size: 14px;
  color: rgba(8, 8, 8, 0.8);
  display: block;
  font-weight: 600;
  margin-bottom: 0.5em;
}
.dark-theme .td-form-group .input-label {
  color: rgba(255, 255, 255, 0.8);
}
.td-form-group .input-label span {
  padding-inline-start: 1px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  color: var(--td-danger);
}
.td-form-group .input-label-inner {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.td-form-group .input-label-inner > p {
  font-size: 12px;
}
.td-form-group .input-select .nice-select {
  height: 44px;
  width: 100%;
  padding: 0 15px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  float: none;
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.08);
}
.td-form-group .input-select .nice-select .current {
  text-align: end;
  font-size: 14px;
  position: relative;
  color: var(--td-white);
}
.td-form-group .input-select .nice-select .list {
  -webkit-transform: scale(1) translateY(0);
  -moz-transform: scale(1) translateY(0);
  -ms-transform: scale(1) translateY(0);
  -o-transform: scale(1) translateY(0);
  transform: scale(1) translateY(0);
  width: 100%;
  padding: 10px 0;
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
  background: #242424;
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  border-style: solid;
  border-color: rgba(255, 255, 255, 0.08);
  border-width: 1px;
  padding: 12px 12px 12px 12px;
  max-height: 300px;
  overflow-y: scroll;
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}
.td-form-group .input-select .nice-select::after {
  font-size: 16px;
  inset-inline-end: 16px;
  width: 8px;
  height: 8px;
  border-bottom: 1.5px solid var(--td-text-primary);
  border-inline-end: 1.5px solid var(--td-text-primary);
  font-size: 16px;
  content: "";
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  border: 5px solid;
  border-top-color: rgba(0, 0, 0, 0);
  border-inline-start-color: rgba(0, 0, 0, 0);
  background-color: rgba(0, 0, 0, 0);
  transition: all ease-in-out 0.2s;
  margin-top: -2px;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -o-border-radius: 2px;
  -ms-border-radius: 2px;
  border-radius: 2px;
}
.td-form-group .input-select .nice-select .option {
  font-size: 14px;
  line-height: 38px;
  min-height: 38px;
  color: var(--td-white);
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
  padding: 0 10px;
}
.td-form-group .input-select .nice-select .option.selected {
  font-weight: 500;
}
.td-form-group .input-select .nice-select .option:hover {
  background-color: #353535;
}
.td-form-group .input-select .nice-select .option.selected.focus {
  background-color: #353535;
}
.td-form-group .input-select .nice-select.open, .td-form-group .input-select .nice-select:focus {
  background-color: #353535;
}
.td-form-group .form-select {
  height: 50px;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  font-size: 14px;
}
[dir=rtl] .td-form-group .form-select {
  background-position: left 0.75rem center;
}
.td-form-group .form-select:focus {
  font-size: 14px;
}
.td-form-group.has-multiple {
  min-width: 250px;
}
.td-form-group.has-multiple .input-field-inner {
  display: flex;
  border: 1px solid rgba(8, 8, 8, 0.16);
  border-radius: 8px;
}
.dark-theme .td-form-group.has-multiple .input-field-inner {
  border-color: rgba(255, 255, 255, 0.1);
}
.td-form-group.has-multiple .input-field-inner .input-field .form-control {
  border: 0;
  height: 44px;
  background-color: transparent;
  color: #999;
}
.td-form-group.has-multiple .input-field-inner .input-field .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.td-form-group.has-multiple .input-field-inner .input-field .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.td-form-group.has-multiple .input-field-inner .input-field .form-control:-moz-placeholder {
  /* Firefox 4-18 */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.td-form-group.has-multiple .input-field-inner .input-field .form-control:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.td-form-group.has-multiple .input-field-inner .input-field .form-control::placeholder {
  /* MODERN BROWSER */
  color: #999;
  font-weight: 500;
  font-size: 14px;
}
.dark-theme .td-form-group.has-multiple .input-field-inner .input-field .form-control {
  color: #999;
}
.dark-theme .td-form-group.has-multiple .input-field-inner .input-field .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #999;
}
.dark-theme .td-form-group.has-multiple .input-field-inner .input-field .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #999;
}
.dark-theme .td-form-group.has-multiple .input-field-inner .input-field .form-control:-moz-placeholder {
  /* Firefox 4-18 */
  color: #999;
}
.dark-theme .td-form-group.has-multiple .input-field-inner .input-field .form-control:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #999;
}
.dark-theme .td-form-group.has-multiple .input-field-inner .input-field .form-control::placeholder {
  /* MODERN BROWSER */
  color: #999;
}
.td-form-group.has-multiple .input-field-inner .input-field .select2-container--default .select2-selection {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  border-radius: 0.375rem;
}
.td-form-group.has-multiple .input-field-inner .input-field .select2-container--default .select2-selection--single {
  height: 44px;
  line-height: 44px;
}
.td-form-group.has-multiple .input-field-inner .input-field .select2-dropdown {
  min-width: max-content;
}
.td-form-group.has-multiple .input-field-inner .input-field:last-child {
  position: relative;
}
.td-form-group.has-multiple .input-field-inner .input-field:last-child::before {
  position: absolute;
  content: "";
  height: calc(100% - 20px);
  top: 50%;
  transform: translateY(-50%);
  inset-inline-start: 0;
  border-inline-start: 1px solid rgba(255, 255, 255, 0.1);
}
.td-form-group .otp-verification {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px 10px;
  flex-wrap: wrap;
  max-width: max-content;
  justify-content: center;
  margin: 0 auto;
}
.td-form-group .otp-verification .control-form {
  height: 60px;
  width: 60px;
  text-align: center;
  font-size: 20px;
  line-height: 24px;
  font-weight: 700;
  background-color: rgba(34, 34, 35, 0.02);
  border: 1px solid rgba(34, 34, 35, 0.16);
  border-radius: 12px;
}
.dark-theme .td-form-group .otp-verification .control-form {
  border-color: rgba(255, 255, 255, 0.1);
  background-color: rgba(255, 255, 255, 0.02);
  color: rgba(255, 255, 255, 0.6);
}
.td-form-group .otp-verification .control-form:focus {
  border-color: var(--td-primary);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-form-group .otp-verification .control-form {
    height: 67px;
    width: 60px;
  }
}

.feedback-invalid {
  font-size: 12px;
  margin-top: 3px;
  color: var(--td-danger);
  display: none;
  font-weight: 500;
  text-align: end;
}

.input-attention {
  font-size: 12px;
  font-weight: 500;
}
.input-attention.xs {
  font-size: 10px;
}

.attachment-previews {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  margin-bottom: 15px;
}
.attachment-previews .preview {
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 10px;
  isolation: isolate;
  width: 105px;
  height: 105px;
  background: rgba(0, 0, 0, 0.04);
  border: 1px solid var(--td-border-primary);
  backdrop-filter: blur(10px);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  flex: none;
  order: 0;
  padding: 5px;
}
.attachment-previews .preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
}
.attachment-previews .preview span {
  font-size: 10px;
  word-wrap: break-word;
  position: absolute;
  bottom: 6px;
  inset-inline-start: 5px;
  width: calc(100% - 12px);
  color: var(--td-white);
  text-overflow: ellipsis;
  overflow: hidden;
  height: 1.2em;
  white-space: nowrap;
}
.attachment-previews .preview .remove {
  position: absolute;
  top: 5px;
  inset-inline-end: 5px;
  background: var(--td-danger);
  color: var(--td-white);
  border: none;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
.attachment-previews .preview .remove svg {
  background: var(--td-danger);
  fill: var(--td-white);
}

.attachment-actions .add-attachment {
  height: 120px;
  border: 1px solid var(--td-border-primary);
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  background-color: #FCFCFC;
  cursor: pointer;
  flex-direction: column;
  gap: 10px;
  font-size: 14px;
}

/*----------------------------------------
	Image Preview 
-----------------------------------------*/
.file-upload-wrap .top-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}
.file-upload-wrap .input-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #080808;
}
.file-upload-wrap #uploadItems {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.upload-custom-file {
  position: relative;
  display: inline-block;
  width: 100%;
  height: 155px;
  text-align: center;
}
.upload-custom-file input[type=file] {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 2px;
  height: 2px;
  overflow: hidden;
  opacity: 0;
}
.upload-custom-file label {
  z-index: 1;
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  bottom: 0;
  inset-inline-end: 0;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  border-radius: 8px;
  transition: transform 0.4s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  -webkit-transition: -webkit-transform 0.4s;
  -moz-transition: -moz-transform 0.4s;
  -ms-transition: -ms-transform 0.4s;
  -o-transition: -o-transform 0.4s;
  transition: transform 0.4s;
  background: rgba(255, 255, 255, 0.04);
  border: 2px dashed rgba(8, 8, 8, 0.16);
  border-radius: 8px;
}
.dark-theme .upload-custom-file label {
  border-color: rgba(255, 255, 255, 0.1);
}
.upload-custom-file label span {
  display: block;
  color: var(--td-text-primary);
  font-size: 14px;
  font-weight: 500;
  -webkit-transition: color 0.4s;
  -moz-transition: color 0.4s;
  -ms-transition: color 0.4s;
  -o-transition: color 0.4s;
  transition: color 0.4s;
}
.upload-custom-file label span b {
  color: var(--td-text-primary);
  font-weight: 500;
}
.upload-custom-file label .type-file-text {
  margin-top: 5px;
  color: #FB405A;
}
.upload-custom-file label .upload-icon {
  width: 40px;
  margin: 0 auto;
  margin-bottom: 4px;
}
.upload-custom-file label.file-ok {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: contain;
  border-color: var(--td-success);
}
.upload-custom-file label.file-ok span {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  width: 100%;
  padding: 0.3rem;
  color: hsl(0, 0%, 100%);
  background-color: rgb(255, 236, 238);
  font-weight: 500;
  font-size: 14px;
  margin: auto;
  text-decoration: none;
  color: var(--td-heading);
}
.dark-theme .upload-custom-file label.file-ok span {
  color: var(--td-white);
  background-color: rgb(54, 43, 69);
}
.upload-custom-file label.file-ok .upload-icon {
  display: none;
}
.upload-custom-file.without-image {
  height: 167px;
}
.upload-custom-file.without-image label {
  background-color: var(--td-text-primary);
}

.upload-thumb-close {
  position: absolute;
  inset-inline-end: 10px;
  top: 35px;
  z-index: 5;
  color: #FB405A;
  display: none;
}

.file-upload-close,
.input-file-close {
  position: absolute;
  top: 8px;
  inset-inline-end: 8px;
  color: #F34141;
  font-size: 18px;
  z-index: 55;
}

/*----------------------------------------*/
/* Animations styles
/*----------------------------------------*/
@keyframes popupBtn {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.4);
    opacity: 0.3;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
@keyframes sticky {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0%);
  }
}
@keyframes tdSpinner {
  from {
    -webkit-transform: rotate(0turn);
    transform: rotate(0turn);
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}
@keyframes banner-shape-anim {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-10px);
  }
}
@keyframes banner-shape-anim-2 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}
@keyframes banner-shape-anim-3 {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-40px);
  }
}
.upDown {
  animation: upDown 1s infinite alternate;
}

@keyframes upDown {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-15px);
  }
}
@keyframes animationglob {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes icon-bounce {
  0%, 100%, 20%, 50%, 80% {
    -webkit-transform: translateY(0);
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
  }
  40% {
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
    transform: translateY(-10px);
  }
  60% {
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px);
    transform: translateY(-5px);
  }
}
@keyframes animation_scale {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0.7);
  }
}
@keyframes iconltr {
  49% {
    transform: translateX(30%);
  }
  50% {
    opacity: 0;
    transform: translateX(-30%);
  }
  51% {
    opacity: 1;
  }
}
@keyframes tada {
  0% {
    transform: scaleX(1);
  }
  10% {
    transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);
  }
  20% {
    transform: scale3d(0.9, 0.9, 0.9) rotate(-3deg);
  }
  30% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  50% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  70% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }
  40% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }
  60% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }
  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }
  to {
    transform: scaleX(1);
  }
}
.tada {
  animation-name: tada;
}

/*----------------------------------------*/
/* Shortcodes styles
/*----------------------------------------*/
.radius-4 {
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
}

.radius-6 {
  -webkit-border-radius: 6px;
  -moz-border-radius: 6px;
  -o-border-radius: 6px;
  -ms-border-radius: 6px;
  border-radius: 6px;
}

.radius-8 {
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
}

.radius-10 {
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  -o-border-radius: 10px;
  -ms-border-radius: 10px;
  border-radius: 10px;
}

.radius-20 {
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
}

.radius-30 {
  -webkit-border-radius: 30px;
  -moz-border-radius: 30px;
  -o-border-radius: 30px;
  -ms-border-radius: 30px;
  border-radius: 30px;
}

.radius-40 {
  -webkit-border-radius: 40px;
  -moz-border-radius: 40px;
  -o-border-radius: 40px;
  -ms-border-radius: 40px;
  border-radius: 40px;
}

.radius-45 {
  -webkit-border-radius: 45px;
  -moz-border-radius: 45px;
  -o-border-radius: 45px;
  -ms-border-radius: 45px;
  border-radius: 45px;
}

.radius-50 {
  -webkit-border-radius: 50px;
  -moz-border-radius: 50px;
  -o-border-radius: 50px;
  -ms-border-radius: 50px;
  border-radius: 50px;
}

.radius-60 {
  -webkit-border-radius: 60px;
  -moz-border-radius: 60px;
  -o-border-radius: 60px;
  -ms-border-radius: 60px;
  border-radius: 60px;
}

.title-font {
  font-family: var(--td-ff-title);
}

.fs-12 {
  font-size: 12px;
}

.fs-14 {
  font-size: 12px;
}

.fs-16 {
  font-size: 16px;
}

.fw-3 {
  font-weight: var(--td-fw-light);
}

.fw-4 {
  font-weight: var(--td-fw-regular);
}

.fw-5 {
  font-weight: var(--td-fw-medium);
}

.fw-6 {
  font-weight: var(--td-fw-sbold);
}

.fw-7 {
  font-weight: var(--td-fw-bold);
}

.fw-8 {
  font-weight: var(--td-fw-ebold);
}

.fw-9 {
  font-weight: var(--td-fw-black);
}

.gap--5 {
  gap: 5px;
}

.gap-10 {
  gap: 10px;
}

.gap-15 {
  gap: 15px;
}

.gap-20 {
  gap: 20px;
}

.gap-24 {
  gap: 24px;
}

.hide {
  opacity: 0;
  transition: opacity 0.5s ease-out;
}

.font-xxs {
  font-size: 14px;
}

/*----------------------------------------*/
/* Background Css
/*----------------------------------------*/
.white-bg {
  background-color: var(--td-white);
}

.black-bg {
  background-color: var(--td-black);
}

.primary-bg {
  background-color: var(--td-bg-primary);
}

/*----------------------------------------*/
/* Scrollbar styles
/*----------------------------------------*/
[data-simplebar] {
  position: unset;
}

.simplebar-track {
  inset-inline-end: -2px;
}
[dir=rtl] .simplebar-track {
  inset-inline-end: auto;
  inset-inline-start: -2px;
}
.simplebar-track.simplebar-vertical {
  top: 100px;
  width: 10px;
}
.simplebar-track.simplebar-horizontal {
  visibility: hidden !important;
}

.simplebar-scrollbar:before {
  background: rgba(99, 98, 231, 0.2);
}

[data-simplebar] {
  position: unset;
}

.simplebar-mask {
  top: 80px;
}

.main-sidebar {
  height: calc(100vh - 70px);
  -webkit-transition: color 1s ease;
  transition: color 1s ease;
  overflow: auto;
  margin-top: 20px;
  margin-bottom: 50px;
}

.simplebar-offset {
  height: calc(100vh - 70px);
}

/*----------------------------------------*/
/* Preloader styles
/*----------------------------------------*/
/*----------------------------------------*/
/* Colors styles
/*----------------------------------------*/
.text-body {
  color: var(--td-body);
}

.white-text {
  color: var(--td-white) !important;
}

.black-text {
  color: var(--td-black);
}

.text-heading {
  color: var(--td-heading);
}

.primary-text {
  color: var(--td-primary);
}

.text-secondary {
  color: var(--td-secondary);
}

.text-success {
  color: var(--td-success);
}

.danger-text {
  color: var(--td-danger);
}

.text-warning {
  color: var(--td-warning);
}

.text-green {
  color: var(--td-green);
}

/*----------------------------------------*/
/* Dark Themes
/*----------------------------------------*/
.dark-theme {
  background-color: #020A22;
}
.dark-theme .header-style-one .header-logo .logo-black {
  display: none;
}
.dark-theme .header-style-one .header-logo .logo-white {
  display: block;
}
.dark-theme .footer-primary .footer-logo .logo-white {
  display: block;
}
.dark-theme .footer-primary .footer-logo .logo-black {
  display: none;
}

/* Header css*/
/*----------------------------------------*/
/*  Header actions Styles
/*----------------------------------------*/
.theme-switcher {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.currency-switcher {
  position: relative;
}
.currency-switcher .select2-container--default .select2-selection--single .select2-selection__arrow {
  inset-inline-end: -6px;
}
.currency-switcher .select2-container--default:not([dir=rtl]) .select2-selection--single .select2-selection__rendered {
  padding-inline-start: 0;
}
.currency-switcher .select2-container--default .select2-selection--single .select2-selection__rendered {
  padding-inline-end: 26px;
}
.currency-switcher .defaults-select .select2-dropdown {
  min-width: 100px;
}

.quick-action-item .action-icon {
  width: 32px;
  height: 32px;
  background-color: rgba(8, 8, 8, 0.1);
  border-radius: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--td-heading);
  font-size: 18px;
}
.dark-theme .quick-action-item .action-icon {
  color: var(--td-white);
  background: rgba(255, 255, 255, 0.1);
}
.quick-action-item .action-icon.notification-btn iconify-icon {
  -webkit-animation: tada 1.5s ease infinite;
  animation: tada 1.5s ease infinite;
}
.quick-action-item .action-icon.notification-btn.active {
  position: relative;
}
.quick-action-item .action-icon.notification-btn.active::before {
  position: absolute;
  content: "";
  height: 8px;
  width: 8px;
  background-color: #FB405A;
  border-radius: 50%;
  top: 0;
  inset-inline-end: 0;
}

.profile-card-box {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: end;
  justify-content: space-between;
  gap: 12px 12px;
  flex-wrap: wrap;
}

.profile-card .profile-title {
  font-size: clamp(2rem, 1.5rem + 2vw, 2rem);
  margin-bottom: 10px;
}

.create-project-container {
  position: relative;
}

.create-project-btn {
  padding: 10px 20px;
  background-color: #f5f7fa;
  border: 1px solid var(--td-border-primary);
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
}

.buttons-dropdown-menu {
  display: none;
  position: absolute;
  top: calc(100% + 5px);
  inset-inline-end: 0;
  width: 220px;
  background: var(--td-white);
  border: 1px solid rgba(171, 178, 225, 0.3);
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.2);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  z-index: 1;
}
.buttons-dropdown-menu ul {
  list-style: none;
  padding: 10px;
  margin: 0;
}
.buttons-dropdown-menu li {
  padding: 8px 15px;
  cursor: pointer;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}
.buttons-dropdown-menu li svg {
  height: 16px;
  width: 16px;
}
.buttons-dropdown-menu li:hover {
  background-color: var(--td-alice-blue);
}

.user-profile-drop {
  position: relative;
  cursor: pointer;
}
.user-profile-drop .dropdown-menu {
  top: calc(100% + 12px);
  inset-inline-end: 0;
  z-index: 9;
  background: var(--td-white);
  transform: translateY(-20px);
  opacity: 0;
  cursor: pointer;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
  position: absolute;
  display: block;
  width: 282px;
  border-radius: 16px;
  padding-top: 0 !important;
  background: var(--td-white);
  border: 1px solid #D1D4DA;
  backdrop-filter: blur(48.5px);
}
.dark-theme .user-profile-drop .dropdown-menu {
  border-color: #0B277A;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
}
.dark-theme .user-profile-drop .dropdown-menu {
  background-color: #171C35;
}
.user-profile-drop .dropdown-menu.show {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}
.user-profile-drop .profile-info-list {
  display: flex;
  flex-direction: column;
  padding: 16px 16px;
  gap: 2px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .user-profile-drop .profile-info-list {
    padding: 12px 12px;
  }
}
.user-profile-drop .profile-info-list .profile-info-item {
  border-radius: 8px;
  padding: 12px 15px;
  display: flex;
  gap: 8px;
  font-size: 14px;
  font-weight: 700;
}
.dark-theme .user-profile-drop .profile-info-list .profile-info-item {
  color: var(--td-text-primary);
}
.user-profile-drop .profile-info-list .profile-info-item .icon {
  display: inline-flex;
  align-items: center;
  font-size: 20px;
}
.user-profile-drop .profile-info-list .profile-info-item:hover {
  background: rgba(255, 255, 255, 0.5);
  color: var(--td-heading);
}
.user-profile-drop .profile-info-list .profile-info-item.profile-log-out {
  background-color: rgba(235, 78, 92, 0.16);
  color: var(--td-danger);
}
.user-profile-drop .profile-info-list .profile-info-item.profile-log-out .icon span {
  color: var(--td-danger);
}

.theme-switcher .dark-mode {
  display: none;
}

.dark-theme .light-mode {
  display: none;
}
.dark-theme .dark-mode {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.dark-theme .dark-mode svg * {
  stroke: rgba(255, 255, 255, 0.6);
}

/*----------------------------------------*/
/*  Header Styles
/*----------------------------------------*/
.header-transparent {
  position: absolute;
  inset-inline-start: 0;
  width: 100%;
  z-index: 99;
}

.active-sticky {
  position: fixed !important;
  top: 0;
  z-index: 111;
  inset-inline-end: 0;
  inset-inline-start: 0;
  width: 100%;
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
}

.mode-switcher button {
  position: relative;
  top: -3px;
}

.header-style-one .header-clip-path {
  position: relative;
  display: block;
  margin-top: 10px;
  padding: 1px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .header-style-one .header-clip-path {
    margin: 10px 0px 0;
  }
}
.header-style-one .header-clip-path .header-clip-path-inner {
  display: inline-flex;
  position: relative;
  width: 100%;
  z-index: 31;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .header-style-one .header-clip-path .header-clip-path-inner {
    clip-path: none;
  }
}
.header-style-one .header-clip-path .header-clip-path-inner::after {
  position: absolute;
  content: "";
  background: var(--td-white);
  clip-path: polygon(2.066% 0.568%, 99.338% 0.568%, 99.338% 0.568%, 99.344% 0.573%, 99.35% 0.587%, 99.356% 0.611%, 99.362% 0.644%, 99.367% 0.686%, 99.373% 0.737%, 99.378% 0.797%, 99.383% 0.864%, 99.388% 0.94%, 99.393% 1.024%, 99.399% 1.158%, 99.952% 14.202%, 99.952% 14.202%, 99.956% 14.309%, 99.959% 14.423%, 99.963% 14.542%, 99.966% 14.666%, 99.968% 14.794%, 99.97% 14.926%, 99.972% 15.062%, 99.973% 15.2%, 99.974% 15.341%, 99.974% 15.483%, 99.974% 65.78%, 99.974% 65.78%, 99.974% 65.931%, 99.973% 66.081%, 99.972% 66.228%, 99.97% 66.371%, 99.968% 66.51%, 99.965% 66.645%, 99.961% 66.774%, 99.958% 66.897%, 99.954% 67.013%, 99.949% 67.122%, 99.942% 67.263%, 98.081% 99.044%, 98.081% 99.044%, 98.076% 99.116%, 98.072% 99.181%, 98.067% 99.238%, 98.062% 99.289%, 98.057% 99.332%, 98.051% 99.368%, 98.046% 99.396%, 98.041% 99.416%, 98.035% 99.428%, 98.03% 99.432%, 0.11% 99.432%, 0.11% 99.432%, 0.096% 99.407%, 0.083% 99.337%, 0.071% 99.223%, 0.06% 99.071%, 0.05% 98.884%, 0.042% 98.666%, 0.035% 98.421%, 0.03% 98.152%, 0.027% 97.864%, 0.026% 97.561%, 0.026% 42.625%, 0.026% 42.625%, 0.026% 42.488%, 0.026% 42.354%, 0.028% 42.221%, 0.029% 42.091%, 0.031% 41.964%, 0.033% 41.84%, 0.036% 41.721%, 0.039% 41.606%, 0.043% 41.495%, 0.047% 41.391%, 0.053% 41.251%, 2.009% 1.066%, 2.009% 1.066%, 2.013% 0.985%, 2.017% 0.911%, 2.022% 0.844%, 2.027% 0.784%, 2.032% 0.731%, 2.037% 0.685%, 2.042% 0.646%, 2.047% 0.615%, 2.052% 0.592%, 2.058% 0.576%, 2.066% 0.568%);
  width: 100%;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .header-style-one .header-clip-path .header-clip-path-inner::after {
    clip-path: none;
  }
}
.dark-theme .header-style-one .header-clip-path .header-clip-path-inner::after {
  background: #020a22;
}
.header-style-one .header-clip-path .header-clip-path-inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
  z-index: 0;
  width: 100%;
  height: 100%;
  clip-path: polygon(2.066% 0.568%, 99.338% 0.568%, 99.338% 0.568%, 99.344% 0.573%, 99.35% 0.587%, 99.356% 0.611%, 99.362% 0.644%, 99.367% 0.686%, 99.373% 0.737%, 99.378% 0.797%, 99.383% 0.864%, 99.388% 0.94%, 99.393% 1.024%, 99.399% 1.158%, 99.952% 14.202%, 99.952% 14.202%, 99.956% 14.309%, 99.959% 14.423%, 99.963% 14.542%, 99.966% 14.666%, 99.968% 14.794%, 99.97% 14.926%, 99.972% 15.062%, 99.973% 15.2%, 99.974% 15.341%, 99.974% 15.483%, 99.974% 65.78%, 99.974% 65.78%, 99.974% 65.931%, 99.973% 66.081%, 99.972% 66.228%, 99.97% 66.371%, 99.968% 66.51%, 99.965% 66.645%, 99.961% 66.774%, 99.958% 66.897%, 99.954% 67.013%, 99.949% 67.122%, 99.942% 67.263%, 98.081% 99.044%, 98.081% 99.044%, 98.076% 99.116%, 98.072% 99.181%, 98.067% 99.238%, 98.062% 99.289%, 98.057% 99.332%, 98.051% 99.368%, 98.046% 99.396%, 98.041% 99.416%, 98.035% 99.428%, 98.03% 99.432%, 0.11% 99.432%, 0.11% 99.432%, 0.096% 99.407%, 0.083% 99.337%, 0.071% 99.223%, 0.06% 99.071%, 0.05% 98.884%, 0.042% 98.666%, 0.035% 98.421%, 0.03% 98.152%, 0.027% 97.864%, 0.026% 97.561%, 0.026% 42.625%, 0.026% 42.625%, 0.026% 42.488%, 0.026% 42.354%, 0.028% 42.221%, 0.029% 42.091%, 0.031% 41.964%, 0.033% 41.84%, 0.036% 41.721%, 0.039% 41.606%, 0.043% 41.495%, 0.047% 41.391%, 0.053% 41.251%, 2.009% 1.066%, 2.009% 1.066%, 2.013% 0.985%, 2.017% 0.911%, 2.022% 0.844%, 2.027% 0.784%, 2.032% 0.731%, 2.037% 0.685%, 2.042% 0.646%, 2.047% 0.615%, 2.052% 0.592%, 2.058% 0.576%, 2.066% 0.568%);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .header-style-one .header-clip-path .header-clip-path-inner::before {
    clip-path: none;
  }
}
.header-style-one .header-clip-path::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #091B52 0%, #0094FF 100%);
  opacity: 0.6;
  content: "";
  clip-path: polygon(2.066% 0.568%, 99.338% 0.568%, 99.338% 0.568%, 99.344% 0.573%, 99.35% 0.587%, 99.356% 0.611%, 99.362% 0.644%, 99.367% 0.686%, 99.373% 0.737%, 99.378% 0.797%, 99.383% 0.864%, 99.388% 0.94%, 99.393% 1.024%, 99.399% 1.158%, 99.952% 14.202%, 99.952% 14.202%, 99.956% 14.309%, 99.959% 14.423%, 99.963% 14.542%, 99.966% 14.666%, 99.968% 14.794%, 99.97% 14.926%, 99.972% 15.062%, 99.973% 15.2%, 99.974% 15.341%, 99.974% 15.483%, 99.974% 65.78%, 99.974% 65.78%, 99.974% 65.931%, 99.973% 66.081%, 99.972% 66.228%, 99.97% 66.371%, 99.968% 66.51%, 99.965% 66.645%, 99.961% 66.774%, 99.958% 66.897%, 99.954% 67.013%, 99.949% 67.122%, 99.942% 67.263%, 98.081% 99.044%, 98.081% 99.044%, 98.076% 99.116%, 98.072% 99.181%, 98.067% 99.238%, 98.062% 99.289%, 98.057% 99.332%, 98.051% 99.368%, 98.046% 99.396%, 98.041% 99.416%, 98.035% 99.428%, 98.03% 99.432%, 0.11% 99.432%, 0.11% 99.432%, 0.096% 99.407%, 0.083% 99.337%, 0.071% 99.223%, 0.06% 99.071%, 0.05% 98.884%, 0.042% 98.666%, 0.035% 98.421%, 0.03% 98.152%, 0.027% 97.864%, 0.026% 97.561%, 0.026% 42.625%, 0.026% 42.625%, 0.026% 42.488%, 0.026% 42.354%, 0.028% 42.221%, 0.029% 42.091%, 0.031% 41.964%, 0.033% 41.84%, 0.036% 41.721%, 0.039% 41.606%, 0.043% 41.495%, 0.047% 41.391%, 0.053% 41.251%, 2.009% 1.066%, 2.009% 1.066%, 2.013% 0.985%, 2.017% 0.911%, 2.022% 0.844%, 2.027% 0.784%, 2.032% 0.731%, 2.037% 0.685%, 2.042% 0.646%, 2.047% 0.615%, 2.052% 0.592%, 2.058% 0.576%, 2.066% 0.568%);
  border-radius: 2px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .header-style-one .header-clip-path::before {
    clip-path: none;
  }
}
.header-style-one .header-logo a {
  display: block;
}
.header-style-one .header-logo .logo-white {
  display: none;
}
.header-style-one .header-logo img {
  height: 30px;
}
@media only screen and (max-width: 35.99875rem) {
  .header-style-one .header-logo img {
    height: 26px;
    object-fit: cover;
  }
}
.header-style-one .header-inner {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  width: 100%;
  z-index: 4;
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .header-style-one .header-inner {
    padding: 14px 20px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .header-style-one .header-inner {
    padding: 16px 20px;
  }
}
.header-style-one .header-left {
  display: flex;
  align-items: center;
  column-gap: 80px;
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .header-style-one .header-left {
    column-gap: 16px;
  }
}
.header-style-one .header-right {
  display: flex;
  align-items: center;
  gap: 14px;
}
.header-style-one .header-right .header-btns-wrap {
  display: flex;
  align-items: center;
  column-gap: 16px;
}
@media only screen and (max-width: 30.06125rem) {
  .header-style-one .header-right .header-btns-wrap {
    column-gap: 8px;
  }
}
.header-style-one .header-right .header-btns-wrap .td-btn {
  height: 40px;
  min-width: 80px;
}
.header-style-one .header-right .header-quick-actions {
  column-gap: 16px;
}
@media only screen and (max-width: 30.06125rem) {
  .header-style-one .header-right .header-quick-actions {
    column-gap: 8px;
  }
}

.language-nav {
  background-color: transparent;
  position: relative;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.language-nav .translate_wrapper.active .more_lang {
  display: block;
  position: absolute;
  top: calc(100% + 32px);
  inset-inline-start: 0;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.2);
  background: #171c35;
  padding: 10px 0;
  width: 144px;
  z-index: 31;
  border-radius: 4px;
  border: 1px solid #D1D4DA;
  background: var(--td-white);
}
.dark-theme .language-nav .translate_wrapper.active .more_lang {
  background-color: #0a1229;
  border-color: #0B277A;
}
.language-nav .current_lang {
  cursor: pointer;
  text-transform: uppercase;
  overflow: hidden;
  margin-inline-end: 5px;
}
.language-nav .current_lang .lang .flag-icon {
  width: 24px;
  height: 24px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  background-size: cover;
  border-radius: 40px;
}
.language-nav .lang.selected {
  display: none;
}
.language-nav .lang span.lang-txt {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  margin-inline-end: 4px;
  font-size: 15px;
  font-weight: 500;
  color: #484848;
}
.dark-theme .language-nav .lang span.lang-txt {
  color: var(--td-white);
}
.language-nav .lang span.lang-txt svg * {
  stroke: rgba(255, 255, 255, 0.6);
}
.language-nav .lang span span {
  color: #999;
  font-weight: 400;
  margin-inline-start: 5px;
  transition: 0.3s;
}
.language-nav .more_lang {
  transform: translateY(-20px);
  opacity: 0;
  cursor: pointer;
  display: none;
  transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
  z-index: 1;
}
.language-nav .more_lang .lang {
  padding: 7px 10px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  transition: 0.3s;
}
.language-nav .more_lang .lang i {
  width: 24px;
  height: 24px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  background-size: cover;
  border-radius: 40px;
}
.language-nav .more_lang .lang:hover {
  background: #E6EFFC;
  color: var(--td-heading);
}
.dark-theme .language-nav .more_lang .lang:hover {
  color: var(--td-white);
  background: #1A2237;
}
.language-nav .more_lang .lang:hover span {
  color: var(--td-heading);
}
.dark-theme .language-nav .more_lang .lang:hover span {
  color: var(--td-white);
}
.language-nav .more_lang.active {
  opacity: 1;
  transform: translateY(0px);
}

/*----------------------------------------*/
/*  Header auth styles
/*----------------------------------------*/
.header-auth-area {
  padding: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.header-auth-quick-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: end;
}
.header-auth-quick-actions .theme-switcher-item {
  background: #fefefe;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
  width: 40px;
  height: 40px;
}
.dark-theme .header-auth-quick-actions .theme-switcher-item {
  background: #16213F;
}
.header-auth-quick-actions .language-dropdown {
  background: #fefefe;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 30px;
  width: max-content;
  height: 40px;
  min-width: 80px;
  padding-inline-start: 14px;
  padding-inline-end: 10px;
}
.dark-theme .header-auth-quick-actions .language-dropdown {
  background: #16213F;
}
.header-auth-quick-actions .language-dropdown .language-nav .current_lang {
  margin-inline-end: 0px;
}
.header-auth-quick-actions .language-nav .more_lang .lang:hover {
  background: #dddddd;
  color: var(--td-heading);
}
.dark-theme .header-auth-quick-actions .language-nav .more_lang .lang:hover {
  background: rgba(8, 8, 8, 0.3);
}
.header-auth-quick-actions .language-nav .translate_wrapper.active .more_lang {
  background: var(--td-white) !important;
  background-color: rgb(255, 255, 255);
  top: 45px;
  inset-inline-start: auto;
  inset-inline-end: 0;
}
.dark-theme .header-auth-quick-actions .language-nav .translate_wrapper.active .more_lang {
  background-color: #230c6b !important;
}
.dark-theme .header-auth-quick-actions .language-nav .current_lang .lang svg * {
  stroke: #9a9da7;
}

/* Authentication css*/
/*----------------------------------------*/
/* Auth styles
/*----------------------------------------*/
.td-authentication-section {
  min-height: 100vh;
  display: grid;
  place-items: center;
  padding: 50px 0;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-authentication-section {
    padding: 100px 0 50px;
  }
}

.auth-top-wrapper {
  margin-bottom: 25px;
  text-align: center;
}

.auth-main-box {
  width: 565px;
  margin: 0 auto;
  border-radius: 16px;
  background-color: var(--td-white);
  backdrop-filter: blur(10px);
  padding: 50px;
}
.dark-theme .auth-main-box {
  background-color: #230C6B;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .auth-main-box {
    width: 100%;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .auth-main-box {
    padding: 30px 30px;
  }
}

.auth-logo {
  margin-bottom: 25px;
}

.auth-intro-contents .title {
  font-size: 24px;
}
@media only screen and (max-width: 35.99875rem) {
  .auth-intro-contents .title {
    font-size: 20px;
  }
}
.auth-intro-contents .description {
  margin-top: 12px;
}

.auth-login-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 12px;
}

.auth-form-group {
  border-radius: 8px;
  border: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .auth-form-group {
  border-color: rgba(255, 255, 255, 0.1);
}
.auth-form-group .td-form-group {
  position: relative;
}
.auth-form-group .td-form-group:not(:last-child) {
  border-bottom: 1px solid #e1e1e1;
}
.dark-theme .auth-form-group .td-form-group:not(:last-child) {
  border-color: rgba(255, 255, 255, 0.1);
}
.auth-form-group .td-form-group .input-field .form-control {
  border: 0;
  height: 60px;
}
.dark-theme .auth-form-group .td-form-group .input-field .form-control {
  background-color: transparent;
}
.dark-theme .auth-form-group .td-form-group .input-field .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #9A9DA7;
}
.dark-theme .auth-form-group .td-form-group .input-field .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #9A9DA7;
}
.dark-theme .auth-form-group .td-form-group .input-field .form-control:-moz-placeholder {
  /* Firefox 4-18 */
  color: #9A9DA7;
}
.dark-theme .auth-form-group .td-form-group .input-field .form-control:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #9A9DA7;
}
.dark-theme .auth-form-group .td-form-group .input-field .form-control::placeholder {
  /* MODERN BROWSER */
  color: #9A9DA7;
}
.auth-form-group .td-form-group .input-field .input-group-text {
  background: transparent;
  border: 0;
  border-inline-end: 1px solid rgba(26, 26, 26, 0.16);
  margin-inline-start: 34px;
}
.auth-form-group .td-form-group .input-field.input-group .form-control {
  padding-inline-start: 16px;
}
.auth-form-group .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-inline-start: 0;
}
.auth-form-group .select2-container--default .select2-selection {
  border: 0;
}
.auth-form-group .select2-selection__rendered {
  margin-inline-start: 36px;
}
.auth-form-group .select2-container--default .select2-selection--single {
  height: 60px;
  line-height: 60px;
}
.dark-theme .auth-form-group .select2-container--default .select2-selection {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.1);
}
.dark-theme .auth-form-group .select2-dropdown {
  background-color: #341B81;
}
.auth-form-group .select2-container--default .select2-search--dropdown .select2-search__field {
  border-color: #9A9DA7;
  color: rgba(255, 255, 255, 0.16);
  color: #9A9DA7;
}

.auth-from-bottom-contents .description {
  color: #47494E;
}
.dark-theme .auth-from-bottom-contents .description {
  color: #9A9DA7;
}

/* Banner css*/
/*----------------------------------------*/
/* Banner styles
/*----------------------------------------*/
.td-banner-area.banner-style-two {
  padding-top: 160px;
  padding-bottom: 210px;
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .td-banner-area.banner-style-two {
    padding-top: 130px;
    padding-bottom: 180px;
  }
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .td-banner-area.banner-style-two {
    padding-top: 130px;
    padding-bottom: 160px;
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .td-banner-area.banner-style-two {
    padding-top: 120px;
    padding-bottom: 130px;
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-banner-area.banner-style-two {
    padding-top: 140px;
    padding-bottom: 80px;
  }
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (max-width: 35.99875rem) {
  .td-banner-area.banner-style-two {
    padding-top: 130px;
    padding-bottom: 60px;
  }
}
.td-banner-area.banner-style-two .banner-bg-thumb {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
  height: 100%;
  width: 100%;
}
.dark-theme .td-banner-area.banner-style-two .banner-bg-thumb {
  display: none;
}
.td-banner-area.banner-style-two .banner-bg-thumb img {
  width: 100%;
  height: 100%;
}
.td-banner-area.banner-style-two .drowing-line {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}
.td-banner-area.banner-style-two .shape-one {
  position: absolute;
  top: 50%;
  inset-inline-start: 110px;
  transform: translateY(-50%);
  z-index: -1;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .td-banner-area.banner-style-two .shape-one {
    inset-inline-start: 0;
  }
}
.td-banner-area.banner-style-two .shape-two {
  position: absolute;
  bottom: 84px;
  inset-inline-start: 77px;
  z-index: -1;
}
.td-banner-area.banner-style-two .shape-two img {
  width: 158px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .td-banner-area.banner-style-two .shape-two img {
    width: 60%;
  }
}
.td-banner-area.banner-style-two .shape-three {
  position: absolute;
  inset-inline-start: 35%;
  bottom: 50px;
  z-index: -1;
}
.td-banner-area.banner-style-two .shape-three img {
  width: 189px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .td-banner-area.banner-style-two .shape-three img {
    width: 60%;
  }
}
.td-banner-area.banner-style-two .shape-one-four {
  position: absolute;
  inset-inline-end: 80px;
  top: 22%;
  z-index: -1;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-banner-area.banner-style-two .shape-one-four img {
    width: 60%;
  }
}
.td-banner-area.banner-style-two .shape-torch-light {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
}
.td-banner-area.banner-style-two .banner-contents .banner-title {
  font-size: 80px;
  line-height: 1.2;
  margin-bottom: 0.9375rem;
}
@media only screen and (min-width: 100rem) and (max-width: 112.49875rem) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 64px;
  }
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 64px;
  }
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 54px;
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 44px;
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 36px;
  }
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 34px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .td-banner-area.banner-style-two .banner-contents .banner-title {
    font-size: 28px;
  }
}
.td-banner-area.banner-style-two .banner-contents .banner-title .highlight {
  background: linear-gradient(90deg, #4776E6 25.04%, #8E54E9 60.72%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: inline-block;
}
.td-banner-area.banner-style-two .banner-contents .description {
  max-width: 41.875rem;
  margin: 0 auto;
  margin-bottom: 2.1875rem;
  font-size: 1rem;
}
.td-banner-area.banner-style-two .banner-contents .banner-btns {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px 20px;
}
.td-banner-area.banner-style-two .banner-thumb {
  padding-inline-start: 20px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-banner-area.banner-style-two .banner-thumb {
    padding-inline-start: 0;
  }
}

/* Menu css*/
/*----------------------------------------*/
/* Main menu css
/*----------------------------------------*/
.bar-icon {
  width: 24px;
  height: 24px;
  background: rgb(255, 255, 255);
  border-radius: 50%;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(7, 37, 68, 0.1);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .bar-icon {
    width: 32px;
    height: 32px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -o-border-radius: 8px;
    -ms-border-radius: 8px;
    border-radius: 8px;
    background-color: #e5e5e5;
  }
}

.td-main-menu nav li {
  position: relative;
  list-style: none;
}
.td-main-menu nav > ul {
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  gap: 30px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .td-main-menu nav > ul {
    gap: 8px;
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .td-main-menu nav > ul {
    gap: 3px;
  }
}
.td-main-menu nav > ul > li > a {
  position: relative;
  font-size: 16px;
  font-weight: 600;
  line-height: 1;
  padding: 33px 5px;
  display: inline-block;
  color: #484848;
  font-family: var(--td-ff-heading);
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
}
.dark-theme .td-main-menu nav > ul > li > a {
  color: white;
}
.td-main-menu nav > ul > li.active a {
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.td-main-menu nav > ul > li:hover > a {
  -webkit-text-fill-color: transparent;
}
.td-main-menu nav > ul > li:hover > ul {
  opacity: 1;
  pointer-events: all;
}
.td-main-menu nav > ul > li:has(ul) > a::after {
  content: "\f107";
  margin-inline-start: 3px;
  position: relative;
  top: 0px;
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m12.37 15.835l6.43-6.63C19.201 8.79 18.958 8 18.43 8H5.57c-.528 0-.771.79-.37 1.205l6.43 6.63c.213.22.527.22.74 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(255, 255, 255, 0.5);
}
.td-main-menu nav > ul > li:has(li.menu-has-children) .dp-menu {
  border-radius: 24px;
}
.td-main-menu nav > ul > li:has(li.menu-has-children) .dp-menu.active {
  border-radius: 24px 0 0px 24px;
}
.td-main-menu nav ul.dp-menu {
  background-color: #171c35;
  width: 320px;
  position: absolute;
  inset-inline-start: 0px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.5s;
  border-radius: 24px;
  display: flex;
  flex-direction: column;
  z-index: 11;
}
.td-main-menu nav ul.dp-menu > li {
  padding: 0px 10px;
}
.td-main-menu nav ul.dp-menu > li:first-child {
  padding-top: 10px;
}
.td-main-menu nav ul.dp-menu > li:last-child {
  padding-bottom: 10px;
}
.td-main-menu nav ul.menu-sidebar {
  position: absolute;
  inset-inline-end: -100%;
  background: #141931;
  padding: 25px 10px 10px;
  width: 320px;
  inset-inline-start: calc(100% + 0px);
  top: 0;
  border-radius: 0px 24px 24px 0px;
  opacity: 0;
  pointer-events: none;
  transition: all 0.5s;
}
.td-main-menu nav ul.menu-sidebar .box .pair-list {
  height: 412px;
  overflow-y: scroll;
  scrollbar-width: none;
}
.td-main-menu nav ul.menu-sidebar .box .pair-list::-webkit-scrollbar {
  display: none;
}
.td-main-menu nav ul.menu-sidebar .search-box {
  position: relative;
  margin-top: 15.5px;
  margin-bottom: 10px;
}
.td-main-menu nav ul.menu-sidebar .search-box .search-icon {
  position: absolute;
  top: 50%;
  inset-inline-start: 15px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}
.td-main-menu nav ul.menu-sidebar .search-box input {
  background: #091628;
  border-radius: 100px;
  border: 1px solid rgba(0, 0, 0, 0);
  padding: 5px 16px 5px 35px;
  height: 36px;
  color: #999;
  line-height: 16px;
  font-size: 14px;
}
.td-main-menu nav ul.menu-sidebar .pair-list {
  display: flex;
  row-gap: 6px;
  flex-direction: column;
}
.td-main-menu nav ul.menu-sidebar .pair-list li a {
  border-radius: 8px;
  padding: 9px 10px;
  display: flex;
  column-gap: 12px;
  font-size: 14px;
  font-weight: 700;
}
.td-main-menu nav ul.menu-sidebar .pair-list li a .currency-icon img {
  width: 20px;
  height: 20px;
  background-size: cover;
  border-radius: 50%;
}
.td-main-menu nav ul.menu-sidebar .pair-list li a .text .eth-usdt-span {
  color: var(--td-white);
}
.td-main-menu nav ul.menu-sidebar .pair-list li a .text .eth-usdt-span-2 {
  color: rgba(255, 255, 255, 0.6);
}
.td-main-menu nav ul.menu-sidebar .pair-list li a:hover {
  background: rgba(255, 255, 255, 0.04);
}

.td-main-menu nav > ul > li ul li:has(ul) > a ::after {
  position: absolute;
  content: "\f107";
  inset-inline-end: 24px;
  top: 50%;
  transform: translateY(-50%);
  display: inline-block;
  width: 18px;
  height: 18px;
  --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='m12.37 15.835l6.43-6.63C19.201 8.79 18.958 8 18.43 8H5.57c-.528 0-.771.79-.37 1.205l6.43 6.63c.213.22.527.22.74 0'/%3E%3C/svg%3E");
  background-color: currentColor;
  -webkit-mask-image: var(--svg);
  mask-image: var(--svg);
  -webkit-mask-repeat: no-repeat;
  mask-repeat: no-repeat;
  -webkit-mask-size: 100% 100%;
  mask-size: 100% 100%;
  color: rgba(255, 255, 255, 0.5);
}
.td-main-menu nav > ul > li ul li:hover > ul {
  opacity: 1;
  pointer-events: all;
}

.menu-icon {
  width: 26px;
  height: 18px;
  position: relative;
  display: block;
}
.menu-icon::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  top: 0;
  inset-inline-start: 0;
  background: var(--td-heading);
  transition: all 0.3s;
}
.menu-icon::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 1px;
  bottom: 0;
  inset-inline-start: 0;
  background: var(--td-heading);
  transition: all 0.3s;
}
.menu-icon span {
  position: absolute;
  content: "";
  width: 18px;
  height: 1px;
  top: 50%;
  inset-inline-end: 0;
  transition: all 0.3s;
  background-color: var(--td-heading);
}

/*----------------------------------------*/
/* Mobile menu css
/*----------------------------------------*/
.mobile-menu {
  margin-bottom: 30px;
}
.mobile-menu ul {
  list-style: none;
}
.mobile-menu ul li {
  position: relative;
}
.mobile-menu ul li > a {
  padding: 14px 0;
  font-size: 15px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #484848;
  font-family: var(--td-ff-heading);
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
}
.dark-theme .mobile-menu ul li > a {
  color: var(--td-white);
}
.mobile-menu ul li > a i {
  width: 24px;
  height: 24px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #ddd;
}
.mobile-menu ul li ul {
  padding-inline-start: 5%;
}
@media only screen and (max-width: 30.06125rem) {
  .mobile-menu ul li ul {
    padding-inline-start: 3%;
  }
}
.mobile-menu ul li:not(:last-child) > a {
  border-bottom: 1px solid rgba(8, 8, 8, 0.3);
}
.mobile-menu ul li.active > a {
  -webkit-text-fill-color: transparent;
}
.mobile-menu ul li.active > .tp-menu-close {
  color: var(--td-white);
  background: var(--td-black);
  border-color: var(--td-black);
}
.mobile-menu ul li.active > .tp-menu-close i {
  -webkit-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -o-transform: rotate(90deg);
  transform: rotate(90deg);
}
.mobile-menu ul li .td-dp-menu {
  display: none;
  padding-inline-start: 20px;
}
.mobile-menu .td-mega-menu {
  padding: 0;
  padding-top: 30px;
  box-shadow: none;
  transform: inherit;
}

.td-mega-menu {
  position: absolute;
  top: 100%;
  inset-inline-start: 0;
  inset-inline-end: 0;
  opacity: 0;
  width: 100%;
  z-index: 99;
  margin: 0 auto;
  background: #fff;
  visibility: hidden;
  transform-origin: top;
  transition: 0.4s;
  transition-duration: 0.1s;
  padding: 30px 30px 10px 30px;
  transform: perspective(300px) rotateX(-18deg);
  box-shadow: 0px 10px 30px 0px rgba(25, 25, 26, 0.1);
}

/*----------------------------------------*/
/* Blog styles
/*----------------------------------------*/
.td-our-blog-section {
  background-color: #F4FAFF;
}
.dark-theme .td-our-blog-section {
  background-color: #0C142B;
}

.single-blog-item:hover .thumbnail-inner .thumbnail img {
  transform: scale(1.1);
}
.single-blog-item .thumbnail-inner {
  position: relative;
  padding: 1px;
  overflow: hidden;
}
.single-blog-item .thumbnail-inner::before, .single-blog-item .thumbnail-inner::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  opacity: 0.6;
  content: "";
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  border-radius: 2px;
}
.single-blog-item .thumbnail-inner .thumbnail {
  position: relative;
  z-index: 3;
  background: #04060a;
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  gap: 12px;
  width: 100%;
}
.single-blog-item .thumbnail-inner .thumbnail img {
  width: 100%;
}
.single-blog-item .thumbnail-inner .thumbnail::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.single-blog-item .blog-contents {
  padding-top: 16px;
}
.single-blog-item .blog-contents .blog-date {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  gap: 8px;
}
.single-blog-item .blog-contents .title {
  font-size: 20px;
}
.single-blog-item .blog-contents .title a {
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
}
.single-blog-item .blog-contents .title a:hover {
  color: var(--td-primary);
  -webkit-text-fill-color: transparent;
}
.single-blog-item .blog-contents .description {
  margin-top: 14px;
}

.read-more-btn {
  color: #4E5364;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  text-decoration-line: underline;
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: #47494E;
}
.read-more-btn:hover {
  -webkit-text-fill-color: transparent;
}

/*----------------------------------------*/
/* Postbox styles
/*----------------------------------------*/
.postbox-main-wrapper {
  padding-inline-end: 60px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .postbox-main-wrapper {
    padding-inline-end: 0;
  }
}

.postbox-details-contents p:not(:last-child) {
  margin-bottom: 30px;
}
.postbox-details-contents h2 {
  line-height: 1.27;
  margin-bottom: 16px;
}
.postbox-details-contents h3 {
  line-height: 1.27;
  margin-bottom: 16px;
}
.postbox-details-contents h5 {
  padding-bottom: 18px;
  border-bottom: 1px solid rgba(8, 8, 8, 0.1);
  margin-bottom: 18px;
}
.dark-theme .postbox-details-contents h5 {
  border-color: rgba(255, 255, 255, 0.1);
}
.postbox-details-contents h6 {
  padding-bottom: 14px;
}
.postbox-details-contents ul {
  list-style-type: disc;
  padding-inline-start: 28px;
  margin-bottom: 16px;
}
.postbox-details-contents ul span {
  font-size: 14px;
  margin: 0;
  color: var(--td-text-primary);
}
.dark-theme .postbox-details-contents ul span {
  color: #9A9DA7;
}
.postbox-details-contents ul li {
  position: relative;
  font-size: 16px;
}
.postbox-details-contents ul li:not(:last-child) {
  margin-bottom: 12px;
}
.postbox-details-contents hr {
  border: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .postbox-details-contents hr {
  border-color: rgba(255, 255, 255, 0.1);
}

.postbox-thumb-inner {
  position: relative;
  padding: 1px;
  overflow: hidden;
  margin-bottom: 25px;
}
.postbox-thumb-inner::before, .postbox-thumb-inner::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  opacity: 0.6;
  content: "";
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  border-radius: 2px;
}
.postbox-thumb-inner .thumbnail {
  position: relative;
  z-index: 3;
  background: #04060a;
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  gap: 12px;
  width: 100%;
}
.postbox-thumb-inner .thumbnail img {
  width: 100%;
}
.postbox-thumb-inner .thumbnail::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
  z-index: -1;
  width: 100%;
  height: 100%;
}

.postbox-img {
  position: relative;
  z-index: 3;
  background: #04060a;
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  gap: 12px;
  width: 100%;
}
.postbox-img::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.postbox-img img {
  width: 100%;
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
}

.postbox-meta ul {
  display: flex;
  align-items: center;
  gap: 12px 20px;
  flex-wrap: wrap;
  padding-inline-start: 0 !important;
}
.postbox-meta ul li {
  list-style: none;
  display: flex;
  align-items: center;
  gap: 6px;
  list-style: none;
  margin: 0;
  padding: 0;
  position: relative;
  border-bottom: 0;
}
.postbox-meta ul li::before {
  display: none;
}
.postbox-meta ul li:not(:last-child) {
  margin-bottom: 0;
}
.postbox-meta ul li .icon {
  font-size: 18px;
  display: inline-flex;
}
.postbox-meta ul li span {
  color: var(--td-text-primary);
}
.dark-theme .postbox-meta ul li span {
  color: #9A9DA7;
}

.postbox-share {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 30px;
}
.postbox-share h6 {
  padding-bottom: 0;
}
.postbox-share a {
  font-size: 16px;
  color: #BBBBBB;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
}
.postbox-share a:hover {
  border-color: var(--td-primary);
}

.tagcloud-items {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 8px;
  align-items: self-start;
}

.tagcloud-box {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px 8px;
}
.tagcloud-box a {
  font-size: 14px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  position: relative;
  text-transform: capitalize;
  background-color: rgba(114, 128, 255, 0.1);
  border: 1px solid rgba(114, 128, 255, 0.2);
  color: rgba(8, 8, 8, 0.6);
  padding: 5px 14px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
}
.dark-theme .tagcloud-box a {
  color: rgba(255, 255, 255, 0.6);
}

.sidebar-sticky {
  position: sticky;
  top: 80px;
}

.sidebar-widget-inner {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.sidebar-widget-content-box {
  padding: 25px 30px 30px;
  background: #f6f6ff;
  border-radius: 16px;
}
.dark-theme .sidebar-widget-content-box {
  background: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(8, 8, 8, 0.16);
}
@media only screen and (max-width: 30.06125rem) {
  .sidebar-widget-content-box {
    padding: 16px 16px 16px;
  }
}

@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .sidebar-wrapper {
    padding-inline-start: 0;
  }
}

.sidebar-widget-title {
  position: relative;
  display: inline-block;
  font-size: 23px;
  margin-bottom: 20px;
}
@media only screen and (max-width: 35.99875rem) {
  .sidebar-widget-title {
    margin-bottom: 18px;
  }
}

/*----------------------------------------*/
/* Recent post styles
/*----------------------------------------*/
.rc-post {
  padding: 12px;
  gap: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(8, 8, 8, 0.16);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
}
.dark-theme .rc-post {
  border-color: rgba(255, 255, 255, 0.16);
}
@media only screen and (max-width: 30.06125rem) {
  .rc-post {
    gap: 12px;
  }
}
.rc-post:hover .rc-post-thumb img {
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}
.rc-post:last-child {
  margin-bottom: 0;
}

.rc-post-title {
  font-size: 16px;
  overflow: hidden;
  -webkit-line-clamp: 2;
  display: box;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  white-space: normal;
  color: var(--td-heading);
  font-weight: 700;
}

.rc-post-thumb-clip {
  position: relative;
  padding: 1px;
  overflow: hidden;
  flex: 0 0 auto;
}
.rc-post-thumb-clip::before, .rc-post-thumb-clip::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  opacity: 0.6;
  content: "";
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  border-radius: 2px;
}

.rc-post-thumb {
  position: relative;
  z-index: 3;
  background: #04060a;
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  overflow: hidden;
  flex: 0 0 auto;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  -o-border-radius: 1px;
  -ms-border-radius: 1px;
  border-radius: 1px;
}
.rc-post-thumb img {
  width: 116px;
  height: 80px;
  object-fit: cover;
}

.rc-meta {
  margin-top: 3px;
}
.rc-meta span {
  font-size: 14px;
}
.rc-meta span svg, .rc-meta span i {
  margin-inline-end: 6px;
}
.rc-meta span svg {
  -webkit-transform: translateY(-2px);
  -moz-transform: translateY(-2px);
  -ms-transform: translateY(-2px);
  -o-transform: translateY(-2px);
  transform: translateY(-2px);
}
.rc-meta span:hover a {
  color: var(--td-primary);
}

/* pages css*/
/*----------------------------------------*/
/*  Dashboard styles 
/*----------------------------------------*/
.app-page-header {
  max-width: 100vw;
  position: fixed;
  top: 0;
  z-index: 33;
  -webkit-transition: 0.3s;
  transition: 0.3s;
  margin-inline-start: 290px;
  width: calc(100% - 290px);
  background-color: #020A22;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .app-page-header {
    margin-inline-start: 260px;
    width: calc(100% - 260px);
  }
}
@media only screen and (max-width: 35.99875rem) {
  .app-page-header {
    margin-inline-start: 0;
    width: 100%;
  }
}
.app-page-header.close_icon {
  margin-inline-start: 80px;
  width: calc(100% - 80px);
}
.app-page-header.dashboard-sticky {
  position: fixed;
  animation: sticky 0.3s;
  -webkit-animation: sticky 0.3s;
  top: 0;
  width: -webkit-fill-available;
  background: #131314;
}

.app-dashboard-header {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px 30px;
  padding: 6px 20px;
  height: 70px;
  border-bottom: 1px solid rgba(8, 8, 8, 0.16);
  background-color: var(--td-white);
}
.dark-theme .app-dashboard-header {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.1);
}
@media only screen and (max-width: 35.99875rem) {
  .app-dashboard-header {
    padding: 15px 15px;
    gap: 16px 16px;
  }
}
.app-dashboard-header .left-contents .user-welcome-info {
  display: flex;
  align-items: center;
  gap: 12px;
}
.app-dashboard-header .left-contents .user-welcome-info .welcome-text {
  font-size: 18px;
  font-family: var(--td-ff-body);
}
@media only screen and (max-width: 35.99875rem) {
  .app-dashboard-header .left-contents .user-welcome-info .welcome-text {
    font-size: 16px;
  }
}
@media only screen and (min-width: 0rem) and (max-width: 23.43625rem) {
  .app-dashboard-header .left-contents .user-welcome-info .welcome-text {
    display: none;
  }
}
.app-dashboard-header .right-contents .header-quick-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}
.app-dashboard-header .right-contents .header-quick-actions .language-nav .translate_wrapper.active .more_lang {
  inset-inline-start: auto;
  inset-inline-end: 0;
}
.app-dashboard-header .right-contents .header-btns-wrap {
  position: relative;
  padding-inline-end: 16px;
  margin-inline-end: 16px;
}
.app-dashboard-header .right-contents .header-btns-wrap::before {
  position: absolute;
  content: "";
  inset-inline-end: 0;
  top: -14px;
  height: 70px;
  width: 1px;
  background-color: rgba(255, 255, 255, 0.1);
}
.app-dashboard-header .right-contents .others-actions {
  display: flex;
  gap: 12px;
}

@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .page-wrapper.compact-wrapper .app-page-header {
    margin-inline-start: 0;
    width: 100%;
  }
}
.page-wrapper.compact-wrapper .app-page-body-wrapper div.app-sidebar-wrapper.close_icon ~ .app-page-body {
  margin-inline-start: 80px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.app-page-body {
  min-height: calc(100vh - 70px);
  margin-top: 70px;
  margin-inline-start: 290px;
  padding: 20px 20px 20px;
  position: relative;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .app-page-body {
    margin-inline-start: 260px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .app-page-body {
    min-height: calc(100vh - 70px);
    margin-top: 70px;
  }
}

@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .app-page-body-wrapper .app-page-body {
    margin-inline-start: 0 !important;
  }
}

.bg-overlay.active {
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.2);
  position: fixed;
  z-index: 8;
  top: 0;
}

/*----------------------------------------*/
/* Dashboard Sidebar styles
/*----------------------------------------*/
.app-sidebar-wrapper {
  position: fixed;
  height: 100%;
  top: 0;
  z-index: 9;
  line-height: inherit;
  text-align: end;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .app-sidebar-wrapper.close_icon {
    margin-inline-start: -300px;
  }
}
.app-sidebar-wrapper .sidebar-inner {
  -webkit-box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
  box-shadow: 0 0 21px 0 rgba(89, 102, 122, 0.1);
  border-inline-end: 1px solid rgba(255, 255, 255, 0.1);
  background: var(--td-heading);
}

.app-sidebar {
  width: 290px;
  height: 100%;
  inset-block-start: 0;
  inset-inline-start: 0;
  background: var(--td-white);
  border-inline-end: 1px solid rgba(8, 8, 8, 0.16);
  backdrop-filter: blur(3px);
}
.dark-theme .app-sidebar {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.1);
  background-color: #020a22;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .app-sidebar {
    width: 260px;
  }
}
.app-sidebar .main-sidebar-header {
  height: 70px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  transition: all 0.03s ease;
  border-bottom: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .app-sidebar .main-sidebar-header {
  border-color: rgba(255, 255, 255, 0.1);
}
.app-sidebar .main-sidebar-header img {
  transition: all 0.02s ease;
}
.app-sidebar .main-sidebar-header .sidebar-logo .main-logo img {
  height: 45px;
}
.app-sidebar .main-sidebar-header .sidebar-logo .main-logo.logo-white-mode {
  display: none;
}
.app-sidebar .main-sidebar-header .sidebar-logo .small-logo {
  display: none;
}
.app-sidebar .nav > ul {
  padding-inline-start: 0px;
}
.app-sidebar .nav ul li {
  list-style: none;
  margin: 0 14px;
  margin-bottom: 4px;
}
.app-sidebar .sidebar-menu {
  display: none;
}
.app-sidebar .sidebar-left,
.app-sidebar .sidebar-right {
  display: none;
}
.app-sidebar .main-menu > .slide .clip-path {
  position: relative;
  display: block;
  padding: 1px;
  background: transparent;
}
.app-sidebar .main-menu > .slide .clip-path .clip-path-inner {
  display: flex;
  position: relative;
  z-index: 3;
  background-color: var(--td-white);
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  gap: 8px;
  height: 43px;
  align-items: center;
  justify-content: start;
  color: var(--td-white);
}
.dark-theme .app-sidebar .main-menu > .slide .clip-path .clip-path-inner {
  background: #020a22;
}
.app-sidebar .main-menu > .slide .clip-path .clip-path-inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  z-index: -1;
  width: 100%;
  height: 100%;
}
.app-sidebar .main-menu > .slide .clip-path .clip-path-inner .btn-icon {
  width: 20px;
  display: inline-flex;
}
.app-sidebar .main-menu > .slide .clip-path::before, .app-sidebar .main-menu > .slide .clip-path::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  border-radius: 2px;
}
.app-sidebar .main-menu > .slide .clip-path::after {
  background: linear-gradient(to right, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  visibility: hidden;
}
.app-sidebar .main-menu > .slide .clip-path:hover::after {
  opacity: 1;
  visibility: visible;
}
.app-sidebar .main-menu > .slide .clip-path.active .inner:before {
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
}
.app-sidebar .main-menu > .slide .clip-path.active:before {
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
}
.app-sidebar .main-menu > .slide .clip-path.active:after {
  display: none;
}
.app-sidebar .main-menu > .slide.active .sidebar-menu .sidebar-menu-item:hover .side-menu-angle, .app-sidebar .main-menu > .slide:hover .sidebar-menu .sidebar-menu-item:hover .side-menu-angle {
  color: var(--td-primary) !important;
}
.app-sidebar .slide.has-sub .sidebar-menu {
  transform: translate(0, 0) !important;
  visibility: visible !important;
}
.app-sidebar .slide.has-sub {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
}
.app-sidebar .slide.has-sub.open > .sidebar-menu-item .side-menu-angle {
  transform: rotate(180deg);
}
.app-sidebar .slide.has-sub {
  display: -ms-grid;
  display: -moz-grid;
  display: grid;
}
.app-sidebar .slide.has-sub.open > .sidebar-menu-item .side-menu-angle {
  transform: rotate(180deg);
}
.app-sidebar .slide.active .clip-path {
  position: relative;
  display: block;
  padding: 1px;
  background: transparent;
}
.app-sidebar .slide.active .clip-path .clip-path-inner::before {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) 11.16%, rgba(142, 84, 233, 0.3) 100%);
}
.app-sidebar .slide.active .clip-path::before, .app-sidebar .slide.active .clip-path::after {
  background: linear-gradient(to right, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
}
.app-sidebar .slide.active .clip-path::after {
  background: linear-gradient(to left, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  opacity: 0;
  visibility: hidden;
}
.app-sidebar .slide.active .clip-path:hover::after {
  opacity: 1;
  visibility: visible;
}
.app-sidebar .slide.active .clip-path.active .inner:before {
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
}
.app-sidebar .slide.active .clip-path.active:before {
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
}
.app-sidebar .slide.active .clip-path.active:after {
  display: none;
}
.app-sidebar .slide.active.active .sidebar-menu .sidebar-menu-item:hover .side-menu-angle, .app-sidebar .slide.active:hover .sidebar-menu .sidebar-menu-item:hover .side-menu-angle {
  color: var(--td-primary) !important;
}
.app-sidebar .slide.active .sidebar-menu-item .side-menu-icon {
  color: var(--td-heading);
}
.dark-theme .app-sidebar .slide.active .sidebar-menu-item .side-menu-icon {
  color: var(--td-white);
}
.app-sidebar .slide.active .sidebar-menu-item .sidebar-menu-label {
  color: var(--td-heading);
}
.dark-theme .app-sidebar .slide.active .sidebar-menu-item .sidebar-menu-label {
  color: var(--td-white);
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item:hover,
.app-sidebar .sidebar-menu.child2 .sidebar-menu-item:hover,
.app-sidebar .sidebar-menu.child3 .sidebar-menu-item:hover {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-category .category-name {
  color: var(--td-secondary);
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  padding: 4px 10px;
  white-space: nowrap;
  position: relative;
  margin-top: 15px;
  display: block;
}
[dir=rtl] .app-sidebar .sidebar-menu-category .category-name {
  text-align: right;
}
.app-sidebar .sidebar-menu-item {
  padding: 12px 16px;
  position: relative;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
  border: 1px solid transparent;
  width: 100%;
}
.app-sidebar .sidebar-menu-item:hover {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) 11.16%, rgba(142, 84, 233, 0.3) 100%);
}
.app-sidebar .sidebar-menu-item:hover .sidebar-menu-label {
  color: var(--td-heading);
}
.app-sidebar .sidebar-menu-item:hover .side-menu-icon {
  color: var(--td-heading);
}
.app-sidebar .sidebar-menu-item.active {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-item.active .sidebar-menu-label,
.app-sidebar .sidebar-menu-item.active .side-menu-angle {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu-item.active .side-menu-icon {
  color: var(--td-primary);
}
.app-sidebar .sidebar-menu {
  padding: 0;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item {
  padding: 6px 6px;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item {
  background-color: transparent !important;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item:before, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item:before, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item:before {
  position: absolute;
  content: "\e404";
  font-family: "Font Awesome 6 Pro";
  font-size: 12px;
  inset-inline-start: -10px;
  opacity: 0.8;
}
.app-sidebar .sidebar-menu.child1 .sidebar-menu-item.active, .app-sidebar .sidebar-menu.child2 .sidebar-menu-item.active, .app-sidebar .sidebar-menu.child3 .sidebar-menu-item.active {
  background-color: transparent !important;
}
.app-sidebar .sidebar-menu.child1 li, .app-sidebar .sidebar-menu.child2 li, .app-sidebar .sidebar-menu.child3 li {
  padding: 0;
  position: relative;
}
.app-sidebar .sidebar-menu.child1 li {
  padding-inline-start: 56px;
}
.app-sidebar .sidebar-menu.child2 li {
  padding-inline-start: 12px;
}
.app-sidebar .sidebar-menu.child3 li {
  padding-inline-start: 16px;
}
.app-sidebar .sidebar-menu-label {
  white-space: nowrap;
  position: relative;
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  text-transform: capitalize;
  transition: 0.3s;
  color: #646465;
}
.dark-theme .app-sidebar .sidebar-menu-label {
  color: #999;
}
.app-sidebar .side-menu-icon {
  line-height: 0;
  font-size: 20px;
  text-align: center;
  color: #646465;
}
.app-sidebar .side-menu-icon svg {
  width: 16px;
  height: 16px;
}
.app-sidebar .side-menu-icon svg * {
  transition: 0.3s;
  width: 16px;
  height: 16px;
  stroke: #999;
}
.app-sidebar .side-menu-angle {
  transform-origin: center;
  position: absolute;
  inset-inline-end: 20px;
  line-height: 1;
  font-size: 14px;
  transition: all 0.03s ease;
  opacity: 0.8;
}

@media only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (max-width: 35.99875rem) {
  .close_sidebar.app-sidebar {
    inset-inline-start: 0px;
  }
}

@media only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (max-width: 35.99875rem) {
  .app-sidebar {
    inset-inline-start: -300px;
  }
}
.app-sidebar.collapsed {
  inset-inline-start: -300px;
}
.app-sidebar.nav-folded {
  width: 80px;
  transition: 0.2s;
}
.app-sidebar.nav-folded .nav ul li {
  margin: 0 10px;
}
.app-sidebar.nav-folded .category-name {
  display: none;
}
.app-sidebar.nav-folded .sidebar-menu-item {
  padding: 10px 16px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
}
.app-sidebar.nav-folded .sidebar-menu-item .sidebar-menu-label,
.app-sidebar.nav-folded .sidebar-menu-item .category-name {
  display: none;
}
.app-sidebar.nav-folded .sidebar-logo .main-logo {
  display: none;
}
.app-sidebar.nav-folded .sidebar-logo .small-logo {
  display: block;
}
.app-sidebar.nav-folded .invite-card-content {
  display: none;
}
.app-sidebar.nav-folded .invite-card-box {
  padding: 8px;
  margin: 10px 10px;
}
.app-sidebar .app-sidebar.nav-folded.side-nav-hover .sidebar-menu-category .category-name {
  display: block;
}
.app-sidebar.side-nav-hover {
  width: 290px;
  transition: all 0.3s ease;
}
.app-sidebar.side-nav-hover .sidebar-menu-item .sidebar-menu-label {
  display: none;
}
.app-sidebar.side-nav-hover .sidebar-menu-category .category-name {
  display: block !important;
}
.app-sidebar.side-nav-hover .sidebar-menu-item {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
.app-sidebar.side-nav-hover .sidebar-logo .main-logo {
  display: block;
}
.app-sidebar.side-nav-hover .sidebar-logo .small-logo {
  display: none;
}
.app-sidebar.side-nav-hover .sidebar-menu-item .sidebar-menu-label {
  display: block;
}
.app-sidebar.side-nav-hover .invite-card-box {
  padding: 8px 8px 8px 16px;
}
.app-sidebar.side-nav-hover .invite-card-content {
  display: block;
  transition: 0.2s ease;
  opacity: 1;
}

.toggle-sidebar {
  position: absolute;
  top: 60px;
  inset-inline-end: -10px;
  z-index: 5;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .toggle-sidebar {
    position: relative;
    top: inherit;
    inset-inline-end: inherit;
    z-index: 5;
  }
}
.toggle-sidebar.active .bar-icon {
  transform: rotate(-180deg);
}

/*----------------------------------------*/
/*  Dashboard default card styles
/*----------------------------------------*/
.default-card {
  background: #091628;
  border-radius: 24px;
}
.default-card .card-heading {
  padding: 18px 30px 18px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
@media only screen and (max-width: 35.99875rem) {
  .default-card .card-heading {
    padding: 18px 20px 18px;
  }
}
.default-card .card-inner {
  padding: 30px 30px 30px;
}
@media only screen and (max-width: 35.99875rem) {
  .default-card .card-inner {
    padding: 20px 20px 20px;
  }
}

.card-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}
.card-heading .title {
  font-size: 20px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .card-heading .title {
    font-size: 18px;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .card-heading .title {
    font-size: 16px;
  }
}
.card-heading .link {
  font-weight: 600;
  color: var(--td-white);
}

.verify-status {
  border-radius: 100px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: #FB405A;
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  gap: 8px;
}
.verify-status .icon {
  flex: 0 0 auto;
  color: var(--td-white);
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-self: center;
}
.verify-status .contents p {
  font-size: 16px;
  font-weight: 500;
  color: var(--td-white);
}

/*----------------------------------------*/
/* Footer primary style
/*----------------------------------------*/
.footer-primary {
  position: relative;
  z-index: 5;
  background: #E6EFFC;
}
.dark-theme .footer-primary {
  background-color: #020A22;
}
.footer-primary .footer-bg {
  position: absolute;
  bottom: 0;
  inset-inline-start: 0;
  z-index: -1;
  width: 448px;
  height: 100%;
  background-repeat: no-repeat;
  background-position: left;
  background-size: 100% 100%;
  opacity: 0.3;
}
.footer-primary .footer-main {
  padding: 80px 0 60px;
}
.footer-primary .footer-logo .logo-white {
  display: none;
}

.footer-widget.footer-col-one {
  max-width: 260px;
}
.footer-widget.footer-col-two {
  padding-inline-start: 90px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .footer-widget.footer-col-two {
    padding-inline-start: 35px;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .footer-widget.footer-col-two {
    padding-inline-start: 0px;
  }
}
.footer-widget.footer-col-three {
  padding-inline-start: 50px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .footer-widget.footer-col-three {
    padding-inline-start: 0px;
  }
}
.footer-widget.footer-col-four {
  padding-inline-start: 20px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .footer-widget.footer-col-four {
    padding-inline-start: 0px;
  }
}

.footer-wg-head .title {
  color: var(--td-heading);
  margin-bottom: 14px;
  font-size: 20px;
  position: relative;
  padding-bottom: 14px;
  font-weight: 700;
}
.footer-wg-head .title::before {
  position: absolute;
  content: "";
  height: 1px;
  width: 54px;
  bottom: 0;
  inset-inline-start: 0;
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
}

.footer-links ul li {
  list-style: none;
}
.footer-links ul li:not(:last-child) {
  margin-bottom: 12px;
}
.footer-links ul li a {
  font-size: 16px;
  font-weight: 600;
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: #47494E;
}
.dark-theme .footer-links ul li a {
  color: #9A9DA7;
}
.footer-links ul li a:hover {
  -webkit-text-fill-color: transparent;
}

.footer-social {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 16px;
  flex-direction: column;
}
.footer-social ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 16px;
}
.footer-social ul li {
  list-style: none;
}
.footer-social ul li a {
  font-size: 20px;
}

.footer-info .info-item {
  display: flex;
  align-items: self-start;
  gap: 10px;
}
.footer-info .info-item:not(:last-child) {
  margin-bottom: 18px;
}
.footer-info .info-item .icon {
  width: 20px;
  height: 20px;
  flex: 0 0 auto;
}
.footer-info .info-item .text a {
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
}
.footer-info .info-item .text a:hover {
  -webkit-text-fill-color: transparent;
}

/*----------------------------------------*/
/* Footer copyright style
/*----------------------------------------*/
.footer-copyright {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px 10px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 20px 0;
}
.dark-theme .footer-copyright {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .footer-copyright {
    justify-content: center;
  }
}

.copyright-link ul {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}
.copyright-link ul li {
  list-style: none;
  font-size: 14px;
  font-weight: 500;
}
.copyright-link ul li a:hover {
  color: var(--td-primary);
}

.copyright-text p {
  font-size: 16px;
  font-weight: 500;
}

/* pages css*/
/*----------------------------------------*/
/* Contact styles
/*----------------------------------------*/
.contact-form-left {
  padding-inline-end: 60px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .contact-form-left {
    padding-inline-end: 30px;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .contact-form-left {
    padding-inline-end: 0;
  }
}

.contact-foem-title {
  font-size: 25px;
  text-align: center;
  margin-bottom: 20px;
}

.contact-form-box {
  position: relative;
  margin-inline-start: 50px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .contact-form-box {
    margin-inline-start: 30px;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .contact-form-box {
    margin-inline-start: 0;
  }
}
.contact-form-box .clip-path {
  position: relative;
  padding: 1px;
  height: 100%;
}
.contact-form-box .clip-path-inner {
  position: relative;
  z-index: 3;
  padding: 40px 40px;
  height: 100%;
}
.contact-form-box .clip-path-inner::before {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  clip-path: polygon(0% 1.349%, 0% 98.651%, 0% 98.651%, 0.016% 98.87%, 0.063% 99.077%, 0.139% 99.271%, 0.24% 99.448%, 0.364% 99.605%, 0.509% 99.74%, 0.671% 99.849%, 0.85% 99.931%, 1.041% 99.982%, 1.242% 100%, 93.955% 100%, 93.955% 100%, 94.044% 99.997%, 94.132% 99.986%, 94.219% 99.969%, 94.304% 99.946%, 94.387% 99.916%, 94.469% 99.879%, 94.547% 99.837%, 94.623% 99.788%, 94.696% 99.734%, 94.766% 99.673%, 99.568% 95.181%, 99.568% 95.181%, 99.647% 95.101%, 99.719% 95.014%, 99.782% 94.921%, 99.839% 94.823%, 99.887% 94.721%, 99.927% 94.614%, 99.959% 94.504%, 99.981% 94.391%, 99.995% 94.276%, 100% 94.159%, 100% 1.349%, 100% 1.349%, 99.984% 1.13%, 99.937% 0.923%, 99.861% 0.729%, 99.76% 0.552%, 99.636% 0.395%, 99.491% 0.26%, 99.329% 0.151%, 99.15% 0.069%, 98.959% 0.018%, 98.758% 0%, 50% 0%, 1.242% 0%, 1.242% 0%, 1.041% 0.018%, 0.85% 0.069%, 0.671% 0.151%, 0.509% 0.26%, 0.364% 0.395%, 0.24% 0.552%, 0.139% 0.729%, 0.063% 0.923%, 0.016% 1.13%, 0% 1.349%);
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
  z-index: -1;
  border-radius: 16px;
}
.contact-form-box .bg-shape {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.td-contact-info-section {
  background-color: #E6EFFC;
}
.dark-theme .td-contact-info-section {
  background-color: #0C142B;
}

.contact-info-item {
  position: relative;
  text-align: center;
}
.contact-info-item .icon {
  margin-bottom: 25px;
  width: 100px;
  height: 100px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid rgba(8, 8, 8, 0.1);
}
.dark-theme .contact-info-item .icon {
  border-color: rgba(255, 255, 255, 0.1);
}
.contact-info-item .icon img {
  width: 50px;
}
.contact-info-item .contents .title {
  font-size: 24px;
}
.contact-info-item .contents .info {
  margin-top: 12px;
}
.contact-info-item .contents .info a {
  color: #484848;
  font-family: var(--td-ff-heading);
  background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
  -webkit-background-clip: text;
  background-clip: text;
}
.dark-theme .contact-info-item .contents .info a {
  color: rgba(255, 255, 255, 0.7);
}
.contact-info-item .contents .info a:hover {
  color: var(--td-primary);
  -webkit-text-fill-color: transparent;
}

.contact-info-main .row [class*=col-]:not(:last-child) .contact-info-item::after {
  position: absolute;
  content: "";
  height: 100%;
  width: 1px;
  background-color: rgba(8, 8, 8, 0.1);
  inset-inline-end: -15%;
  top: 0;
}
.dark-theme .contact-info-main .row [class*=col-]:not(:last-child) .contact-info-item::after {
  background-color: rgba(255, 255, 255, 0.1);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .contact-info-main .row [class*=col-]:not(:last-child) .contact-info-item::after {
    display: none;
  }
}

/*----------------------------------------*/
/* No data found styles
/*----------------------------------------*/
.no-data-found {
  text-align: center;
  padding: 0.625rem 0.625rem;
  margin: 0 auto;
}
.no-data-found img {
  width: 50px;
  height: 50px;
  margin-bottom: 8px;
}
.no-data-found span {
  font-size: 16px;
  font-weight: 700;
  display: block;
}

/*----------------------------------------*/
/* Notifications styles
/*----------------------------------------*/
.notifications-list-wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.notifications-list-grid {
  display: grid;
  align-items: center;
  grid-template-columns: 1fr auto;
  gap: 12px 16px;
  background-color: #FBF9FE;
  border: 1px solid rgba(8, 8, 8, 0.1);
  border-radius: 0.375rem;
  padding: 19px 20px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .notifications-list-grid {
    display: flex;
    flex-direction: column;
    align-items: start;
  }
}
.dark-theme .notifications-list-grid {
  background-color: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.1);
}
.notifications-list-grid:nth-child(1) .notifications-lists a .notification-item .icon {
  background-color: #88333C;
}
.notifications-list-grid:nth-child(2) .notifications-lists a .notification-item .icon {
  background-color: #33884D;
}
.notifications-list-grid:nth-child(3) .notifications-lists a .notification-item .icon {
  background-color: #33884D;
}
.notifications-list-grid:nth-child(4) .notifications-lists a .notification-item .icon {
  background-color: #808833;
}
.notifications-list-grid:nth-child(5) .notifications-lists a .notification-item .icon {
  background-color: #6596F4;
}
.notifications-list-grid:nth-child(6) .notifications-lists a .notification-item .icon {
  background-color: #800AF6;
}
.notifications-list-grid:nth-child(6n+1) .notifications-lists a .notification-item .icon {
  background-color: #00B4D8;
}
.notifications-list-grid:nth-child(6n+2) .notifications-lists a .notification-item .icon {
  background-color: #E56BFF;
}
.notifications-list-grid:nth-child(6n+3) .notifications-lists a .notification-item .icon {
  background-color: #29B475;
}
.notifications-list-grid:nth-child(6n+4) .notifications-lists a .notification-item .icon {
  background-color: #808833;
}
.notifications-list-grid:nth-child(6n+5) .notifications-lists a .notification-item .icon {
  background-color: #6596F4;
}
.notifications-list-grid:nth-child(6n+6) .notifications-lists a .notification-item .icon {
  background-color: #800AF6;
}

.notification-panel-box {
  position: relative;
}

.notification-panel {
  width: 440px;
  position: absolute;
  top: calc(100% + 12px);
  visibility: hidden;
  display: block;
  inset-inline-end: 0;
  z-index: 9;
  transform: translateY(-20px);
  opacity: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
  border-radius: 8px;
  border: 1px solid #E1E4EA;
  background: var(--td-white);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08), 0px 8px 26px 0px rgba(0, 0, 0, 0.12);
  overflow: hidden;
}
.dark-theme .notification-panel {
  border-color: #0B277A;
}
@media only screen and (max-width: 35.99875rem) {
  .notification-panel {
    width: 400px;
    inset-inline-end: 0px;
  }
}
@media only screen and (min-width: 26.5625rem) and (max-width: 29.99875rem) {
  .notification-panel {
    width: 355px;
    inset-inline-end: 0px;
  }
}
@media only screen and (min-width: 23.4375rem) and (max-width: 26.56125rem) {
  .notification-panel {
    width: 340px;
    inset-inline-end: -30px;
  }
}
@media only screen and (min-width: 0rem) and (max-width: 23.43625rem) {
  .notification-panel {
    width: 300px;
    inset-inline-end: -50px;
  }
}
.dark-theme .notification-panel {
  background: #171C35;
}
.notification-panel.active {
  visibility: visible;
  opacity: 1;
  transform: translateY(0px);
}
.notification-panel .notification-item:not(:last-child) {
  margin-bottom: 12px;
}

.notifications-inner {
  padding: 10px 20px 10px;
  height: 360px;
  overflow-y: scroll;
  margin-inline-end: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.notifications-inner::-webkit-scrollbar {
  width: 0.3125rem;
}
.notifications-inner::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.12);
}
.notifications-inner::-webkit-scrollbar-thumb {
  background-image: linear-gradient(125deg, #555 0%, #555 100%);
  border-radius: 0.625rem;
}

.notification-header {
  background: #FBF9F7;
}
.dark-theme .notification-header {
  background: #1c2339;
}
.notification-header .heading-top {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 16px 10px;
}
.notification-header .heading-top .title {
  font-size: 18px;
  font-family: var(--td-ff-body);
  font-weight: 700;
}

.notification-btn-close {
  width: 32px;
  height: 32px;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  z-index: 2;
  font-size: 20px;
  background-color: rgba(8, 8, 8, 0.1);
}
.dark-theme .notification-btn-close {
  background-color: rgba(8, 8, 8, 0.3);
}

.notifications-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}
@media only screen and (max-width: 35.99875rem) {
  .notifications-top {
    margin-bottom: 12px;
  }
}
.notifications-top button {
  font-size: 14px;
  font-weight: 500;
}
.notifications-top button:hover {
  color: var(--td-white);
}

.notifications-middle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px 10px;
}
.notifications-middle .middle-buttons {
  display: flex;
  align-items: center;
  gap: 20px;
}
@media only screen and (max-width: 35.99875rem) {
  .notifications-middle .middle-buttons {
    gap: 10px;
  }
}

.notification-list {
  display: flex;
}
.notification-list .notification-item {
  display: flex;
  align-items: self-start;
  column-gap: 12px;
}
.notification-list .notification-item .icon {
  width: 34px;
  height: 34px;
  flex: 0 0 auto;
  border-radius: 50%;
  background: #88333C;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: var(--td-white);
}
.notification-list .notification-item .icon img {
  width: 18px;
}
.notification-list .notification-item .contents .title {
  font-weight: 700;
  font-size: 16px;
}
.notification-list .notification-item .contents .message {
  margin-top: 5px;
  font-size: 14px;
  margin-bottom: 0;
}
.notification-list .notification-item .contents .time {
  font-size: 12px;
  margin-top: 10px;
  display: block;
}

.notifications-lists {
  display: flex;
  flex-direction: column;
  row-gap: 18px;
}
.notifications-lists a:nth-child(1) .notification-item .icon {
  background-color: #88333C;
}
.notifications-lists a:nth-child(2) .notification-item .icon {
  background-color: #33884D;
}
.notifications-lists a:nth-child(3) .notification-item .icon {
  background-color: #337788;
}
.notifications-lists a:nth-child(4) .notification-item .icon {
  background-color: #808833;
}
.notifications-lists a:nth-child(5) .notification-item .icon {
  background-color: #6596F4;
}
.notifications-lists a:nth-child(6) .notification-item .thumb {
  background-color: #800AF6;
}
.notifications-lists a:nth-child(6n+1) .notification-item .icon {
  background-color: #00B4D8;
}
.notifications-lists a:nth-child(6n+2) .notification-item .thumb {
  background-color: #E56BFF;
}
.notifications-lists a:nth-child(6n+3) .notification-item .icon {
  background-color: #29B475;
}
.notifications-lists a:nth-child(6n+4) .notification-item .icon {
  background-color: #E74B54;
}
.notifications-lists a:nth-child(6n+5) .notification-item .icon {
  background-color: #6596F4;
}
.notifications-lists a:nth-child(6n+6) .notification-item .icon {
  background-color: #9e69d2;
}

.notifications-box .notifications-drop-btn::after {
  display: none;
}
.notifications-box .dropdown-menu {
  background: #101016;
  backdrop-filter: blur(50px);
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  width: 442px;
  top: 150% !important;
  padding: 24px 24px 24px;
  border: 0;
  inset-inline-end: 0;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .notifications-box .dropdown-menu {
    transform: translateX(35%);
  }
}
@media only screen and (max-width: 30.06125rem) {
  .notifications-box .dropdown-menu {
    transform: translateX(45%);
  }
}
@media only screen and (max-width: 30.06125rem) {
  .notifications-box .dropdown-menu {
    width: 350px;
    padding: 15px 15px;
  }
}
@media (max-width: 360px) {
  .notifications-box .dropdown-menu {
    width: 300px;
    padding: 15px 15px;
    inset-inline-end: -15px;
  }
}
.notifications-box .dropdown-menu:before {
  position: absolute;
  content: "";
  inset: 0;
  -webkit-border-radius: 20px;
  -moz-border-radius: 20px;
  -o-border-radius: 20px;
  -ms-border-radius: 20px;
  border-radius: 20px;
  padding: 2px;
  background: linear-gradient(139.9deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
.notifications-box .notifications-top-content {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 27px;
}
@media only screen and (max-width: 30.06125rem) {
  .notifications-box .notifications-top-content {
    margin-bottom: 17px;
  }
}
.notifications-box .notifications-top-content .title {
  color: var(--td-white);
  font-size: 16px;
  font-weight: 800;
}
@media only screen and (max-width: 30.06125rem) {
  .notifications-box .notifications-top-content .title {
    font-size: 14px;
  }
}
.notifications-box .notifications-top-content .link {
  color: var(--td-white);
  font-size: 14px;
  font-weight: 500;
}
@media only screen and (max-width: 30.06125rem) {
  .notifications-box .notifications-top-content .link {
    font-size: 12px;
  }
}
.notifications-box .notifications-top-content .link:hover {
  color: var(--td-primary);
}
.notifications-box .notifications-info-wrapper {
  height: 280px;
  overflow-y: scroll;
  scrollbar-width: thin;
  padding-inline-end: 5px;
}
.notifications-box .notifications-info-list ul li {
  list-style: none;
}
.notifications-box .notifications-info-list ul li .list-item {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: start;
  gap: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  padding: 10px 10px;
}
.notifications-box .notifications-info-list ul li .list-item .content .title {
  font-size: 14px;
  font-weight: 700;
  color: var(--td-white);
}
@media only screen and (max-width: 30.06125rem) {
  .notifications-box .notifications-info-list ul li .list-item .content .title {
    font-size: 12px;
  }
}
.notifications-box .notifications-info-list ul li .list-item .content .info {
  font-size: 11px;
  color: var(--td-white);
}
.notifications-box .notifications-info-list ul li .list-item:hover {
  background: rgba(255, 255, 255, 0.1);
}
.notifications-box .notifications-info-list ul li:not(:last-child) {
  margin-bottom: 6px;
}
.notifications-box .notifications-bottom-content {
  margin-top: 24px;
}
@media only screen and (max-width: 30.06125rem) {
  .notifications-box .notifications-bottom-content {
    margin-top: 14px;
  }
}
.notifications-box .notifications-bottom-content .notifications-btn {
  background: rgba(255, 255, 255, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.08);
  -webkit-border-radius: 12px;
  -moz-border-radius: 12px;
  -o-border-radius: 12px;
  -ms-border-radius: 12px;
  border-radius: 12px;
  width: 100%;
  display: -webkit-inline-box;
  display: -moz-inline-box;
  display: -ms-inline-flexbox;
  display: -webkit-inline-flex;
  display: inline-flexbox;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 38px;
  color: var(--td-white);
  font-size: 14px;
  font-weight: 800;
}

/*----------------------------------------*/
/* Mining checkout styles
/*----------------------------------------*/
.buy-mining-plan-info {
  display: flex;
  align-items: start;
  gap: 16px;
}
@media only screen and (max-width: 35.99875rem) {
  .buy-mining-plan-info {
    flex-direction: column;
  }
}
.buy-mining-plan-info .icon {
  flex: 0 0 auto;
}
.buy-mining-plan-info .icon img {
  width: 35px;
  height: 35px;
}
.buy-mining-plan-info .contents .title {
  font-size: 20px;
}
.buy-mining-plan-info .contents .description {
  margin-top: 10px;
}

.buy-mining-plan-contents,
.buy-mining-plan-form {
  padding: 30px 40px;
}
@media only screen and (max-width: 35.99875rem) {
  .buy-mining-plan-contents,
  .buy-mining-plan-form {
    padding: 20px 20px;
  }
}

.buy-mining-plan-form {
  border-top: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .buy-mining-plan-form {
  border-color: rgba(255, 255, 255, 0.16);
}

.checkout-payment-info-item {
  padding: 30px 20px;
  display: flex;
  align-content: center;
  justify-content: space-between;
}
.checkout-payment-info-item:not(:last-child) {
  border-bottom: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .checkout-payment-info-item:not(:last-child) {
  border-color: rgba(255, 255, 255, 0.16);
}
.checkout-payment-info-item.has-bg-color {
  background-color: #f6f6f6;
  margin: 0 1px;
}
.dark-theme .checkout-payment-info-item.has-bg-color {
  background-color: #161F3B;
}

.payment-method-supported {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}
.payment-method-supported .card-item img {
  width: 29px;
  height: 20px;
  border-radius: 3px;
}

.checkout-bank-form .td-form-group .input-field .form-control::-webkit-input-placeholder,
.buy-mining-plan-form .td-form-group .input-field .form-control::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: #9A9DA7;
}
.checkout-bank-form .td-form-group .input-field .form-control::-moz-placeholder,
.buy-mining-plan-form .td-form-group .input-field .form-control::-moz-placeholder {
  /* Firefox 19+ */
  color: #9A9DA7;
}
.checkout-bank-form .td-form-group .input-field .form-control:-moz-placeholder,
.buy-mining-plan-form .td-form-group .input-field .form-control:-moz-placeholder {
  /* Firefox 4-18 */
  color: #9A9DA7;
}
.checkout-bank-form .td-form-group .input-field .form-control:-ms-input-placeholder,
.buy-mining-plan-form .td-form-group .input-field .form-control:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: #9A9DA7;
}
.checkout-bank-form .td-form-group .input-field .form-control::placeholder,
.buy-mining-plan-form .td-form-group .input-field .form-control::placeholder {
  /* MODERN BROWSER */
  color: #9A9DA7;
}

/*----------------------------------------*/
/* Page contents styles
/*----------------------------------------*/
.td-privacy-policy-section,
.td-terms-conditions-section {
  padding-top: 50px;
}

.td-page-contents h3 {
  font-size: 38px;
  line-height: 1.3;
  margin-bottom: 20px;
}
.td-page-contents h4 {
  font-size: 24px;
  margin-bottom: 16px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .td-page-contents h4 {
    font-size: 18px;
    line-height: 1.5;
  }
}
.td-page-contents p {
  line-height: 28px;
}
.td-page-contents p:not(:last-child) {
  margin-bottom: 30px;
}
.td-page-contents span {
  color: var(--td-heading);
  margin-bottom: 10px;
  display: block;
}
.td-page-contents b:not(:last-child) {
  margin-bottom: 30px;
  display: block;
}
.td-page-contents ul {
  margin-bottom: 40px;
  list-style-type: disc;
  padding-inline-start: 28px;
}
.td-page-contents ul li {
  margin-bottom: 14px;
  color: var(--td-text-primary);
}
.td-page-contents a {
  font-size: 17px;
  font-weight: 600;
  line-height: 27px;
  color: var(--td-primary);
  text-decoration: underline;
}
.td-page-contents a:hover {
  text-decoration: none;
}

/*----------------------------------------*/
/* Mining responsibly styles
/*----------------------------------------*/
.mining-responsibly-item {
  position: relative;
  padding: 1px;
  display: block;
  z-index: 5;
}
.mining-responsibly-item::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  opacity: 0.6;
  content: "";
  clip-path: polygon(0.457% 0%, 99.543% 0%, 99.543% 0%, 99.617% 0.009%, 99.688% 0.035%, 99.753% 0.076%, 99.813% 0.131%, 99.866% 0.199%, 99.912% 0.279%, 99.949% 0.368%, 99.977% 0.465%, 99.994% 0.57%, 100% 0.68%, 100% 99.32%, 100% 99.32%, 99.994% 99.43%, 99.977% 99.535%, 99.949% 99.632%, 99.912% 99.722%, 99.866% 99.801%, 99.813% 99.869%, 99.753% 99.924%, 99.688% 99.965%, 99.617% 99.991%, 99.543% 100%, 12.044% 100%, 12.044% 100%, 12.011% 99.998%, 11.978% 99.993%, 11.946% 99.984%, 11.914% 99.972%, 11.883% 99.957%, 11.853% 99.938%, 11.824% 99.916%, 11.796% 99.891%, 11.769% 99.863%, 11.743% 99.832%, 0.156% 84.744%, 0.156% 84.744%, 0.128% 84.703%, 0.102% 84.66%, 0.079% 84.613%, 0.058% 84.564%, 0.041% 84.513%, 0.026% 84.46%, 0.015% 84.404%, 0.007% 84.348%, 0.002% 84.29%, 0% 84.232%, 0% 0.68%, 0% 0.68%, 0.006% 0.57%, 0.023% 0.465%, 0.051% 0.368%, 0.088% 0.279%, 0.134% 0.199%, 0.187% 0.131%, 0.247% 0.076%, 0.312% 0.035%, 0.383% 0.009%, 0.457% 0%);
  border-radius: 16px;
}
.mining-responsibly-item::after {
  position: absolute;
  content: "";
  width: 100%;
  height: 100%;
  top: 0;
  inset-inline-start: 0;
  background-image: url(../images/mining-responsibly/noise.png);
  z-index: 3;
  clip-path: polygon(0.457% 0%, 99.543% 0%, 99.543% 0%, 99.617% 0.009%, 99.688% 0.035%, 99.753% 0.076%, 99.813% 0.131%, 99.866% 0.199%, 99.912% 0.279%, 99.949% 0.368%, 99.977% 0.465%, 99.994% 0.57%, 100% 0.68%, 100% 99.32%, 100% 99.32%, 99.994% 99.43%, 99.977% 99.535%, 99.949% 99.632%, 99.912% 99.722%, 99.866% 99.801%, 99.813% 99.869%, 99.753% 99.924%, 99.688% 99.965%, 99.617% 99.991%, 99.543% 100%, 12.044% 100%, 12.044% 100%, 12.011% 99.998%, 11.978% 99.993%, 11.946% 99.984%, 11.914% 99.972%, 11.883% 99.957%, 11.853% 99.938%, 11.824% 99.916%, 11.796% 99.891%, 11.769% 99.863%, 11.743% 99.832%, 0.156% 84.744%, 0.156% 84.744%, 0.128% 84.703%, 0.102% 84.66%, 0.079% 84.613%, 0.058% 84.564%, 0.041% 84.513%, 0.026% 84.46%, 0.015% 84.404%, 0.007% 84.348%, 0.002% 84.29%, 0% 84.232%, 0% 0.68%, 0% 0.68%, 0.006% 0.57%, 0.023% 0.465%, 0.051% 0.368%, 0.088% 0.279%, 0.134% 0.199%, 0.187% 0.131%, 0.247% 0.076%, 0.312% 0.035%, 0.383% 0.009%, 0.457% 0%);
  border-radius: 16px;
}
.mining-responsibly-item .clip-path {
  position: relative;
  z-index: 3;
  background: #EDF1FC;
  clip-path: polygon(0.457% 0%, 99.543% 0%, 99.543% 0%, 99.617% 0.009%, 99.688% 0.035%, 99.753% 0.076%, 99.813% 0.131%, 99.866% 0.199%, 99.912% 0.279%, 99.949% 0.368%, 99.977% 0.465%, 99.994% 0.57%, 100% 0.68%, 100% 99.32%, 100% 99.32%, 99.994% 99.43%, 99.977% 99.535%, 99.949% 99.632%, 99.912% 99.722%, 99.866% 99.801%, 99.813% 99.869%, 99.753% 99.924%, 99.688% 99.965%, 99.617% 99.991%, 99.543% 100%, 12.044% 100%, 12.044% 100%, 12.011% 99.998%, 11.978% 99.993%, 11.946% 99.984%, 11.914% 99.972%, 11.883% 99.957%, 11.853% 99.938%, 11.824% 99.916%, 11.796% 99.891%, 11.769% 99.863%, 11.743% 99.832%, 0.156% 84.744%, 0.156% 84.744%, 0.128% 84.703%, 0.102% 84.66%, 0.079% 84.613%, 0.058% 84.564%, 0.041% 84.513%, 0.026% 84.46%, 0.015% 84.404%, 0.007% 84.348%, 0.002% 84.29%, 0% 84.232%, 0% 0.68%, 0% 0.68%, 0.006% 0.57%, 0.023% 0.465%, 0.051% 0.368%, 0.088% 0.279%, 0.134% 0.199%, 0.187% 0.131%, 0.247% 0.076%, 0.312% 0.035%, 0.383% 0.009%, 0.457% 0%);
  width: 100%;
  border-radius: 16px;
}
.dark-theme .mining-responsibly-item .clip-path {
  background: #04060a;
}
.mining-responsibly-item .clip-path::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.05) -17.8%, rgba(9, 70, 255, 0.03) 90.06%);
  z-index: -1;
  width: 100%;
  height: 100%;
  border-radius: 16px;
}
.dark-theme .mining-responsibly-item .clip-path::before {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
}
.mining-responsibly-item .clip-inner {
  padding: 40px 40px 70px;
  position: relative;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .mining-responsibly-item .clip-inner {
    padding: 30px 30px 60px;
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .mining-responsibly-item .clip-inner {
    padding: 30px 30px 60px;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .mining-responsibly-item .clip-inner {
    padding: 24px 24px;
  }
}
.mining-responsibly-item .gradient-bg {
  position: absolute;
  bottom: 0;
  width: 100%;
  inset-inline-start: 0;
  z-index: -1;
}
.mining-responsibly-item .gradient-bg img {
  width: 100%;
}
.mining-responsibly-item .icon {
  margin-bottom: 30px;
}
.mining-responsibly-item .icon img {
  width: 72px;
  height: 72px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .mining-responsibly-item .icon img {
    width: 52px;
    height: 52px;
  }
}
.mining-responsibly-item .contents .title {
  font-size: 24px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .mining-responsibly-item .contents .title {
    font-size: 20px;
  }
}
.mining-responsibly-item .contents .description {
  margin-top: 10px;
}

/*----------------------------------------*/
/*  Our mission styles 
/*----------------------------------------*/
.td-our-mission-section {
  background-color: #6556FF;
}

.our-mission-heading .title-inner {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 14px;
}
.our-mission-heading .title-inner .icon {
  width: 24px;
  height: 24px;
}
.our-mission-heading .title-inner .title {
  color: var(--td-white);
  font-size: 24px;
}
.our-mission-heading .description {
  color: rgba(255, 255, 255, 0.8);
}

.our-mission-value ul {
  list-style-type: disc;
  padding-inline-start: 28px;
  margin-bottom: 16px;
}
.our-mission-value ul li {
  color: rgba(255, 255, 255, 0.8);
}
.our-mission-value ul li:not(:last-child) {
  margin-bottom: 13px;
}

/*----------------------------------------*/
/*  Who we are card styles 
/*----------------------------------------*/
.td-who-we-are-section {
  background-color: #E6EFFC;
}
.dark-theme .td-who-we-are-section {
  background-color: #0C142B;
}

/*----------------------------------------*/
/*  Asset management Styles 
/*----------------------------------------*/
.dark-theme .td-asset-management-section {
  background-color: #0C142B;
}

.asset-management-thumb-wrapper {
  position: relative;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .asset-management-thumb-wrapper {
    margin-top: 30px;
  }
}
.asset-management-thumb-wrapper .site-log-box .site-logo {
  width: 186px;
  height: 200px;
  background-color: #F0F4FF;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
  border-radius: 16px;
  clip-path: polygon(99.903% 0%, 0% 0%, 0% 100%, 82.644% 100%, 99.903% 87.046%, 99.903% 0%);
  z-index: 5;
}
.asset-management-thumb-wrapper .site-log-box .site-logo::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  filter: drop-shadow(0px 0px 70px rgba(191, 150, 255, 0.6));
  z-index: -1;
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .asset-management-thumb-wrapper .site-log-box .site-logo {
    width: 136px;
    height: 150px;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .asset-management-thumb-wrapper .site-log-box .site-logo {
    width: 100px;
    height: 114px;
  }
}
[dir=rtl] .asset-management-thumb-wrapper .site-log-box .site-logo {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
.dark-theme .asset-management-thumb-wrapper .site-log-box .site-logo {
  background-color: #020A22;
}
.asset-management-thumb-wrapper .site-log-box .site-logo img {
  height: 80px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .asset-management-thumb-wrapper .site-log-box .site-logo img {
    height: 50px;
  }
}

.asset-management-thumb {
  position: relative;
  z-index: 1;
}
.asset-management-thumb::before {
  position: absolute;
  content: "";
  width: 340px;
  height: 340px;
  background: #8DF4FF;
  opacity: 0.2;
  filter: blur(80px);
  inset-inline-start: 50%;
  transform: translateX(-50%);
  top: 67px;
  z-index: -1;
}
.asset-management-thumb img {
  width: 100%;
}

.asset-management-shapes .shape-one {
  position: absolute;
  inset-inline-end: -45px;
  top: -130px;
  z-index: -1;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .asset-management-shapes .shape-one img {
    width: 150px;
  }
}
.asset-management-shapes .shape-two {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: -1;
  inset-inline-start: 0;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .asset-management-shapes .shape-two img {
    width: 150px;
  }
}

/*----------------------------------------*/
/*  How operate Styles 
/*----------------------------------------*/
.td-how-operate-section {
  background-color: #E6EFFC;
}
.dark-theme .td-how-operate-section {
  background-color: #0C142B;
}

.how-operate-inner {
  position: relative;
  padding: 2px;
  display: block;
}
.how-operate-inner .operate-clip-path-inner {
  display: inline-flex;
  position: relative;
  z-index: 3;
  width: 100%;
  padding-top: 300px;
  padding-bottom: 75px;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .how-operate-inner .operate-clip-path-inner {
    padding-top: 200px;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .how-operate-inner .operate-clip-path-inner {
    padding-top: 60px;
    padding-bottom: 0;
  }
}
.how-operate-inner .operate-clip-path-inner::before {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  content: "";
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
  z-index: -1;
  width: calc(100% - 130px);
  height: 100%;
  clip-path: polygon(0% 46.218%, 0% 46.218%, 0.034% 45.114%, 0.135% 44.054%, 0.297% 43.052%, 0.516% 42.118%, 0.787% 41.265%, 1.107% 40.505%, 1.471% 39.85%, 1.874% 39.313%, 2.312% 38.905%, 2.78% 38.639%, 50% 19.872%, 100% 0%, 100% 100%, 3.352% 100%, 3.352% 100%, 2.808% 99.899%, 2.292% 99.608%, 1.812% 99.141%, 1.372% 98.516%, 0.982% 97.747%, 0.647% 96.851%, 0.374% 95.843%, 0.171% 94.739%, 0.044% 93.555%, 0% 92.308%, 0% 46.218%);
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .how-operate-inner .operate-clip-path-inner::before {
    width: calc(100% - 30px);
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .how-operate-inner .operate-clip-path-inner::before {
    display: none;
  }
}
.how-operate-inner .operate-clip-path-inner::after {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  content: "";
  clip-path: polygon(0% 46.218%, 0% 46.218%, 0.034% 45.114%, 0.135% 44.054%, 0.297% 43.052%, 0.516% 42.118%, 0.787% 41.265%, 1.107% 40.505%, 1.471% 39.85%, 1.874% 39.313%, 2.312% 38.905%, 2.78% 38.639%, 50% 19.872%, 100% 0%, 100% 100%, 3.352% 100%, 3.352% 100%, 2.808% 99.899%, 2.292% 99.608%, 1.812% 99.141%, 1.372% 98.516%, 0.982% 97.747%, 0.647% 96.851%, 0.374% 95.843%, 0.171% 94.739%, 0.044% 93.555%, 0% 92.308%, 0% 46.218%);
  z-index: -1;
  width: calc(100% - 130px);
  height: 100%;
  background: #F3F7FE;
}
.dark-theme .how-operate-inner .operate-clip-path-inner::after {
  background: #020a22;
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .how-operate-inner .operate-clip-path-inner::after {
    width: calc(100% - 30px);
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .how-operate-inner .operate-clip-path-inner::after {
    display: none;
  }
}
.how-operate-inner::before {
  position: absolute;
  inset-inline-end: 2px;
  top: 0;
  transition: all 0.3s;
  width: calc(100% - 132px);
  height: 100%;
  background: #729CFF;
  content: "";
  clip-path: polygon(0% 46.218%, 0% 46.218%, 0.034% 45.114%, 0.135% 44.054%, 0.297% 43.052%, 0.516% 42.118%, 0.787% 41.265%, 1.107% 40.505%, 1.471% 39.85%, 1.874% 39.313%, 2.312% 38.905%, 2.78% 38.639%, 50% 19.872%, 100% 0%, 100% 100%, 3.352% 100%, 3.352% 100%, 2.808% 99.899%, 2.292% 99.608%, 1.812% 99.141%, 1.372% 98.516%, 0.982% 97.747%, 0.647% 96.851%, 0.374% 95.843%, 0.171% 94.739%, 0.044% 93.555%, 0% 92.308%, 0% 46.218%);
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .how-operate-inner::before {
    width: calc(100% - 30px);
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .how-operate-inner::before {
    display: none;
  }
}

.how-operate-item .contents .count {
  display: flex;
  width: 40px;
  height: 40px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 14px;
  border: 1px solid #4776E6;
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.06) 0%, rgba(142, 84, 233, 0.06) 100%);
  color: #222223;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 25px;
}
.dark-theme .how-operate-item .contents .count {
  color: rgba(255, 255, 255, 0.6);
}
.how-operate-item .contents .title {
  font-size: 24px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .how-operate-item .contents .title {
    font-size: 20px;
  }
}
.how-operate-item .contents .description {
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  margin-top: 12px;
}

/*----------------------------------------*/
/* Newsletter styles
/*----------------------------------------*/
.td-newsletter-section {
  background: #6556FF;
}
.dark-theme .td-newsletter-section {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) 0%, rgba(142, 84, 233, 0.3) 100%);
}

.newsletter-thumb {
  max-width: 504px;
  margin: 0 auto;
  position: relative;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .newsletter-thumb {
    max-width: 404px;
  }
}
.newsletter-thumb::before {
  position: absolute;
  content: "";
  border-radius: 298px;
  background: #C5FFE2;
  filter: blur(100px);
  width: 298px;
  height: 298px;
  z-index: -1;
  top: 50%;
  inset-inline-start: 50%;
  transform: translate(-50%, -50%);
}
[dir=rtl] .newsletter-thumb::before {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}

.newsletter-contents {
  max-width: 500px;
  margin: 0 auto;
}
.newsletter-contents .newsletter-form {
  position: relative;
}
.newsletter-contents .newsletter-form .input-clip {
  position: relative;
  padding: 1px;
  display: block;
}
.newsletter-contents .newsletter-form .input-clip::before {
  position: absolute;
  content: "";
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3) 100%);
  clip-path: polygon(0.512% 0.962%, 99.488% 0.962%, 99.488% 0.962%, 99.551% 0.999%, 99.61% 1.109%, 99.665% 1.284%, 99.715% 1.518%, 99.76% 1.806%, 99.798% 2.143%, 99.829% 2.521%, 99.853% 2.934%, 99.867% 3.378%, 99.872% 3.846%, 99.872% 75.331%, 99.872% 75.331%, 99.871% 75.531%, 99.868% 75.728%, 99.864% 75.923%, 99.858% 76.115%, 99.85% 76.303%, 99.84% 76.486%, 99.829% 76.663%, 99.816% 76.835%, 99.801% 77%, 99.785% 77.158%, 99.76% 77.37%, 96.99% 98.193%, 96.99% 98.193%, 96.968% 98.348%, 96.944% 98.489%, 96.919% 98.614%, 96.893% 98.724%, 96.866% 98.819%, 96.838% 98.897%, 96.809% 98.958%, 96.78% 99.002%, 96.75% 99.029%, 96.719% 99.038%, 0.512% 99.038%, 0.512% 99.038%, 0.449% 99.001%, 0.39% 98.891%, 0.335% 98.716%, 0.285% 98.482%, 0.24% 98.194%, 0.202% 97.857%, 0.171% 97.479%, 0.147% 97.066%, 0.133% 96.622%, 0.128% 96.154%, 0.128% 3.846%, 0.128% 3.846%, 0.132% 3.407%, 0.145% 2.988%, 0.166% 2.596%, 0.193% 2.233%, 0.228% 1.907%, 0.267% 1.62%, 0.313% 1.379%, 0.362% 1.188%, 0.416% 1.052%, 0.472% 0.977%, 0.512% 0.962%);
}
.dark-theme .newsletter-contents .newsletter-form .input-clip::before {
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
}
.newsletter-contents .newsletter-form .input-clip .clip-inner {
  position: relative;
  z-index: 3;
  width: 100%;
  clip-path: polygon(0.512% 0.962%, 99.488% 0.962%, 99.488% 0.962%, 99.551% 0.999%, 99.61% 1.109%, 99.665% 1.284%, 99.715% 1.518%, 99.76% 1.806%, 99.798% 2.143%, 99.829% 2.521%, 99.853% 2.934%, 99.867% 3.378%, 99.872% 3.846%, 99.872% 75.331%, 99.872% 75.331%, 99.871% 75.531%, 99.868% 75.728%, 99.864% 75.923%, 99.858% 76.115%, 99.85% 76.303%, 99.84% 76.486%, 99.829% 76.663%, 99.816% 76.835%, 99.801% 77%, 99.785% 77.158%, 99.76% 77.37%, 96.99% 98.193%, 96.99% 98.193%, 96.968% 98.348%, 96.944% 98.489%, 96.919% 98.614%, 96.893% 98.724%, 96.866% 98.819%, 96.838% 98.897%, 96.809% 98.958%, 96.78% 99.002%, 96.75% 99.029%, 96.719% 99.038%, 0.512% 99.038%, 0.512% 99.038%, 0.449% 99.001%, 0.39% 98.891%, 0.335% 98.716%, 0.285% 98.482%, 0.24% 98.194%, 0.202% 97.857%, 0.171% 97.479%, 0.147% 97.066%, 0.133% 96.622%, 0.128% 96.154%, 0.128% 3.846%, 0.128% 3.846%, 0.132% 3.407%, 0.145% 2.988%, 0.166% 2.596%, 0.193% 2.233%, 0.228% 1.907%, 0.267% 1.62%, 0.313% 1.379%, 0.362% 1.188%, 0.416% 1.052%, 0.472% 0.977%, 0.512% 0.962%);
  background: #3D3587;
}
.newsletter-contents .newsletter-form .input-clip .clip-inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  z-index: -1;
  width: 100%;
  height: 100%;
  clip-path: polygon(0.512% 0.962%, 99.488% 0.962%, 99.488% 0.962%, 99.551% 0.999%, 99.61% 1.109%, 99.665% 1.284%, 99.715% 1.518%, 99.76% 1.806%, 99.798% 2.143%, 99.829% 2.521%, 99.853% 2.934%, 99.867% 3.378%, 99.872% 3.846%, 99.872% 75.331%, 99.872% 75.331%, 99.871% 75.531%, 99.868% 75.728%, 99.864% 75.923%, 99.858% 76.115%, 99.85% 76.303%, 99.84% 76.486%, 99.829% 76.663%, 99.816% 76.835%, 99.801% 77%, 99.785% 77.158%, 99.76% 77.37%, 96.99% 98.193%, 96.99% 98.193%, 96.968% 98.348%, 96.944% 98.489%, 96.919% 98.614%, 96.893% 98.724%, 96.866% 98.819%, 96.838% 98.897%, 96.809% 98.958%, 96.78% 99.002%, 96.75% 99.029%, 96.719% 99.038%, 0.512% 99.038%, 0.512% 99.038%, 0.449% 99.001%, 0.39% 98.891%, 0.335% 98.716%, 0.285% 98.482%, 0.24% 98.194%, 0.202% 97.857%, 0.171% 97.479%, 0.147% 97.066%, 0.133% 96.622%, 0.128% 96.154%, 0.128% 3.846%, 0.128% 3.846%, 0.132% 3.407%, 0.145% 2.988%, 0.166% 2.596%, 0.193% 2.233%, 0.228% 1.907%, 0.267% 1.62%, 0.313% 1.379%, 0.362% 1.188%, 0.416% 1.052%, 0.472% 0.977%, 0.512% 0.962%);
}
.newsletter-contents .newsletter-form .input-clip .clip-inner input {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.3) -1.65%, rgba(142, 84, 233, 0.3) 43.73%);
  border: 0;
  height: 52px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  padding-inline-end: 140px;
}
.newsletter-contents .newsletter-form .input-clip .clip-inner input::-webkit-input-placeholder {
  /* Chrome/Opera/Safari */
  color: rgba(255, 255, 255, 0.6);
}
.newsletter-contents .newsletter-form .input-clip .clip-inner input::-moz-placeholder {
  /* Firefox 19+ */
  color: rgba(255, 255, 255, 0.6);
}
.newsletter-contents .newsletter-form .input-clip .clip-inner input:-moz-placeholder {
  /* Firefox 4-18 */
  color: rgba(255, 255, 255, 0.6);
}
.newsletter-contents .newsletter-form .input-clip .clip-inner input:-ms-input-placeholder {
  /* IE 10+  Edge*/
  color: rgba(255, 255, 255, 0.6);
}
.newsletter-contents .newsletter-form .input-clip .clip-inner input::placeholder {
  /* MODERN BROWSER */
  color: rgba(255, 255, 255, 0.6);
}
.newsletter-contents .newsletter-form .td-btn {
  position: absolute;
  top: 50%;
  inset-inline-end: 6px;
  z-index: 11;
  transform: translateY(-50%);
  background: linear-gradient(90deg, #4776E6 36.36%, #8E54E9 136.36%);
}

.newsletter-shapes .shape-one {
  position: absolute;
  top: 52px;
  inset-inline-end: 5%;
  z-index: -1;
}

/*----------------------------------------*/
/* Client review styles
/*----------------------------------------*/
.our-client-glow .glow-one {
  position: absolute;
  top: -48px;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  z-index: -1;
}
[dir=rtl] .our-client-glow .glow-one {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}

.bd-pagination {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 10px;
}
.bd-pagination .swiper-pagination-bullet {
  width: 7px;
  height: 7px;
  border-radius: 0;
  margin: 0 !important;
  background-color: #CCE2FA;
  opacity: 1;
  border-radius: 10px;
  transition: 0.3s;
}
.bd-pagination .swiper-pagination-bullet-active {
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  width: 19px;
}

.client-review-item {
  position: relative;
  padding: 1px;
  display: block;
}
.client-review-item::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  opacity: 0.6;
  content: "";
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  border-radius: 2px;
}
.client-review-item .clip-path {
  position: relative;
  z-index: 3;
  background: var(--td-white);
  clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
  gap: 12px;
  width: 100%;
}
.dark-theme .client-review-item .clip-path {
  background: #04060a;
}
.client-review-item .clip-path::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) 81.54%, rgba(142, 84, 233, 0.1) 118.26%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.dark-theme .client-review-item .clip-path::before {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
}
.client-review-item .clip-inner {
  padding: 32px 32px;
}
@media only screen and (max-width: 30.06125rem) {
  .client-review-item .clip-inner {
    padding: 24px 24px;
  }
}
.client-review-item .review-contents {
  margin-bottom: 30px;
}

.admin-item {
  display: flex;
  align-items: center;
  gap: 16px;
}
.admin-item .admin-thumbnail img {
  width: 50px;
  height: 50px;
}
.admin-item .admin-info .admin-name {
  font-size: 20px;
}
.admin-item .admin-info .admin-designation {
  font-size: 16px;
  margin-top: 2px;
  display: block;
}

/*----------------------------------------*/
/* Why choose styles
/*----------------------------------------*/
.td-why-choose-us-section {
  background-color: #F4FAFF;
}
.dark-theme .td-why-choose-us-section {
  background-color: #0C142B;
}

.why-choose-left-contents {
  padding-inline-end: 20px;
}

.why-choose-column {
  padding: 34px 50px;
  border-inline-start: 1px solid rgba(8, 8, 8, 0.16);
  display: flex;
  flex-direction: column;
  gap: 50px;
}
.dark-theme .why-choose-column {
  border-color: rgba(255, 255, 255, 0.1);
}
@media only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .why-choose-column {
    border-inline-start: 0;
    padding: 0;
  }
}
@media only screen and (max-width: 35.99875rem) {
  .why-choose-column {
    border-inline-start: 0;
    padding: 30px;
  }
}

.why-choose-item {
  width: 240px;
}
.why-choose-item .icon {
  width: 50px;
  height: 50px;
  margin-bottom: 25px;
}
.why-choose-item .contents .title {
  margin-bottom: 10px;
  font-size: 20px;
}

.why-choose-bg {
  position: absolute;
  top: 0;
  left: 0;
  background-repeat: no-repeat;
  width: 100%;
  height: 100%;
  z-index: -1;
  display: none;
}
.dark-theme .why-choose-bg {
  display: block;
}

/*----------------------------------------*/
/*  Our solutions styles
/*----------------------------------------*/
.td-our-solutions {
  background-color: #F4FAFF;
}
.dark-theme .td-our-solutions {
  background-color: #0C142B;
}

.our-solutions-item {
  position: relative;
  text-align: center;
  height: 100%;
}
.our-solutions-item .clip-path {
  position: relative;
  padding: 1px;
  height: 100%;
}
.our-solutions-item .clip-path-inner {
  position: relative;
  z-index: 3;
  background: rgba(255, 255, 255, 0.5);
  clip-path: polygon(97.403% 0%, 2.597% 0%, 2.597% 0%, 2.176% 0.032%, 1.776% 0.125%, 1.404% 0.274%, 1.063% 0.473%, 0.761% 0.719%, 0.501% 1.005%, 0.29% 1.326%, 0.132% 1.678%, 0.034% 2.056%, 0% 2.454%, 0% 97.337%, 0% 97.337%, 0.034% 97.735%, 0.132% 98.113%, 0.29% 98.465%, 0.501% 98.786%, 0.761% 99.072%, 1.063% 99.318%, 1.404% 99.517%, 1.776% 99.666%, 2.176% 99.759%, 2.597% 99.791%, 82.141% 99.791%, 82.141% 99.791%, 82.329% 99.785%, 82.515% 99.766%, 82.698% 99.734%, 82.878% 99.69%, 83.055% 99.634%, 83.226% 99.567%, 83.392% 99.488%, 83.552% 99.397%, 83.705% 99.296%, 83.851% 99.184%, 99.113% 86.567%, 99.113% 86.567%, 99.275% 86.421%, 99.422% 86.264%, 99.553% 86.097%, 99.669% 85.92%, 99.768% 85.734%, 99.85% 85.542%, 99.915% 85.343%, 99.962% 85.139%, 99.99% 84.931%, 100% 84.72%, 100% 2.454%, 100% 2.454%, 99.966% 2.056%, 99.868% 1.678%, 99.71% 1.326%, 99.499% 1.005%, 99.239% 0.719%, 98.937% 0.473%, 98.596% 0.274%, 98.224% 0.125%, 97.824% 0.032%, 97.403% 0%);
  padding: 50px 35px;
  height: 100%;
}
.dark-theme .our-solutions-item .clip-path-inner {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
}
.our-solutions-item .clip-border {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.our-solutions-item .contents .icon {
  width: 60px;
  height: 60px;
  margin: 0 auto;
  margin-bottom: 20px;
}
.our-solutions-item .contents .title {
  font-size: 24px;
  margin-bottom: 10px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .our-solutions-item .contents .title {
    font-size: 20px;
  }
}
.our-solutions-item .contents .description {
  font-size: 16px;
  line-height: 26px;
}

.why-choose-wrapper {
  display: grid;
  grid-template-columns: 1fr 1fr;
}
@media only screen and (max-width: 35.99875rem) {
  .why-choose-wrapper {
    grid-template-columns: 1fr;
  }
}

/*----------------------------------------*/
/* Recent transaction styles
/*----------------------------------------*/
.recent-transition-bg {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  display: none;
  z-index: -1;
}
.dark-theme .recent-transition-bg {
  display: block;
}

.td-table td {
  text-align: start;
  padding: 22px 16px;
  font-weight: 500;
  font-size: 16px;
}

.recent-transactions-box {
  position: relative;
}
.recent-transactions-box .clip-path {
  position: relative;
  padding: 1px;
  height: 100%;
}
.recent-transactions-box .clip-path-inner {
  position: relative;
  z-index: 3;
  background: #F8FCFF;
  clip-path: polygon(0% 1.349%, 0% 98.651%, 0% 98.651%, 0.016% 98.87%, 0.063% 99.077%, 0.139% 99.271%, 0.24% 99.448%, 0.364% 99.605%, 0.509% 99.74%, 0.671% 99.849%, 0.85% 99.931%, 1.041% 99.982%, 1.242% 100%, 93.955% 100%, 93.955% 100%, 94.044% 99.997%, 94.132% 99.986%, 94.219% 99.969%, 94.304% 99.946%, 94.387% 99.916%, 94.469% 99.879%, 94.547% 99.837%, 94.623% 99.788%, 94.696% 99.734%, 94.766% 99.673%, 99.568% 95.181%, 99.568% 95.181%, 99.647% 95.101%, 99.719% 95.014%, 99.782% 94.921%, 99.839% 94.823%, 99.887% 94.721%, 99.927% 94.614%, 99.959% 94.504%, 99.981% 94.391%, 99.995% 94.276%, 100% 94.159%, 100% 1.349%, 100% 1.349%, 99.984% 1.13%, 99.937% 0.923%, 99.861% 0.729%, 99.76% 0.552%, 99.636% 0.395%, 99.491% 0.26%, 99.329% 0.151%, 99.15% 0.069%, 98.959% 0.018%, 98.758% 0%, 50% 0%, 1.242% 0%, 1.242% 0%, 1.041% 0.018%, 0.85% 0.069%, 0.671% 0.151%, 0.509% 0.26%, 0.364% 0.395%, 0.24% 0.552%, 0.139% 0.729%, 0.063% 0.923%, 0.016% 1.13%, 0% 1.349%);
  padding: 24px 24px 24px;
  height: 100%;
}
.dark-theme .recent-transactions-box .clip-path-inner {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
  backdrop-filter: blur(10px);
}
.recent-transactions-box .transaction-contents {
  border-radius: 8px;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
  backdrop-filter: blur(10px);
  padding: 20px;
}
.recent-transactions-box .transaction-contents .td-custom-table {
  min-width: 450px;
}
.recent-transactions-box .bg-shape {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.transactions-heading .title {
  font-size: 20px;
}

.td-table .clip-path {
  position: relative;
  display: inline-block;
  padding: 1px;
}
.td-table .clip-path .inner {
  display: inline-flex;
  position: relative;
  z-index: 3;
  background: #020a22;
  clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
  gap: 12px;
  padding: 20px 40px;
}
.td-table .clip-path .inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.td-table .clip-path::before, .td-table .clip-path::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg, #091B52 0%, #0094FF 100%);
  opacity: 0.6;
  content: "";
  clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
  border-radius: 2px;
}

/*----------------------------------------*/
/* Calculate earning styles
/*----------------------------------------*/
.td-calculate-earning-section {
  background: #F5F5F5;
  position: relative;
  z-index: 31;
}
.dark-theme .td-calculate-earning-section {
  background: rgba(255, 255, 255, 0.04);
}

.calculate-earning-form .clip-path {
  position: relative;
  padding: 1px;
}
.calculate-earning-form .clip-path .clip-path-inner {
  position: relative;
  z-index: 3;
}
.calculate-earning-form .clip-path .clip-path-inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: #F8FCFF;
  width: 100%;
  height: 100%;
  clip-path: polygon(0.46% 0.179%, 99.54% 0.179%, 99.54% 0.179%, 99.605% 0.196%, 99.667% 0.243%, 99.725% 0.319%, 99.778% 0.421%, 99.825% 0.547%, 99.865% 0.693%, 99.898% 0.857%, 99.922% 1.037%, 99.937% 1.23%, 99.943% 1.434%, 99.943% 75.578%, 99.943% 75.578%, 99.941% 75.677%, 99.938% 75.775%, 99.931% 75.871%, 99.923% 75.965%, 99.912% 76.056%, 99.899% 76.145%, 99.884% 76.23%, 99.867% 76.311%, 99.847% 76.388%, 99.826% 76.461%, 99.811% 76.506%, 91.702% 99.495%, 91.702% 99.495%, 91.679% 99.555%, 91.655% 99.609%, 91.63% 99.658%, 91.604% 99.7%, 91.576% 99.736%, 91.549% 99.766%, 91.52% 99.79%, 91.491% 99.807%, 91.461% 99.817%, 91.431% 99.821%, 0.46% 99.821%, 0.46% 99.821%, 0.395% 99.804%, 0.333% 99.757%, 0.275% 99.681%, 0.222% 99.579%, 0.175% 99.453%, 0.135% 99.307%, 0.102% 99.143%, 0.078% 98.963%, 0.063% 98.77%, 0.057% 98.566%, 0.057% 1.434%, 0.057% 1.434%, 0.063% 1.23%, 0.078% 1.037%, 0.102% 0.857%, 0.135% 0.693%, 0.175% 0.547%, 0.222% 0.421%, 0.275% 0.319%, 0.333% 0.243%, 0.395% 0.196%, 0.46% 0.179%);
}
.dark-theme .calculate-earning-form .clip-path .clip-path-inner::before {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
}
.calculate-earning-form .clip-path .clip-path-inner::after {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: #0c142b;
  clip-path: polygon(0.46% 0.179%, 99.54% 0.179%, 99.54% 0.179%, 99.605% 0.196%, 99.667% 0.243%, 99.725% 0.319%, 99.778% 0.421%, 99.825% 0.547%, 99.865% 0.693%, 99.898% 0.857%, 99.922% 1.037%, 99.937% 1.23%, 99.943% 1.434%, 99.943% 75.578%, 99.943% 75.578%, 99.941% 75.677%, 99.938% 75.775%, 99.931% 75.871%, 99.923% 75.965%, 99.912% 76.056%, 99.899% 76.145%, 99.884% 76.23%, 99.867% 76.311%, 99.847% 76.388%, 99.826% 76.461%, 99.811% 76.506%, 91.702% 99.495%, 91.702% 99.495%, 91.679% 99.555%, 91.655% 99.609%, 91.63% 99.658%, 91.604% 99.7%, 91.576% 99.736%, 91.549% 99.766%, 91.52% 99.79%, 91.491% 99.807%, 91.461% 99.817%, 91.431% 99.821%, 0.46% 99.821%, 0.46% 99.821%, 0.395% 99.804%, 0.333% 99.757%, 0.275% 99.681%, 0.222% 99.579%, 0.175% 99.453%, 0.135% 99.307%, 0.102% 99.143%, 0.078% 98.963%, 0.063% 98.77%, 0.057% 98.566%, 0.057% 1.434%, 0.057% 1.434%, 0.063% 1.23%, 0.078% 1.037%, 0.102% 0.857%, 0.135% 0.693%, 0.175% 0.547%, 0.222% 0.421%, 0.275% 0.319%, 0.333% 0.243%, 0.395% 0.196%, 0.46% 0.179%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.calculate-earning-form .clip-path::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  opacity: 0.3;
  content: "";
  clip-path: polygon(0.46% 0.179%, 99.54% 0.179%, 99.54% 0.179%, 99.605% 0.196%, 99.667% 0.243%, 99.725% 0.319%, 99.778% 0.421%, 99.825% 0.547%, 99.865% 0.693%, 99.898% 0.857%, 99.922% 1.037%, 99.937% 1.23%, 99.943% 1.434%, 99.943% 75.578%, 99.943% 75.578%, 99.941% 75.677%, 99.938% 75.775%, 99.931% 75.871%, 99.923% 75.965%, 99.912% 76.056%, 99.899% 76.145%, 99.884% 76.23%, 99.867% 76.311%, 99.847% 76.388%, 99.826% 76.461%, 99.811% 76.506%, 91.702% 99.495%, 91.702% 99.495%, 91.679% 99.555%, 91.655% 99.609%, 91.63% 99.658%, 91.604% 99.7%, 91.576% 99.736%, 91.549% 99.766%, 91.52% 99.79%, 91.491% 99.807%, 91.461% 99.817%, 91.431% 99.821%, 0.46% 99.821%, 0.46% 99.821%, 0.395% 99.804%, 0.333% 99.757%, 0.275% 99.681%, 0.222% 99.579%, 0.175% 99.453%, 0.135% 99.307%, 0.102% 99.143%, 0.078% 98.963%, 0.063% 98.77%, 0.057% 98.566%, 0.057% 1.434%, 0.057% 1.434%, 0.063% 1.23%, 0.078% 1.037%, 0.102% 0.857%, 0.135% 0.693%, 0.175% 0.547%, 0.222% 0.421%, 0.275% 0.319%, 0.333% 0.243%, 0.395% 0.196%, 0.46% 0.179%);
  border-radius: 2px;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
}
.dark-theme .calculate-earning-form .clip-path::before {
  background: linear-gradient(179deg, #0B277A 0%, #0094FF 100%);
}
.calculate-earning-form .filter-contents {
  display: grid;
  align-items: center;
  width: 100%;
  gap: 20px 50px;
  padding: 50px 50px;
  grid-template-columns: auto 1px auto;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .calculate-earning-form .filter-contents {
    padding: 20px 20px;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .calculate-earning-form .filter-contents {
    grid-template-columns: 1fr;
  }
}
.calculate-earning-form .filter-contents .divider {
  width: 1px;
  height: calc(100% + 100px);
  background: #C6E7FF;
  display: inline-block;
  position: relative;
  z-index: 15;
}
.dark-theme .calculate-earning-form .filter-contents .divider {
  background: #0094FF;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .calculate-earning-form .filter-contents .divider {
    height: calc(100% + 40px);
  }
}
@media only screen and (max-width: 30.06125rem) {
  .calculate-earning-form .filter-contents .divider {
    display: none;
  }
}
.calculate-earning-form .filter-contents .divider::before {
  position: absolute;
  content: "";
  width: 26px;
  height: 26px;
  background-image: url(../images/icons/star-gradient.svg);
  background-repeat: no-repeat;
  background-size: cover;
  bottom: -13px;
  inset-inline-end: -13px;
}
.calculate-earning-form .filter-contents .select2-container--default .select2-selection {
  border-radius: 4px;
  border: 1px solid #D1D4DA;
  backdrop-filter: blur(10px);
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) 40.3%, rgba(142, 84, 233, 0.1) 100%);
}
.dark-theme .calculate-earning-form .filter-contents .select2-container--default .select2-selection {
  border-color: rgba(255, 255, 255, 0.1);
}
.calculate-earning-form .filter-contents .select2-dropdown {
  box-shadow: none;
  border: 1px solid #D1D4DA;
  background: #F8FCFF;
  backdrop-filter: blur(10px);
}
.dark-theme .calculate-earning-form .filter-contents .select2-dropdown {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
}
.calculate-earning-form .filter-contents .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: #E6EFFC !important;
  color: var(--td-heading) !important;
}
.dark-theme .calculate-earning-form .filter-contents .select2-container--default .select2-results__option--highlighted[aria-selected] {
  background-color: rgba(255, 255, 255, 0.1) !important;
}
.calculate-earning-form .filter-contents .select2-container--default .select2-results__option {
  color: var(--td-heading);
  font-weight: 600;
  font-size: 14px;
}
.calculate-earning-form .filter-contents .select2-container--default .select2-selection--single .select2-selection__rendered {
  color: var(--td-heading);
}
.calculate-earning-form .Revenue-content {
  border-top: 1px solid #C6E7FF;
  padding: 30px 50px;
  text-align: center;
  position: relative;
  z-index: 2;
}
.dark-theme .calculate-earning-form .Revenue-content {
  border-color: #0094FF;
}
.calculate-earning-form .Revenue-content .title {
  font-size: 16px;
  margin-bottom: 10px;
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.calculate-earning-form .Revenue-content .currency {
  font-size: 24px;
  font-weight: 700;
}

/*----------------------------------------*/
/* Future mining styles
/*----------------------------------------*/
.td-future-mining-section {
  background: #F5F5F5;
}
.dark-theme .td-future-mining-section {
  background-color: #020A22;
}

.future-mining-bg {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: -1;
}
.dark-theme .future-mining-bg {
  opacity: 0.06;
}

.future-mining-shapes .shape-one {
  position: absolute;
  inset-inline-end: 0px;
  top: 0;
  z-index: -1;
}

.future-mining-contents .rewards-box {
  border-radius: 16px;
  border: 2px dashed #4776E6;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(9.5px);
  padding: 30px;
  margin-bottom: 40px;
  margin-top: 40px;
  max-width: 533px;
}
@media only screen and (max-width: 35.99875rem) {
  .future-mining-contents .rewards-box {
    padding: 20px;
  }
}
.future-mining-contents .rewards-box .title {
  font-size: 28px;
  margin-bottom: 16px;
}
@media only screen and (max-width: 35.99875rem) {
  .future-mining-contents .rewards-box .title {
    font-size: 24px;
  }
}

.future-mining-thumb {
  text-align: end;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .future-mining-thumb {
    text-align: center;
  }
}
.future-mining-thumb img {
  max-width: 508px;
}
@media only screen and (max-width: 35.99875rem) {
  .future-mining-thumb img {
    width: 100%;
  }
}

/*----------------------------------------*/
/*  KYC styles 
/*----------------------------------------*/
.verification-inner-contents {
  padding: 30px 30px;
}

.identity-alert-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.kyc-card {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-radius: 8px;
}
.kyc-card:not(:last-child) {
  margin-bottom: 15px;
}
@media only screen and (max-width: 35.99875rem) {
  .kyc-card {
    flex-direction: column;
    align-items: start;
    gap: 12px;
  }
}
.kyc-card .status-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  background-color: var(--td-white);
  border-radius: 50%;
  margin-inline-end: 15px;
  flex: 0 0 auto;
}
.kyc-card .status-icon .icon {
  font-size: 1.5rem;
}
.kyc-card .details .label {
  font-weight: bold;
  color: #ccc;
}
.kyc-card .details .status {
  display: inline-block;
  padding: 2px 16px;
  border-radius: 30px;
  font-weight: bold;
  font-size: 14px;
}
.kyc-card .details .view-details {
  color: #3a82e1;
  text-decoration: none;
  font-size: 0.9rem;
}
.kyc-card .details .view-details:hover {
  text-decoration: underline;
}
.kyc-card .details .submission-date {
  font-size: 0.85rem;
  color: #aaa;
  margin-top: 5px;
}
.kyc-card .details .details-info {
  display: flex;
  align-items: center;
  gap: 12px 12px;
  flex-wrap: wrap;
}
.kyc-card.rejected {
  background: rgba(255, 46, 55, 0.04);
  border: 2px dashed rgba(255, 46, 55, 0.2);
  border-radius: 16px;
}
.kyc-card.rejected .status-icon .icon {
  color: #b53039;
}
.kyc-card.rejected .details .status {
  background-color: #b53039;
  color: var(--td-white);
}
.kyc-card.success {
  background: rgba(0, 204, 0, 0.02);
  border: 2px dashed rgba(0, 204, 0, 0.2);
  border-radius: 16px;
}
.kyc-card.success .status-icon .icon {
  color: #2f7d4f;
}
.kyc-card.success .details .status {
  background-color: #2f7d4f;
  color: var(--td-white);
}

/*----------------------------------------*/
/* 2FA styles
/*----------------------------------------*/
.two-fa-auth-wrapper .contents .description {
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 26px;
}
.dark-theme .two-fa-auth-wrapper .contents .description {
  color: rgba(255, 255, 255, 0.8);
}
.two-fa-auth-wrapper .qr-code p {
  background: linear-gradient(90deg, #C340C0 0%, #FB405A 13.39%, #F7A34A 26.79%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}
.two-fa-auth-wrapper .qr-code .thumb {
  max-width: 336px;
  margin-top: 35px;
}
.two-fa-auth-wrapper .qr-code .thumb .qr-code-dark {
  display: none;
}
.dark-theme .two-fa-auth-wrapper .qr-code .thumb .qr-code-dark {
  display: block;
}
.dark-theme .two-fa-auth-wrapper .qr-code .thumb .qr-code-light {
  display: none;
}
@media only screen and (max-width: 35.99875rem) {
  .two-fa-auth-wrapper .qr-code .thumb {
    max-width: 236px;
  }
}
.two-fa-auth-wrapper .qr-code .thumb img {
  -webkit-border-radius: 16px;
  -moz-border-radius: 16px;
  -o-border-radius: 16px;
  -ms-border-radius: 16px;
  border-radius: 16px;
}

/*----------------------------------------*/
/* Support chat styles
/*----------------------------------------*/
.support-ticket-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #d5dae7;
  padding: 12px 16px;
  border-radius: 10px;
  flex-wrap: wrap;
  gap: 12px 12px;
}
.dark-theme .support-ticket-top {
  background: #16213F;
}

.support-ticket-top-right {
  display: flex;
  align-items: center;
  gap: 12px 12px;
  flex-wrap: wrap;
}

.support-ticket-chat-area {
  border-radius: 1rem;
  border: 1px solid #0B277A;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
  border: 1px solid rgba(8, 8, 8, 0.16);
  padding: 30px 30px;
}
.dark-theme .support-ticket-chat-area {
  border-color: rgba(255, 255, 255, 0.16);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .support-ticket-chat-area {
    padding: 30px 30px;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .support-ticket-chat-area {
    padding: 24px 24px;
  }
}

.support-box-wrapper .support-chat-text-item {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 16px 16px;
  justify-content: start;
  width: 45%;
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .support-box-wrapper .support-chat-text-item {
    width: 75%;
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .support-box-wrapper .support-chat-text-item {
    width: 85%;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .support-box-wrapper .support-chat-text-item {
    width: 100%;
  }
}
.support-box-wrapper .support-chat-text-item:not(:last-child) {
  margin-bottom: 20px;
}
.support-box-wrapper .support-chat-text-item.user-message {
  flex-direction: row-reverse;
  margin-inline-start: auto;
  justify-content: end;
}
.support-box-wrapper .support-chat-text-item.user-message .message-list-box .message-list .description {
  color: var(--td-text-primary);
}
.support-box-wrapper .chat-text-avatar {
  flex: 0 0 auto;
}
.support-box-wrapper .chat-text-avatar .thumb {
  width: 40px;
  height: 40px;
}
@media only screen and (max-width: 35.99875rem) {
  .support-box-wrapper .chat-text-avatar .thumb {
    width: 32px;
    height: 32px;
  }
}
.support-box-wrapper .chat-text-avatar .thumb span {
  width: 40px;
  height: 40px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #7879F1;
  border-radius: 50%;
  font-size: 26px;
  color: var(--td-white);
}
@media only screen and (max-width: 35.99875rem) {
  .support-box-wrapper .chat-text-avatar .thumb span {
    width: 32px;
    height: 32px;
  }
}
.support-box-wrapper .chat-text-avatar .thumb img {
  width: 100%;
}
.support-box-wrapper .message-list-box .message-list {
  padding: 12px 12px;
  position: relative;
  background-color: rgba(34, 34, 35, 0.04);
  border: 1px solid rgba(34, 34, 35, 0.1);
  border-radius: 10px;
}
.dark-theme .support-box-wrapper .message-list-box .message-list {
  background-color: rgba(255, 255, 255, 0.04);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.support-box-wrapper .message-list-box .message-list .description {
  line-height: 28px;
}
.dark-theme .support-box-wrapper .message-list-box .message-list .description {
  color: white;
}
@media only screen and (max-width: 35.99875rem) {
  .support-box-wrapper .message-list-box .message-list .description {
    font-size: 14px;
  }
}
.support-box-wrapper .message-list-box .message-list .chat-attachments {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin-top: 5px;
}
.support-box-wrapper .message-list-box .message-list .chat-attachments .attachment {
  padding: 12px 16px;
  font-size: 14px;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  align-items: center;
  gap: 10px;
  background: #F7F7F7;
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
}
.dark-theme .support-box-wrapper .message-list-box .message-list .chat-attachments .attachment {
  background: rgba(2, 10, 34, 0.4509803922);
}
.support-box-wrapper .message-list-box .message-list .author {
  font-size: 14px;
  font-weight: 500;
  color: rgba(8, 8, 8, 0.8);
}
.support-box-wrapper .message-list-box .message-list .timestamp {
  display: block;
  text-align: right;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: -0.03em;
}
.support-box-wrapper .message-list-box .support-meta {
  margin-top: 6px;
  font-size: 14px;
}

.attachment-actions-buttons {
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  gap: 14px 14px;
  flex-wrap: wrap;
  margin-top: 15px;
  justify-content: end;
}
.attachment-actions-buttons .add-attachment,
.attachment-actions-buttons .add-ticket {
  padding: 10px 12px;
  color: var(--td-white);
  border: none;
  cursor: pointer;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  -ms-border-radius: 4px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 14px;
  letter-spacing: -0.03em;
  background: var(--td-primary);
  -webkit-border-radius: 8px;
  -moz-border-radius: 8px;
  -o-border-radius: 8px;
  -ms-border-radius: 8px;
  border-radius: 8px;
}
.attachment-actions-buttons .add-attachment {
  border: 1px solid rgba(8, 8, 8, 0.2);
  background-color: transparent;
  color: #606060;
}
@media only screen and (max-width: 30.06125rem) {
  .attachment-actions-buttons .td-btn {
    width: 100%;
  }
}

.support-attachment-form {
  margin-top: 35px;
}
.support-attachment-form .upload-chat-attachments {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px 12px;
  margin-bottom: 12px;
}
.support-attachment-form .upload-chat-attachments .attachment {
  border-radius: 6.452px;
  border: 1px solid rgba(34, 34, 35, 0.1);
  background: rgba(34, 34, 35, 0.04);
  backdrop-filter: blur(8px);
  border-radius: 6px;
  padding: 6px 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}
.dark-theme .support-attachment-form .upload-chat-attachments .attachment {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
}
.support-attachment-form .upload-chat-attachments .attachment .screenshot {
  flex: 0 0 auto;
  width: 38px;
  height: 38px;
}
.support-attachment-form .upload-chat-attachments .attachment .text {
  font-size: 14px;
}
.support-attachment-form .upload-chat-attachments .attachment .close {
  position: absolute;
  top: -8px;
  inset-inline-end: -6px;
  width: 18px;
  height: 18px;
  background: #EC0707;
  border-radius: 38.5714px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.support-attachment-form form textarea {
  border-radius: 12px;
  background: rgba(34, 34, 35, 0.04);
  border: 1px solid rgba(8, 8, 8, 0.16);
  height: 131px;
}
.dark-theme .support-attachment-form form textarea {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--td-white);
}

/*----------------------------------------*/
/* Referral tree styles
/*----------------------------------------*/
.tree .tree-item {
  position: relative;
  margin-bottom: 5px;
  margin-top: 5px;
}
.tree .tree-item:before {
  content: "";
  position: absolute;
  top: 16px;
  inset-inline-start: -20px;
  width: 20px;
  height: 1px;
  background-color: #334166;
}
.tree .tree-item:first-child:before {
  top: 16px;
}
.tree .tree-item:not(.parent) .tree-content {
  padding-inline-start: 16px;
}
.tree .tree-item.parent {
  cursor: pointer;
}
.tree .tree-content {
  padding: 2px 0;
  display: flex;
  align-items: center;
}
.tree .toggle-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  margin-inline-end: 8px;
}
.tree .toggle-icon img {
  vertical-align: middle;
}
.tree .tree-children {
  position: relative;
  padding-inline-start: 20px;
  margin-inline-start: 10px;
  border-inline-start: 1px solid #334166;
  margin-top: 5px;
  margin-bottom: 5px;
}
.tree .tree-item.root:before {
  display: none;
}
.tree .tree-item.collapsed .tree-children {
  display: none;
}
.tree .name {
  color: var(--td-text-primary);
  font-size: 14px;
}
.tree .name.main-parent {
  font-size: 24px;
  line-height: 1;
  font-weight: 700;
  color: var(--td-white);
}
@media only screen and (max-width: 35.99875rem) {
  .tree .name.main-parent {
    font-size: 20px;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .tree .name.main-parent {
    font-size: 18px;
  }
}

/*----------------------------------------*/
/* Referral program styles
/*----------------------------------------*/
.referral-program-section {
  padding: 50px 50px;
}
@media only screen and (max-width: 35.99875rem) {
  .referral-program-section {
    padding: 30px 30px;
  }
}

.referral-program-wrapper {
  position: relative;
  margin-bottom: 30px;
}
.referral-program-wrapper .referral-contents {
  max-width: 496px;
  margin: 0 auto;
}
.referral-program-wrapper .referral-contents .title {
  margin-bottom: 34px;
}
.referral-program-wrapper .referral-contents .bonus-thumb img {
  width: 16.25rem;
  height: 16.25rem;
}
@media only screen and (max-width: 30.06125rem) {
  .referral-program-wrapper .referral-contents .bonus-thumb img {
    width: 12.5rem;
    height: 12.5rem;
  }
}

.referral-form-input form {
  position: relative;
}
.referral-form-input form .form-input {
  height: 60px;
  border-radius: 2px;
  background: #F6F6F6;
  border: 1px solid rgba(34, 34, 35, 0.16);
}
.dark-theme .referral-form-input form .form-input {
  background: rgba(255, 255, 255, 0.04);
  border-color: rgba(255, 255, 255, 0.1);
  color: var(--td-white);
}
.referral-form-input .referral-btn {
  position: absolute;
  inset-inline-end: 10px;
  top: 50%;
  transform: translateY(-50%);
}
@media only screen and (max-width: 35.99875rem) {
  .referral-form-input .referral-btn {
    width: 100%;
    position: inherit;
    transform: inherit;
    margin-top: 20px;
    top: inherit;
    left: inherit;
    inset-inline-end: 0;
  }
}
.referral-form-input .referral-btn .td-btn {
  height: 40px;
}
@media only screen and (max-width: 35.99875rem) {
  .referral-form-input .referral-btn .td-btn {
    width: 100%;
  }
}
.referral-form-input .description {
  margin-top: 8px;
  color: var(--td-heading);
  font-size: 14px;
}

.referral-bottom {
  margin-top: 50px;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 30px 30px;
}
.referral-bottom .referral-status .status-info ul li {
  list-style: none;
  display: flex;
  gap: 12px;
}
.referral-bottom .referral-status .status-info ul li:not(:last-child) {
  margin-bottom: 14px;
}
.referral-bottom .social-icons {
  display: flex;
  align-items: center;
  gap: 12px 12px;
  flex-wrap: wrap;
  margin-top: 16px;
}
.referral-bottom .social-icons a {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 10px;
  width: 30px;
  height: 30px;
  background: linear-gradient(90deg, rgba(195, 64, 192, 0.16) 0%, rgba(251, 64, 90, 0.16) 50%, rgba(247, 163, 74, 0.16) 100%);
  border-radius: 43px;
}
.referral-bottom .social-icons a:hover {
  transform: translateY(-3px);
}

.referral-bonus-shape {
  position: absolute;
  inset-inline-start: 50%;
  transform: translateX(-50%);
  top: 30%;
  z-index: -1;
  width: 486px;
}
[dir=rtl] .referral-bonus-shape {
  inset-inline-start: auto;
  inset-inline-end: 50%;
}
@media only screen and (max-width: 35.99875rem) {
  .referral-bonus-shape {
    width: 100%;
  }
}

/*----------------------------------------*/
/* Portfolio badge styles
/*----------------------------------------*/
.portfolio-badge-grid {
  display: grid;
  gap: 30px 30px;
  align-items: center;
  grid-template-columns: repeat(auto-fit, minmax(348px, 1fr));
}
@media only screen and (min-width: 100rem) and (max-width: 112.49875rem), only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .portfolio-badge-grid {
    grid-template-columns: repeat(auto-fit, minmax(348px, 1fr));
  }
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .portfolio-badge-grid {
    grid-template-columns: repeat(auto-fit, minmax(305px, 1fr));
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .portfolio-badge-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .portfolio-badge-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (max-width: 35.99875rem) {
  .portfolio-badge-grid {
    grid-template-columns: 1fr;
  }
}

.portfolio-badge-item {
  padding: 40px 20px 75px;
  position: relative;
  border-radius: 24px;
  z-index: 1;
  background-color: var(--td-black);
}
.portfolio-badge-item .portfolio-active {
  position: absolute;
  top: 0;
  inset-block-start: 23px;
}
.portfolio-badge-item .portfolio-active img {
  width: 30px;
  height: 30px;
}
.portfolio-badge-item:hover.badge-lock::after {
  opacity: 1;
  background-color: transparent;
  background-size: 0;
}
.portfolio-badge-item.is-silver {
  box-shadow: 0px 6.629px 39.771px -11.6px #8AA8B5 inset;
}
.portfolio-badge-item.is-silver-pro {
  box-shadow: 0px 6.629px 39.771px -11.6px #3EC5FF inset;
}
.portfolio-badge-item.is-silver-pro::before {
  background: linear-gradient(-185deg, rgb(62, 197, 255) 0%, rgba(62, 197, 255, 0) 100%);
}
.portfolio-badge-item.is-gold::before {
  background: linear-gradient(-185deg, rgb(240, 126, 0) 0%, rgba(240, 126, 0, 0) 100%);
}
.portfolio-badge-item.is-gold-pro::before {
  background: linear-gradient(-185deg, rgb(255, 43, 20) 0%, rgba(255, 43, 20, 0) 100%);
}
.portfolio-badge-item.is-platinum::before {
  background: linear-gradient(-185deg, rgb(250, 40, 243) 0%, rgba(250, 40, 243, 0) 100%);
}
.portfolio-badge-item.is-platinum-pro::before {
  background: linear-gradient(-185deg, rgb(158, 255, 62) 0%, rgba(158, 255, 62, 0) 100%);
}
.portfolio-badge-item.badge-lock:after {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  background-color: rgba(0, 0, 0, 0.7);
  background-image: url(../images/icons/lock.svg);
  background-repeat: no-repeat;
  border-radius: 24px;
  background-size: 70px;
  background-position: center center;
  transition: 0.3s;
}
.portfolio-badge-item .badge-pattern {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
}
.portfolio-badge-item .badge-pattern img {
  width: 100%;
  height: 100%;
  border-radius: 24px;
}
.portfolio-badge-item .portfolio-badge-top {
  text-align: center;
}
.portfolio-badge-item .portfolio-info .title {
  font-family: "Bricolage Grotesque";
  font-size: 30px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  background: linear-gradient(180deg, #FFF 43.1%, rgba(255, 255, 255, 0) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 8px;
}
.portfolio-badge-item .portfolio-info .earning {
  color: var(--td-white);
}
.portfolio-badge-item .features-list {
  margin-top: 30px;
}
.portfolio-badge-item .features-list ul li {
  list-style: none;
  display: flex;
  gap: 8px 8px;
}
.portfolio-badge-item .features-list ul li:not(:last-child) {
  margin-bottom: 12px;
}
.portfolio-badge-item .features-list ul li .text {
  color: white;
}
.dark-theme .portfolio-badge-item .features-list ul li .text {
  color: #9A9DA7;
}
.portfolio-badge-item .badge-icon {
  margin-bottom: 10px;
}
.portfolio-badge-item .badge-icon img {
  height: 100px;
}
@media only screen and (max-width: 30.06125rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .portfolio-badge-item .badge-icon img {
    height: 80px;
  }
}
.portfolio-badge-item::before {
  position: absolute;
  content: "";
  inset: 0;
  padding: 2px;
  background: linear-gradient(-185deg, rgb(138, 168, 181) 0%, rgba(138, 168, 181, 0) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  border-radius: 24px;
  z-index: -1;
}

/*----------------------------------------*/
/*  All wallets styles
/*----------------------------------------*/
.all-wallets-grid {
  display: grid;
  gap: 18px;
  align-items: center;
  grid-template-columns: repeat(auto-fit, minmax(290px, 290px));
}
@media only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .all-wallets-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
@media only screen and (min-width: 48rem) and (max-width: 61.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .all-wallets-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .all-wallets-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (max-width: 30.06125rem) {
  .all-wallets-grid {
    grid-template-columns: 1fr;
  }
}

/*----------------------------------------*/
/* payment status styles
/*----------------------------------------*/
.payment-status-area {
  border-radius: 1rem;
  border: 1px solid rgba(8, 8, 8, 0.16);
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
  padding: 50px 50px;
}
.dark-theme .payment-status-area {
  border: 1px solid #0B277A;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .payment-status-area {
    padding: 30px 30px;
  }
}

.payment-status-wrapper .status-heading {
  margin-bottom: 2.1875rem;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .payment-status-wrapper .status-heading {
    margin-bottom: 1.5625rem;
  }
}
.payment-status-wrapper .status-heading .title {
  font-size: clamp(1.4rem, 2.5vw, 2rem);
}
.payment-status-wrapper .status-icon {
  width: 8.75rem;
  height: 8.75rem;
  margin: 0 auto;
}
.payment-status-wrapper .status-contents {
  margin-top: 2.1875rem;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .payment-status-wrapper .status-contents {
    margin-top: 1.5625rem;
  }
}
.payment-status-wrapper .status-contents .btn-wrap {
  margin-top: 3.4375rem;
}
.payment-status-wrapper .status-contents .description {
  margin-bottom: 1.5rem;
}
.payment-status-wrapper .transaction-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 14px;
  color: var(--td-heading);
  padding: 10px 14px;
  background: #f6f6f6;
  border: 1px solid rgba(8, 8, 8, 0.16);
  border-radius: 32px;
}
.dark-theme .payment-status-wrapper .transaction-badge {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.08);
}

/*----------------------------------------*/
/* Add Money styles
/*----------------------------------------*/
.default-area-style {
  background: var(--td-white);
  border-radius: 16px;
  position: relative;
  z-index: 1;
  border: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .default-area-style {
  background-color: #0C1633;
}
.dark-theme .default-area-style::before {
  position: absolute;
  content: "";
  inset: 0;
  padding: 1px;
  background: linear-gradient(180deg, rgb(11, 39, 122) 0%, rgb(0, 148, 255) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  border-radius: 16px;
  z-index: -1;
}
.default-area-style .heading-top {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0px 30px;
  height: 60px;
  background: #F6F6F6;
  border-radius: 16px 16px 0px 0px;
  margin: 0px 1px;
}
.dark-theme .default-area-style .heading-top {
  background: #16213F;
}
.default-area-style .heading-top .title {
  font-size: 20px;
  font-weight: 700;
  font-family: var(--td-ff-body);
}
.dark-theme .default-area-style .heading-top .title {
  color: var(--td-heading);
}
.default-area-style .default-content-inner {
  padding: 30px 30px 30px;
}
@media only screen and (max-width: 35.99875rem) {
  .default-area-style .default-content-inner {
    padding: 24px 20px 24px;
  }
}

/*----------------------------------------*/
/* Pricing styles
/*----------------------------------------*/
.dark-theme .td-pricing-section {
  background-color: #020A22;
}
.td-pricing-section .pricing-dot-bg {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
}
.dark-theme .td-pricing-section .pricing-dot-bg {
  display: block;
}

.pricing-tab-filter.td-tab {
  position: relative;
  display: inline-block;
  padding: 1px;
}
.pricing-tab-filter.td-tab .inner {
  display: inline-flex;
  position: relative;
  z-index: 3;
  background: var(--td-white);
  clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
  gap: 12px;
  padding: 20px 40px;
}
.dark-theme .pricing-tab-filter.td-tab .inner {
  background: #020a22;
}
@media only screen and (max-width: 35.99875rem) {
  .pricing-tab-filter.td-tab .inner {
    padding: 20px 30px;
    clip-path: none;
  }
}
@media only screen and (max-width: 30.06125rem) {
  .pricing-tab-filter.td-tab .inner {
    padding: 20px 20px;
  }
}
.pricing-tab-filter.td-tab .inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: var(--td-white);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.dark-theme .pricing-tab-filter.td-tab .inner::before {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
}
.pricing-tab-filter.td-tab::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  content: "";
  clip-path: polygon(5.116% 0.575%, 98.367% 0.575%, 98.367% 0.575%, 98.381% 0.579%, 98.395% 0.593%, 98.409% 0.614%, 98.423% 0.644%, 98.436% 0.682%, 98.449% 0.728%, 98.462% 0.782%, 98.474% 0.844%, 98.486% 0.913%, 98.497% 0.99%, 98.512% 1.112%, 99.873% 13.129%, 99.873% 13.129%, 99.885% 13.24%, 99.895% 13.358%, 99.905% 13.483%, 99.913% 13.614%, 99.92% 13.75%, 99.926% 13.891%, 99.931% 14.036%, 99.934% 14.184%, 99.936% 14.334%, 99.937% 14.487%, 99.937% 65.391%, 99.937% 65.391%, 99.936% 65.532%, 99.934% 65.671%, 99.931% 65.807%, 99.928% 65.941%, 99.923% 66.072%, 99.917% 66.199%, 99.91% 66.322%, 99.902% 66.44%, 99.893% 66.552%, 99.883% 66.659%, 99.867% 66.802%, 95.926% 98.823%, 95.926% 98.823%, 95.914% 98.912%, 95.902% 98.993%, 95.889% 99.065%, 95.875% 99.127%, 95.861% 99.181%, 95.847% 99.225%, 95.832% 99.26%, 95.817% 99.285%, 95.802% 99.3%, 95.787% 99.306%, 0.272% 99.306%, 0.272% 99.306%, 0.238% 99.281%, 0.206% 99.209%, 0.176% 99.094%, 0.149% 98.94%, 0.125% 98.751%, 0.104% 98.531%, 0.087% 98.283%, 0.074% 98.011%, 0.066% 97.72%, 0.063% 97.413%, 0.063% 41.89%, 0.063% 41.89%, 0.064% 41.749%, 0.066% 41.61%, 0.068% 41.474%, 0.072% 41.34%, 0.077% 41.209%, 0.083% 41.082%, 0.09% 40.959%, 0.098% 40.841%, 0.107% 40.728%, 0.117% 40.621%, 0.133% 40.479%, 4.976% 1.06%, 4.976% 1.06%, 4.988% 0.97%, 5.001% 0.89%, 5.014% 0.818%, 5.027% 0.755%, 5.041% 0.701%, 5.055% 0.656%, 5.07% 0.621%, 5.085% 0.596%, 5.1% 0.58%, 5.116% 0.575%);
  border-radius: 2px;
}
.dark-theme .pricing-tab-filter.td-tab::before {
  background: linear-gradient(180deg, #091B52 0%, #0094FF 100%);
}
@media only screen and (max-width: 35.99875rem) {
  .pricing-tab-filter.td-tab::before {
    clip-path: none;
  }
}
.pricing-tab-filter.td-tab .nav-tabs {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link {
  position: relative;
  display: inline-block;
  padding: 1px;
  background: transparent;
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link .inner {
  display: inline-flex;
  position: relative;
  z-index: 3;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  gap: 8px;
  padding: 0 20px;
  height: 43px;
  align-items: center;
  justify-content: center;
  color: var(--td-heading);
  font-weight: 600;
}
@media only screen and (max-width: 35.99875rem) {
  .pricing-tab-filter.td-tab .nav-tabs .nav-link .inner {
    padding: 0 16px;
  }
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link .inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) 2.28%, rgba(142, 84, 233, 0.1) 100%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.dark-theme .pricing-tab-filter.td-tab .nav-tabs .nav-link .inner::before {
  background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) -8.58%, rgba(142, 84, 233, 0.1) 100%);
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link .inner .btn-icon {
  width: 20px;
  display: inline-flex;
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link::before {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, #958CFF 0%, #958CFF 100%);
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  border-radius: 2px;
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link.active .inner {
  color: var(--td-white);
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link.active .inner:before {
  background: linear-gradient(90deg, rgb(195, 64, 192) 0%, rgb(251, 64, 90) 50%, rgb(247, 163, 74) 100%);
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link.active:before {
  background: linear-gradient(90deg, rgb(195, 64, 192) 0%, rgb(251, 64, 90) 50%, rgb(247, 163, 74) 100%);
}
.pricing-tab-filter.td-tab .nav-tabs .nav-link.active:after {
  display: none;
}

.pricing-tab-contents {
  max-width: 1920px;
}

.pricing-item {
  height: 100%;
  padding-top: 30px;
}
.pricing-item .pricing-card {
  backdrop-filter: blur(10px);
  border-radius: 5px;
  position: relative;
  z-index: 1;
  height: 100%;
}
.pricing-item .pricing-card .pricing-clip-path {
  position: relative;
  padding: 1px;
  height: 100%;
}
.pricing-item .pricing-card .pricing-card-inner {
  display: flex;
  position: relative;
  flex-direction: column;
  justify-content: space-between;
  gap: 30px;
  z-index: 3;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
  clip-path: polygon(98.095% 0%, 1.905% 0%, 1.905% 0%, 1.596% 0.018%, 1.303% 0.071%, 1.029% 0.155%, 0.78% 0.268%, 0.558% 0.407%, 0.368% 0.569%, 0.213% 0.751%, 0.097% 0.95%, 0.025% 1.164%, 0% 1.389%, 0% 98.493%, 0% 98.493%, 0.025% 98.718%, 0.097% 98.932%, 0.213% 99.131%, 0.368% 99.313%, 0.558% 99.475%, 0.78% 99.614%, 1.029% 99.727%, 1.303% 99.811%, 1.596% 99.864%, 1.905% 99.882%, 86.903% 99.882%, 86.903% 99.882%, 87.041% 99.878%, 87.178% 99.867%, 87.312% 99.849%, 87.444% 99.825%, 87.573% 99.793%, 87.699% 99.755%, 87.821% 99.71%, 87.938% 99.659%, 88.051% 99.601%, 88.158% 99.538%, 99.35% 92.397%, 99.35% 92.397%, 99.468% 92.315%, 99.576% 92.226%, 99.672% 92.131%, 99.757% 92.031%, 99.83% 91.926%, 99.89% 91.817%, 99.938% 91.705%, 99.972% 91.589%, 99.993% 91.472%, 100% 91.352%, 100% 1.389%, 100% 1.389%, 99.975% 1.164%, 99.903% 0.95%, 99.787% 0.751%, 99.632% 0.569%, 99.442% 0.407%, 99.22% 0.268%, 98.971% 0.155%, 98.697% 0.071%, 98.404% 0.018%, 98.095% 0%);
  padding: 80px 40px 40px;
  height: 100%;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .pricing-item .pricing-card .pricing-card-inner {
    padding: 70px 30px 30px;
  }
}
.pricing-item .pricing-card .bg-shape {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.pricing-item .pricing-card .plan-icon {
  position: absolute;
  top: -30px;
  inset-inline-start: 30px;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f4ff;
  backdrop-filter: blur(7.69231px);
  background-repeat: no-repeat;
  background-size: cover;
  z-index: 15;
}
.dark-theme .pricing-item .pricing-card .plan-icon {
  background: #040F30;
}
@media only screen and (max-width: 35.99875rem) {
  .pricing-item .pricing-card .plan-icon {
    width: 80px;
    height: 80px;
  }
}
.pricing-item .pricing-card .plan-icon img {
  width: 50px;
  height: 50px;
}
@media only screen and (max-width: 35.99875rem) {
  .pricing-item .pricing-card .plan-icon img {
    width: 36px;
    height: 36px;
  }
}
.pricing-item .pricing-card .plan-title {
  font-size: 30px;
  margin-top: 20px;
  margin-bottom: 5px;
  color: var(--td-heading);
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .pricing-item .pricing-card .plan-title {
    font-size: 26px;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .pricing-item .pricing-card .plan-title {
    font-size: 24px;
  }
}
.pricing-item .pricing-card .plan-subtitle {
  margin-top: 6px;
  color: #47494E;
  font-size: 14px;
  margin-bottom: 16px;
}
.dark-theme .pricing-item .pricing-card .plan-subtitle {
  color: rgba(255, 255, 255, 0.8);
}
.pricing-item .pricing-card .plan-price {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 30px;
  color: #484848;
  border-bottom: 4px solid #E0E3E9;
  padding-bottom: 20px;
}
.dark-theme .pricing-item .pricing-card .plan-price {
  color: rgba(255, 255, 255, 0.7);
  border-color: rgba(255, 255, 255, 0.1);
}
.pricing-item .pricing-card .price-period {
  font-size: 14px;
  font-weight: 400;
  color: #484848;
}
.dark-theme .pricing-item .pricing-card .price-period {
  color: var(--td-white);
}
.pricing-item .pricing-card .feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}
.pricing-item .pricing-card .feature-check {
  color: #4cd964;
  margin-inline-end: 8px;
  width: 16px;
  flex: 0 0 auto;
}
.pricing-item .pricing-card .features-list ul li {
  list-style: none;
  display: flex;
}
.dark-theme .pricing-item .pricing-card .features-list ul li .feature-text {
  color: rgba(255, 255, 255, 0.8);
}
.pricing-item .pricing-card .features-list ul li:not(:last-child) {
  margin-bottom: 16px;
}
.pricing-item .pricing-shapes .shape-one {
  position: absolute;
  top: 0;
  inset-inline-end: 0;
  z-index: -1;
}
.pricing-item .pricing-shapes .shape-two {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}

.pricing-card .pricing-card-inner {
  display: flex;
  position: relative;
  z-index: 3;
  background: #6D5EFF;
  clip-path: polygon(98.095% 0%, 1.905% 0%, 1.905% 0%, 1.596% 0.018%, 1.303% 0.071%, 1.029% 0.155%, 0.78% 0.268%, 0.558% 0.407%, 0.368% 0.569%, 0.213% 0.751%, 0.097% 0.95%, 0.025% 1.164%, 0% 1.389%, 0% 98.493%, 0% 98.493%, 0.025% 98.718%, 0.097% 98.932%, 0.213% 99.131%, 0.368% 99.313%, 0.558% 99.475%, 0.78% 99.614%, 1.029% 99.727%, 1.303% 99.811%, 1.596% 99.864%, 1.905% 99.882%, 86.903% 99.882%, 86.903% 99.882%, 87.041% 99.878%, 87.178% 99.867%, 87.312% 99.849%, 87.444% 99.825%, 87.573% 99.793%, 87.699% 99.755%, 87.821% 99.71%, 87.938% 99.659%, 88.051% 99.601%, 88.158% 99.538%, 99.35% 92.397%, 99.35% 92.397%, 99.468% 92.315%, 99.576% 92.226%, 99.672% 92.131%, 99.757% 92.031%, 99.83% 91.926%, 99.89% 91.817%, 99.938% 91.705%, 99.972% 91.589%, 99.993% 91.472%, 100% 91.352%, 100% 1.389%, 100% 1.389%, 99.975% 1.164%, 99.903% 0.95%, 99.787% 0.751%, 99.632% 0.569%, 99.442% 0.407%, 99.22% 0.268%, 98.971% 0.155%, 98.697% 0.071%, 98.404% 0.018%, 98.095% 0%);
  padding: 80px 40px 40px;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
}

.td-pricing-two-section {
  background-color: #020A22;
}

.price-two-shapes .shape-one {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
}
.price-two-shapes .shape-two {
  position: absolute;
  inset-inline-end: 0;
  bottom: 0;
}

.pricing-overlay-bg {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: right;
}
.pricing-overlay-bg img {
  width: 100%;
}

.pricing-tab-filter.td-tab.style-two::before {
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgba(255, 255, 255, 0.4) 100%);
}
.dark-theme .pricing-tab-filter.td-tab.style-two .inner {
  background: #020a22;
}
.pricing-tab-filter.td-tab.style-two .inner::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -36.7%, rgba(153, 153, 153, 0) 101.24%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.pricing-tab-filter.td-tab.style-two .nav-tabs .nav-link.active::before {
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
}
.pricing-tab-filter.td-tab.style-two .nav-tabs .nav-link.active .inner {
  color: var(--td-white);
}
.pricing-tab-filter.td-tab.style-two .nav-tabs .nav-link.active .inner:before {
  background: linear-gradient(90deg, #4776E6 0%, #8E54E9 100%);
}
.pricing-tab-filter.td-tab.style-two .nav-tabs .nav-link .inner {
  color: var(--td-heading);
}

.pricing-item.style-two .pricing-card .pricing-card-inner {
  background: var(--td-white);
}
.dark-theme .pricing-item.style-two .pricing-card .pricing-card-inner {
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
}
.pricing-item.style-two .pricing-card .plan-icon {
  background-color: var(--td-white);
}
.dark-theme .pricing-item.style-two .pricing-card .plan-icon {
  background-color: #040F30;
}
.pricing-item.style-two .pricing-card .plan-icon img {
  width: 50px;
  height: 50px;
}
.pricing-item.style-two .pricing-card .plan-subtitle {
  color: #47494E;
}
.dark-theme .pricing-item.style-two .pricing-card .plan-subtitle {
  color: rgba(255, 255, 255, 0.8);
}
.pricing-item.style-two .pricing-card .plan-price {
  color: #484848;
}
.dark-theme .pricing-item.style-two .pricing-card .plan-price {
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 4px solid rgba(255, 255, 255, 0.1);
}
.dark-theme .pricing-item.style-two .pricing-card .features-list ul li .feature-text {
  color: rgba(255, 255, 255, 0.8);
}

.dark-theme .td-faq-section-two {
  background-color: #0C142B;
}

/*----------------------------------------*/
/* Page titles styles
/*----------------------------------------*/
.page-title-wrapper {
  padding-bottom: 16px;
  border-bottom: 2px solid rgba(8, 8, 8, 0.1);
}
.dark-theme .page-title-wrapper {
  border-color: rgba(255, 255, 255, 0.1);
}
.page-title-wrapper .page-title {
  font-size: 24px;
}
@media only screen and (max-width: 35.99875rem) {
  .page-title-wrapper .page-title {
    font-size: 1.25rem;
  }
}

.pages-navigation-nav {
  position: relative;
  display: inline-block;
  padding: 1px;
}
.pages-navigation-nav .pages-navigation-items {
  position: relative;
  display: inline-flex;
  flex-wrap: wrap;
  z-index: 3;
  background: #F8F9FF;
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  gap: 12px;
  padding: 10px;
}
.dark-theme .pages-navigation-nav .pages-navigation-items {
  background: #020a22;
}
.pages-navigation-nav .pages-navigation-items::before {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  content: "";
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -19.04%, rgba(9, 70, 255, 0.06) 53.33%);
  z-index: -1;
  width: 100%;
  height: 100%;
}
.pages-navigation-nav .pages-navigation-items .td-btn {
  height: 36px;
}
.pages-navigation-nav::before, .pages-navigation-nav::after {
  position: absolute;
  inset-inline-start: 0;
  top: 0;
  transition: all 0.3s;
  width: 100%;
  height: 100%;
  background: linear-gradient(0deg, #D9DADC -19.04%, #D9DADC 53.33%);
  content: "";
  clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
  border-radius: 2px;
}
.dark-theme .pages-navigation-nav::before, .dark-theme .pages-navigation-nav::after {
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
}
.pages-navigation-nav::after {
  background: linear-gradient(to left, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
  opacity: 0;
  visibility: hidden;
}
.pages-navigation-nav:hover::after {
  opacity: 1;
  visibility: visible;
}

/*----------------------------------------*/
/*  Dashboard widget styles
/*----------------------------------------*/
.dashboard-widget-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 20px;
}
@media only screen and (max-width: 35.99875rem) {
  .dashboard-widget-grid {
    grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
  }
}

.dashboard-widget-card {
  display: flex;
  align-items: center;
  background: #091628;
  padding: 18px 18px;
  border-radius: 16px;
  gap: 16px;
  position: relative;
  z-index: 1;
}
.dashboard-widget-card::before {
  position: absolute;
  content: "";
  height: 100%;
  width: 100%;
  top: 0;
  inset-inline-start: 0;
  border: 1px solid rgba(8, 8, 8, 0.16);
  border-radius: 16px;
  z-index: -1;
}
.dark-theme .dashboard-widget-card::before {
  border-color: rgba(255, 255, 255, 0.16);
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .dashboard-widget-card {
    padding: 12px 12px;
  }
}
.dashboard-widget-card .icon {
  border-radius: 12px;
  border: 1px solid transparent;
  padding: 10px;
  display: flex;
  flex-direction: row;
  gap: 10px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  width: 50px;
  height: 50px;
  position: relative;
  background-color: #F3E4FF;
}
.dark-theme .dashboard-widget-card .icon {
  border: 1px solid rgba(255, 255, 255, 0.1);
}
.dashboard-widget-card .icon img {
  max-height: 30px;
}
@media only screen and (max-width: 35.99875rem) {
  .dashboard-widget-card .icon {
    width: 44px;
    height: 44px;
  }
  .dashboard-widget-card .icon img {
    width: 26px;
  }
}
.dashboard-widget-card .contents .card-title {
  font-size: 16px;
  font-weight: 700;
}
.dark-theme .dashboard-widget-card .contents .card-title {
  color: #999999;
}
@media only screen and (max-width: 35.99875rem) {
  .dashboard-widget-card .contents .card-title {
    font-size: 14px;
  }
}
.dashboard-widget-card .contents .card-value {
  font-size: 18px;
  font-weight: 500;
  margin-top: 0.25rem;
}
@media only screen and (max-width: 35.99875rem) {
  .dashboard-widget-card .contents .card-value {
    font-size: 16px;
  }
}
.dashboard-widget-card:nth-child(1) {
  background: linear-gradient(74.94deg, #FFFFFF 0%, #EEDAFF 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(1) {
  background: linear-gradient(75deg, #020A22 0%, #341B4A 99.54%);
}
.dashboard-widget-card:nth-child(2) {
  background: linear-gradient(75deg, #FFF 0%, #FFE6DA 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(2) {
  background: linear-gradient(75deg, #020A22 0%, #351F14 99.54%);
}
.dashboard-widget-card:nth-child(3) {
  background: linear-gradient(75deg, #FFF 0%, #FFDAEF 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(3) {
  background: linear-gradient(75deg, #020A22 0%, #341426 99.54%);
}
.dashboard-widget-card:nth-child(4) {
  background: linear-gradient(75deg, #FFF 0%, #FFFDDB 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(4) {
  background: linear-gradient(75deg, #020A22 0%, #353313 99.54%);
}
.dashboard-widget-card:nth-child(5) {
  background: linear-gradient(75deg, #FFF 0%, #E3FFDD 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(5) {
  background: linear-gradient(75deg, #020A22 0%, #172D12 99.54%);
}
.dashboard-widget-card:nth-child(6) {
  background: linear-gradient(75deg, #FFF 0%, #DAFEFF 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(6) {
  background: linear-gradient(75deg, #020A22 0%, #122D2E 99.54%);
}
.dashboard-widget-card:nth-child(7) {
  background: linear-gradient(75deg, #FFF 0%, #DAE8FF 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(7) {
  background: linear-gradient(75deg, #020A22 0%, #1B2C4A 99.54%);
}
.dashboard-widget-card:nth-child(8) {
  background: linear-gradient(75deg, #FFF 0%, #F0D6FF 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(8) {
  background: linear-gradient(75deg, #020A22 0%, #1B1E4A 99.54%);
}
.dashboard-widget-card:nth-child(9) {
  background: linear-gradient(75deg, #FFF 0%, #DAF3FF 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(9) {
  background: linear-gradient(75deg, #020A22 0%, #1B3A4A 99.54%);
}
.dashboard-widget-card:nth-child(10) {
  background: linear-gradient(75deg, #FFF 0%, #FFE4D9 99.54%);
}
.dark-theme .dashboard-widget-card:nth-child(10) {
  background: linear-gradient(75deg, #020A22 0%, #261B4A 99.54%);
}

/*----------------------------------------*/
/* My wallets styles
/*----------------------------------------*/
.users-wallets-grid {
  display: flex;
  align-items: center;
  gap: 20px;
  padding-bottom: 10px;
}
.users-wallets-grid::-webkit-scrollbar {
  width: 5px;
  height: 8px;
}
.users-wallets-grid::-webkit-scrollbar-track {
  background: #0f201e;
}
.users-wallets-grid::-webkit-scrollbar-thumb {
  background-image: linear-gradient(125deg, #7445FF 0%, #7445FF 100%);
  border-radius: 10px;
}
.users-wallets-grid::-webkit-scrollbar-thumb:hover {
  background: #7445FF;
}
.users-wallets-grid .user-wallets-card {
  min-width: 297px;
  height: 152px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem) {
  .users-wallets-grid .user-wallets-card {
    min-width: 250px;
  }
}

.user-wallets-card {
  border-radius: 16px;
  padding: 20px 30px;
  position: relative;
  z-index: 1;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem), only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .user-wallets-card {
    padding: 20px 20px;
  }
}
.user-wallets-card .wallets-contents-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  gap: 20px;
}
.user-wallets-card .wallets-contents-inner .top-options {
  display: flex;
  gap: 8px;
  align-items: center;
}
.user-wallets-card .wallets-contents-inner .top-options .thumb,
.user-wallets-card .wallets-contents-inner .top-options .icon {
  flex: 0 0 auto;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.user-wallets-card .wallets-contents-inner .top-options .thumb img,
.user-wallets-card .wallets-contents-inner .top-options .icon img {
  width: 100%;
}
.user-wallets-card .wallets-contents-inner .top-options .icon {
  background-color: var(--td-primary);
}
.user-wallets-card .wallets-contents-inner .top-options .icon .symbol {
  color: var(--td-white);
  font-weight: 500;
  font-size: 18px;
}
.user-wallets-card .wallets-contents-inner .top-options .wallets-info .title {
  font-size: 16px;
  color: var(--td-white);
  font-weight: 700;
}
.user-wallets-card .wallets-contents-inner .top-options .wallets-info .title span {
  color: var(--td-white);
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem) {
  .user-wallets-card .wallets-contents-inner .top-options .wallets-info .title {
    font-size: 18px;
    line-height: 1.2;
  }
}
.user-wallets-card .wallets-contents-inner .top-options .wallets-info .currency {
  font-size: 14px;
  color: var(--td-white);
  font-weight: 500;
  text-transform: uppercase;
}
.user-wallets-card .wallets-contents-inner.active .top-options .thumb,
.user-wallets-card .wallets-contents-inner.active .top-options .icon {
  background: var(--td-white);
}
.user-wallets-card .wallets-contents-inner.active .top-options .icon .symbol {
  color: var(--td-heading);
}
.user-wallets-card .wallets-contents-inner .wallets-contents .title {
  color: var(--td-white);
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
}
.user-wallets-card .wallets-contents-inner .wallets-contents .card-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
}
.user-wallets-card .wallets-contents-inner .wallets-contents .card-buttons .card-btn {
  border-radius: 26px;
  background: transparent;
  backdrop-filter: blur(25px);
  padding: 5px 10px;
  font-size: 13px;
  color: var(--td-white);
  width: 100%;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--td-white);
}
.user-wallets-card .wallets-pattern {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  border-radius: 16px;
  z-index: -1;
  background-size: 100% 100%;
}
.user-wallets-card .action-button {
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.75);
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(12px);
  width: 36px;
  height: 36px;
  padding: 8px;
  align-items: center;
  color: var(--td-white);
  position: absolute;
  inset-inline-end: 18px;
  top: 12px;
}

/*----------------------------------------*/
/*  User card styles 
/*----------------------------------------*/
.user-balance-card {
  padding: 30px 30px;
  position: relative;
  border-radius: 16px;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.05) -31.74%, rgba(9, 70, 255, 0.03) 88.89%);
  border: 1px solid rgba(34, 34, 35, 0.16);
  z-index: 2;
}
.dark-theme .user-balance-card {
  background: linear-gradient(135deg, #0f1e44, #0d1a3a);
  border: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .user-balance-card {
  border-color: #1c2b56;
}
@media only screen and (max-width: 30.06125rem) {
  .user-balance-card {
    padding: 20px 20px;
  }
}
.user-balance-card .main-balance {
  margin-bottom: 40px;
}
@media only screen and (max-width: 35.99875rem) {
  .user-balance-card .main-balance {
    margin-bottom: 1.875rem;
  }
}
.user-balance-card .main-balance .balance-value {
  font-weight: 700;
  display: flex;
  align-items: center;
  font-size: 24px;
  gap: 4px;
}
.user-balance-card .profit-balance .balance-value {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 700;
}
.user-balance-card .profit-balance .balance-value h5 {
  font-size: 16px;
}
.user-balance-card .balance-label {
  font-size: 16px;
  margin-bottom: 4px;
  display: block;
}
.dark-theme .user-balance-card .balance-label {
  color: #9A9DA7;
}
.user-balance-card .icon-btn {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  color: rgb(154, 157, 167);
  padding: 5px;
  font-size: 20px;
}
.user-balance-card .level-badge {
  position: absolute;
  top: 16px;
  inset-inline-end: 16px;
  padding: 6px 16px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 30px;
  background: linear-gradient(0deg, rgba(6, 16, 49, 0.2) 0%, rgba(62, 197, 255, 0.2) 100%);
  backdrop-filter: blur(15px);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  z-index: 1;
}
@media only screen and (max-width: 30.06125rem) {
  .user-balance-card .level-badge {
    position: initial;
    margin-bottom: 15px;
  }
}
.user-balance-card .level-badge img {
  width: 18px;
}
.user-balance-card .level-badge::before {
  position: absolute;
  content: "";
  border-radius: 30px;
  inset: 0;
  padding: 2px;
  background: linear-gradient(0deg, rgba(62, 197, 255, 0.05) 0%, rgba(62, 197, 255, 0.5) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
.user-balance-card .card-bg {
  position: absolute;
  top: 0;
  inset-inline-start: 0;
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-size: cover;
  z-index: -1;
  opacity: 0.3;
}
.dark-theme .user-balance-card .card-bg {
  opacity: 1;
}
[dir=rtl] .user-balance-card .card-bg {
  transform: rotate(-180deg);
}

.user-referral-card {
  padding: 30px 30px;
  overflow: hidden;
  position: relative;
  border-radius: 16px;
  border: 1px solid rgba(8, 8, 8, 0.16);
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
  backdrop-filter: blur(10px);
}
.dark-theme .user-referral-card {
  border-color: #0B277A;
}
@media only screen and (max-width: 35.99875rem) {
  .user-referral-card {
    padding: 20px 20px 20px;
  }
}
.user-referral-card .referral-contents .title {
  font-size: 20px;
  margin-bottom: 6px;
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .user-referral-card .referral-contents .title {
    font-size: 18px;
  }
}
.user-referral-card .referral-contents .description {
  font-size: 14px;
}
.user-referral-card .referral-input {
  margin-top: 25px;
  position: relative;
}
.user-referral-card .referral-input .input {
  position: relative;
  margin-bottom: 5px;
}
.user-referral-card .referral-input .input input {
  background: rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(8, 8, 8, 0.16);
  border-radius: 0px;
  padding-inline-end: 128px;
  color: #9A9DA7;
  border-radius: 8px;
}
.dark-theme .user-referral-card .referral-input .input input {
  background-color: #141D3B;
}
.dark-theme .user-referral-card .referral-input .input input {
  border-color: rgba(255, 255, 255, 0.1);
}
.user-referral-card .referral-input .input input:focus {
  border-color: var(--td-primary);
}
.user-referral-card .referral-input .input .td-btn {
  position: absolute;
  inset-inline-end: 5px;
  top: 50%;
  transform: translateY(-50%);
  height: 36px;
  font-size: 14px;
  padding: 0px 16px;
  gap: 6px;
  border-radius: 6px;
}
.user-referral-card .referral-input .description {
  font-size: 14px;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/*----------------------------------------*/
/* Mining styles
/*----------------------------------------*/
.history-table-wrapper {
  margin: 0 auto;
  overflow: hidden;
  padding-bottom: 30px;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
  border-radius: 30px;
  position: relative;
  z-index: 1;
}
.history-table-wrapper::before {
  position: absolute;
  content: "";
  inset: 0;
  padding: 1px;
  background: linear-gradient(180deg, rgba(8, 8, 8, 0.16) 0%, rgba(8, 8, 8, 0.16) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  border-radius: 30px;
  z-index: -1;
}
.dark-theme .history-table-wrapper::before {
  background: linear-gradient(180deg, rgb(11, 39, 122) 0%, rgb(0, 148, 255) 100%);
}
.history-table-wrapper .header {
  padding: 16px 30px;
  color: var(--td-heading);
  font-weight: bold;
  font-size: 18px;
  background: rgba(255, 255, 255, 0.08);
}
.dark-theme .history-table-wrapper .header {
  color: var(--td-heading);
}
.history-table-wrapper table {
  width: 100%;
  border-collapse: collapse;
}
.history-table-wrapper th,
.history-table-wrapper td {
  padding: 18px 30px;
  text-align: start;
  border-bottom: 1px solid rgba(8, 8, 8, 0.16);
  color: #8d8fa5;
}
.dark-theme .history-table-wrapper th,
.dark-theme .history-table-wrapper td {
  border-color: rgba(255, 255, 255, 0.16);
}
.history-table-wrapper td {
  font-weight: 400;
}
.history-table-wrapper td:last-child {
  text-align: end;
}
.history-table-wrapper .button-wrapper {
  padding: 0 30px;
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 30px;
}

.mining-history-details-grid {
  display: grid;
  grid-template-columns: 554px 1fr;
}
@media only screen and (min-width: 87.5rem) and (max-width: 99.99875rem), only screen and (min-width: 75rem) and (max-width: 87.49875rem) {
  .mining-history-details-grid {
    grid-template-columns: 500px 1fr;
  }
}
@media only screen and (min-width: 62rem) and (max-width: 74.99875rem) {
  .mining-history-details-grid {
    grid-template-columns: 430px 1fr;
  }
}
@media only screen and (max-width: 35.99875rem), only screen and (min-width: 36rem) and (max-width: 47.99875rem), only screen and (min-width: 48rem) and (max-width: 61.99875rem) {
  .mining-history-details-grid {
    grid-template-columns: 1fr;
  }
}

.mining-history-card-wrapper {
  display: flex;
  padding: 30px 30px;
  align-items: center;
  justify-content: center;
}
@media only screen and (max-width: 30.06125rem) {
  .mining-history-card-wrapper {
    padding: 30px 16px;
  }
}

.mining-history-card {
  position: relative;
  border-radius: 40px 30px 30px;
  padding: 40px 30px 30px;
  width: 345px;
  margin: 1px;
  margin-top: 22px;
  background: #F4EEFD;
  z-index: 2;
}
.mining-history-card::before {
  position: absolute;
  content: "";
  inset: 0;
  border-radius: 40px 30px 30px;
  padding: 1px;
  background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
  -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  z-index: -1;
}
.dark-theme .mining-history-card {
  background-color: #16213F;
  box-shadow: 0px 8px 30px 0px #C840B7 inset;
  backdrop-filter: blur(19.5px);
}
@media only screen and (max-width: 30.06125rem) {
  .mining-history-card {
    width: 100%;
  }
}
.mining-history-card .card-amount {
  text-align: center;
}
.mining-history-card .card-amount .amount {
  font-weight: 800;
  font-size: 24px;
}
.mining-history-card .card-amount .change {
  font-size: 16px;
  color: #A28DFF;
  text-align: center;
  margin-top: 8px;
  font-weight: 500;
}
.mining-history-card .stats-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}
.mining-history-card .stats-grid .stat-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: rgba(8, 8, 8, 0.6);
}
.dark-theme .mining-history-card .stats-grid .stat-row {
  color: #9A9DA7;
}
.mining-history-card .stats-grid .stat-row .stat-name {
  font-weight: 800;
}
.mining-history-card .stats-grid .stat-row .stat-label,
.mining-history-card .stats-grid .stat-row .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: rgba(8, 8, 8, 0.6);
}
.dark-theme .mining-history-card .stats-grid .stat-row .stat-label,
.dark-theme .mining-history-card .stats-grid .stat-row .stat-value {
  color: #9A9DA7;
}
.mining-history-card .btn-inner {
  text-align: center;
  mask-type: 20px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px dashed rgba(8, 8, 8, 0.16);
}
.dark-theme .mining-history-card .btn-inner {
  border-color: rgba(255, 255, 255, 0.16);
}
.mining-history-card .card-badge {
  position: absolute;
  bottom: calc(100% - 20px);
  left: 50%;
  transform: translateX(-50%);
}
.mining-history-card .card-badge .clip-badge {
  color: var(--td-heading);
}

.mining-history-area {
  border-inline-start: 1px solid rgba(8, 8, 8, 0.16);
}
.dark-theme .mining-history-area {
  border-color: rgba(255, 255, 255, 0.16);
}
.mining-history-area .history-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  padding-top: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(8, 8, 8, 0.16);
  padding-left: 30px;
  padding-right: 30px;
}
.mining-history-area .history-row:last-child {
  border-bottom: 0;
}
.mining-history-area .history-row .history-label {
  font-family: var(--td-ff-heading);
  font-size: 16px;
  font-weight: 800;
  flex: 1;
}
.dark-theme .mining-history-area .history-row {
  border-color: rgba(255, 255, 255, 0.16);
}

/*# sourceMappingURL=styles.css.map */
