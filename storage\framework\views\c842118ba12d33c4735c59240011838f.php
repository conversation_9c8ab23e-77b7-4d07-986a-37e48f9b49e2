<?php $__env->startSection('title'); ?>
    <?php echo e(__('Cron Job Logs')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title"> <?php echo e(__('Cron Job Logs')); ?></h2>
                            <a href="<?php echo e(route('admin.cron.jobs.clear.logs',$id)); ?>" class="title-btn red-btn" type="button">
                                    <i data-lucide="trash-2"></i>
                                    <?php echo e(__('Clear Logs')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <div class="row">
                <div class="col-xl-12">
                    <div class="site-card">
                        <div class="site-card-body">
                            <div class="site-table table-responsive">
                                <table class="table">
                                    <thead>
                                    <tr>
                                        <th scope="col"><?php echo e(__('Started At')); ?></th>
                                        <th scope="col"><?php echo e(__('Ended At')); ?></th>
                                        <th scope="col"><?php echo e(__('Duration')); ?></th>
                                        <th scope="col"><?php echo e(__('Error')); ?></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($log->started_at->format('d M Y h:i A')); ?></strong>
                                            </td>
                                            <td>
                                                <strong><?php echo e($log->ended_at->format('d M Y h:i A')); ?></strong>
                                            </td>
                                            <td>
                                                <?php echo e($log->duration); ?> <?php echo e(__('Seconds')); ?>

                                            </td>
                                            <td>
                                                <?php echo e($log->error); ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <td colspan="8" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                                    <?php endif; ?>
                                    </tbody>
                                </table>
                                <?php echo e($logs->links()); ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/cron-jobs/logs.blade.php ENDPATH**/ ?>