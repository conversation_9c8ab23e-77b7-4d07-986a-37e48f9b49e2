<?php $__env->startSection('title'); ?>
    <?php echo e(__('Register')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('style'); ?>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<section class="td-authentication-section">
    <div class="container">
        <div class="auth-main-box">
            <div class="auth-top-wrapper">
                <div class="auth-logo">
                    <a href="<?php echo e(route('home')); ?>">
                        <?php echo $__env->make('frontend::auth.logo', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    </a>
                </div>
                <div class="auth-intro-contents">
                    <h4 class="title"><?php echo e($data['title']); ?></h4>
                </div>
            </div>

            <div class="auth-from-box">
                <form id="register-form" method="POST" action="<?php echo e(route('register')); ?>" novalidate>
                    <?php echo csrf_field(); ?>

                    <div class="auth-form-group">
                        <!-- First Name -->
                        <div class="td-form-group has-left-icon <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="text" name="first_name" class="form-control" id="f-name"
                                       placeholder="First name" required value="<?php echo e(old('first_name')); ?>">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:user"></iconify-icon>
                                </span>
                            </div>
                            <?php $__errorArgs = ['first_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Last Name -->
                        <div class="td-form-group has-left-icon <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="text" name="last_name" class="form-control" id="l-name"
                                       placeholder="Last name" required value="<?php echo e(old('last_name')); ?>">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:user"></iconify-icon>
                                </span>
                            </div>
                            <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Email-->
                        <div class="td-form-group has-left-icon <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="text" name="email" class="form-control" id="email"
                                       placeholder="Email" required value="<?php echo e(old('email')); ?>">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:mail"></iconify-icon>
                                </span>
                            </div>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <!-- Username -->
                        <?php if(getPageSetting('username_show')): ?>
                        <div class="td-form-group has-left-icon <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="text" name="username" class="form-control" id="username"
                                       placeholder="Username" <?php echo e(getPageSetting('username_validation') ? 'required' : ''); ?> value="<?php echo e(old('username')); ?>">
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:at"></iconify-icon>
                                </span>
                            </div>
                            <?php $__errorArgs = ['username'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                        <?php endif; ?>

                        <!-- Country -->
                        <?php if(getPageSetting('country_show')): ?>
                            <div class="td-form-group has-left-icon <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <div class="input-field">
                                    <select name="country" class="form-select select2Icons form-control " id="countrySelect" <?php if(getPageSetting('country_validation')): echo 'required'; endif; ?>>
                                        <option value="" disabled><?php echo e(__('Select Country')); ?></option>
                                        <?php $__currentLoopData = getCountries(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $country): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option
                                                value="<?php echo e($country['name']); ?>:<?php echo e($country['dial_code']); ?>"
                                                data-image="<?php echo e($country['flag']); ?>"
                                                data-code="<?php echo e($country['dial_code']); ?>"
                                                <?php if(old('country', data_get($location, 'name') . ':' . data_get($location, 'dial_code')) == $country['name'] . ':' . $country['dial_code']): echo 'selected'; endif; ?>>
                                                <?php echo e($country['name']); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:location"></iconify-icon>
                                    </span>
                                </div>
                                <?php $__errorArgs = ['country'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        <?php endif; ?>

                        <!-- Phone Number -->
                        <?php if(getPageSetting('phone_show')): ?>
                            <div class="td-form-group has-left-icon <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <div class="input-field input-group">
                                    <span class="input-group-text" id="country-dial-code">
                                        +<?php echo e(old('country_code', '880')); ?>

                                    </span>
                                    <input type="text" name="phone" class="form-control" id="phone"
                                           placeholder="Your phone number" <?php if(getPageSetting('phone_validation')): echo 'required'; endif; ?> value="<?php echo e(old('phone')); ?>">
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:phone-call"></iconify-icon>
                                    </span>
                                </div>
                                <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        <?php endif; ?>

                        <!-- Referral Code -->
                        <?php if(getPageSetting('referral_code_show')): ?>
                            <div class="td-form-group has-left-icon <?php $__errorArgs = ['invite'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <div class="input-field">
                                    <input type="text" name="invite" class="form-control" id="referral-code"
                                           placeholder="Referral Code" value="<?php echo e(request('invite')); ?>" 
                                           <?php if(getPageSetting('referral_code_validation')): ?> required <?php endif; ?>>
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:user-plus"></iconify-icon>
                                    </span>
                                </div>
                                <?php $__errorArgs = ['invite'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        <?php else: ?>
                            <input type="hidden" name="invite" value="<?php echo e(request('invite')); ?>">
                        <?php endif; ?>

                        <!-- Gender -->
                        <?php if(getPageSetting('gender_show')): ?>
                            <div class="td-form-group has-left-icon <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                <div class="input-field">
                                    <select name="gender" class="form-select defaultselect2" <?php if(getPageSetting('gender_validation')): echo 'required'; endif; ?>>
                                        <option value="" disabled selected>Select Gender</option>
                                        <option value="male" <?php if(old('gender') == 'male'): echo 'selected'; endif; ?>>Male</option>
                                        <option value="female" <?php if(old('gender') == 'female'): echo 'selected'; endif; ?>>Female</option>
                                        <option value="other" <?php if(old('gender') == 'other'): echo 'selected'; endif; ?>>Other</option>
                                    </select>
                                    <span class="input-icon">
                                        <iconify-icon icon="tabler:gender-male"></iconify-icon>
                                    </span>
                                </div>
                                <?php $__errorArgs = ['gender'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="feedback-invalid"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        <?php endif; ?>

                        <!-- Password -->
                        <div class="td-form-group has-left-icon has-right-icon <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="password" name="password" class="form-control password-input"
                                       id="password" placeholder="Password" required>
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                </span>
                                <span class="input-icon eyeicon">
                                    <img id="eye-icon-img" class="eye-img" src="<?php echo e(frontendAsset('images/icons/eye.svg')); ?>" alt="eye">
                                </span>
                            </div>
                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Confirm Password -->
                        <div class="td-form-group has-left-icon has-right-icon <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                            <div class="input-field">
                                <input type="password" name="password_confirmation" class="form-control password-input"
                                       id="password_confirmation" placeholder="Confirm Password" required>
                                <span class="input-icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                </span>
                                <span class="input-icon eyeicon">
                                    <img id="eye-icon-img-confirm" class="eye-img" src="<?php echo e(frontendAsset('images/icons/eye.svg')); ?>" alt="eye">
                                </span>
                            </div>
                            <?php $__errorArgs = ['password_confirmation'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>
                    <div class="input-attention-text text-end mt-8">
                        <p class="input-attention"><?php echo e(__('Minimum length is 8 characters.')); ?></p>
                     </div>

                    <!-- Terms & Conditions -->
                    <div class="auth-login-option mt-10 mb-30">
                        <div class="animate-custom">
                            <input class="inp-cbx" id="auth_trams" type="checkbox" name="i_agree" style="display: none;" required>
                            <label class="cbx" for="auth_trams">
                                <span>
                                    <svg width="12px" height="9px" viewBox="0 0 12 9">
                                        <polyline points="1 5 4 8 11 1"></polyline>
                                    </svg>
                                </span>
                                <span>I agree with the <a class="td-underline-btn" href="<?php echo e(url('page/terms-conditions')); ?>">Terms & Conditions</a></span>
                            </label>
                            <?php $__errorArgs = ['i_agree'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="feedback-invalid"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="auth-from-btn-wrap">
                        <button class="td-btn grd-fill-btn-primary w-100 radius-30" type="submit">Sign Up</button>
                    </div>
                </form>
            </div>

            <!-- Bottom Link -->
            <div class="auth-from-bottom-contents mt-10">
                <div class="have-auth-accounts text-center">
                    <p class="description">Already have an account?
                        <a class="td-underline-btn" href="<?php echo e(route('login')); ?>">Sign In</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script'); ?>
    <script>
        $(document).on('change', '#countrySelect', function(e) {
            var country = $(this).val();
            $('#country-dial-code').html(country.split(":")[1])
        });

        $('#countrySelect').trigger('change');
    </script>

<script>
    document.getElementById('eye-icon-img').addEventListener('click', function () {
        const input = document.getElementById('password');
        togglePasswordVisibility(input, this);
    });

    document.getElementById('eye-icon-img-confirm').addEventListener('click', function () {
        const input = document.getElementById('password_confirmation');
        togglePasswordVisibility(input, this);
    });

    function togglePasswordVisibility(input, iconElement) {
        const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
        iconElement.src = type != 'password'
            ? "<?php echo e(frontendAsset('images/icons/eye-open.svg')); ?>"
            : "<?php echo e(frontendAsset('images/icons/eye.svg')); ?>";
    }
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.auth', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/auth/register.blade.php ENDPATH**/ ?>