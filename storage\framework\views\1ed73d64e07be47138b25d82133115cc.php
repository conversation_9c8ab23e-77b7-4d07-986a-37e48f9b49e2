<?php $__env->startSection('title'); ?>
    <?php echo e(__('Cron Jobs')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title"><?php echo e(__('Cron Jobs')); ?></h2>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-create')): ?>
                                <a href="" class="title-btn" type="button" data-bs-toggle="modal"
                                   data-bs-target="#addNewCron">
                                    <i data-lucide="plus-circle"></i><?php echo e(__('Add New')); ?></a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="container-fluid">
            <div class="row">
                <div class="col-xl-12">
                    <div class="site-card">
                        <div class="site-card-body">
                            <div class="site-table table-responsive">
                                <table class="table">
                                    <thead>
                                    <tr>
                                        <th scope="col"><?php echo e(__('Name')); ?></th>
                                        <th scope="col"><?php echo e(__('Run Every')); ?></th>
                                        <th scope="col"><?php echo e(__('Next Run At')); ?></th>
                                        <th scope="col"><?php echo e(__('Last Run At')); ?></th>
                                        <th scope="col"><?php echo e(__('Type')); ?></th>
                                        <th scope="col"><?php echo e(__('Status')); ?></th>
                                        <th scope="col"><?php echo e(__('Action')); ?></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $jobs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $job): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>

                                            <td>
                                                <strong><?php echo e($job->name); ?></strong>
                                            </td>
                                            <td>
                                                <strong><?php echo e($job->schedule); ?> <?php echo e(__('Seconds')); ?></strong>
                                            </td>
                                            <td>
                                                <strong><?php echo e($job->next_run_at->format('d M Y h:i A')); ?></strong> <br>
                                                <span>(<?php echo e($job->next_run_at->diffForHumans()); ?>)</span>
                                            </td>
                                            <td>
                                                <strong><?php echo e($job->last_run_at?->format('d M Y h:i A') ?? '--'); ?></strong> <br>
                                                <span>(<?php echo e($job->last_run_at?->diffForHumans()); ?>)</span>
                                            </td>
                                            <td>
                                                <strong>
                                                    <?php switch($job->type):
                                                        case ('system'): ?>
                                                            <div class="site-badge success"><?php echo e(__('System')); ?></div>
                                                            <?php break; ?>
                                                        <?php case ('custom'): ?>
                                                            <div class="site-badge pending"><?php echo e(__('Custom')); ?></div>
                                                            <?php break; ?>
                                                    <?php endswitch; ?>
                                                </strong>
                                            </td>
                                            <td>
                                                <strong>
                                                    <?php switch($job->status):
                                                        case ('running'): ?>
                                                            <div class="site-badge success"><?php echo e(__('Running')); ?></div>
                                                            <?php break; ?>
                                                        <?php case ('paused'): ?>
                                                            <div class="site-badge pending"><?php echo e(__('Paused')); ?></div>
                                                            <?php break; ?>
                                                    <?php endswitch; ?>
                                                </strong>
                                            </td>

                                            <td>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-logs')): ?>
                                                    <a href="<?php echo e(route('admin.cron.jobs.logs',encrypt($job->id))); ?>" class="round-icon-btn grad-btn" data-bs-toggle="tooltip"
                                                    title="Logs" data-bs-original-title="Logs">
                                                        <i data-lucide="file-cog"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-run')): ?>
                                                    <a href="<?php echo e(route('cron.job',['run_action' => $job->id])); ?>" class="round-icon-btn blue-btn" data-bs-toggle="tooltip" title="Run Now" data-bs-original-title="Run Now">
                                                        <i data-lucide="refresh-ccw"></i>
                                                    </a>
                                                <?php endif; ?>

                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-edit')): ?>
                                                    <button class="round-icon-btn primary-btn editBtn" data-bs-toggle="tooltip"
                                                    title="Edit" data-bs-original-title="Edit" type="button"
                                                            data-cron-job="<?php echo e(json_encode($job)); ?>">
                                                        <i data-lucide="edit-3"></i>
                                                    </button>
                                                <?php endif; ?>

                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-delete')): ?>
                                                    <?php if($job->type == 'custom'): ?>
                                                    <button class="round-icon-btn red-btn delete-btn" data-bs-toggle="tooltip"
                                                    title="Delete" data-bs-original-title="Delete" data-id="<?php echo e(encrypt($job->id)); ?>" type="button">
                                                        <i data-lucide="trash"></i>
                                                    </button>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <td colspan="8" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                                    <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Modal for Add New Earning -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-create')): ?>
            <?php echo $__env->make('backend.cron-jobs.include.__add_new', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>
        <!-- Modal for Add New Earning-->

        <!-- Modal for Edit Earning -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-edit')): ?>
            <?php echo $__env->make('backend.cron-jobs.include.__edit', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>
        <!-- Modal for Edit Earning-->

        <!-- Modal for Delete -->
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('cron-job-delete')): ?>
            <?php echo $__env->make('backend.cron-jobs.include.__delete', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
        <?php endif; ?>
        <!-- Modal for Delete Box End-->

    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('script'); ?>
    <script>
        "use strict";
        
        $('#portfolio_id').select2({
            dropdownParent : $('#addNewEarning'),
            minimumResultsForSearch: Infinity
        });

        $('.editBtn').on('click',function (e) {

            "use strict";

            e.preventDefault();
            var cron = $(this).data('cron-job');

            var url = '<?php echo e(route('admin.cron.jobs.update', ":id")); ?>';
            url = url.replace(':id', cron.id);

            $('#editCronForm').attr('action', url);
            $('#edit-name').val(cron.name);
            $('#edit-schedule').val(cron.schedule);
            $('#edit-next-run').val(moment(cron.next_run_at).format('YYYY-MM-DDThh:mm'));
            $('#edit-url').val(cron.url);
            $('#edit-status').val(cron.status);

            if(cron.type == 'system'){
                $('#url-area').hide();
            }else{
                $('#url-area').show();
            }

            $('#editCron').modal('show');
        });

        $('.delete-btn').on('click',function(){

            var url = '<?php echo e(route('admin.cron.jobs.delete', ":id")); ?>';
            url = url.replace(':id',$(this).attr('data-id'));

            $('#delete-form').attr('action',url);
            $('#deleteModal').modal('show');
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/cron-jobs/index.blade.php ENDPATH**/ ?>