

<?php $__env->startSection('title'); ?>
   <?php echo e(__('Mining Details')); ?>

<?php $__env->stopSection(); ?>
<?php use \App\Enums\TxnType; ?>
<?php use \App\Models\Coin; ?>
<?php use \App\Enums\TxnStatus; ?>
<?php use \App\Enums\UserMiningStatus; ?>
<?php $__env->startSection('content'); ?>
   <div class="col-xxl-12">
      <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
          <h3 class="page-title"><?php echo e(__('Mining Details')); ?></h3>
        </div>
      </div>
      <?php echo $__env->make('frontend::user.mining.include.navigation', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
   </div>
   <div class="col-xxl-12">
      <div class="mining-history-details-area default-area-style">
         <div class="heading-top">
            <h5 class="title"><?php echo e(__('History Details')); ?></h5>
         </div>
            <div class="mining-history-details-grid">
               <div class="mining-history-card-wrapper">
                  <div class="mining-history-card">
                     <div class="card-badge">
                        <div class="td-btn btn-chip grd-outline-fill-btn btn-secondary">
                           <div class="inner-btn">
                              <div class="bitcoin-icon"><?php echo e($transaction->scheme->miner->coin->symbol); ?></div>
                              <span class="btn-text"><?php echo e($transaction->scheme->miner->coin->name); ?></span>
                           </div>
                        </div>
                     </div>
                     <div class="card-amount">
                        <h4 class="amount" data-bs-toggle="tooltip" title="<?php echo e(__('This live calculation is based on the average return amount. The actual amount may vary. Final amount will be updated after next mining.')); ?>"><?php echo e(formatAmount($liveCalData['currentWalletBalance'], $transaction->scheme->miner->coin->code, true)); ?></h4>
                        <h5 class="change"> 
                           <?php if($transaction->scheme->return_amount_type=='min-max'): ?>
                           <small><?php echo e(__('Estimated Earnings')); ?></small>
                           <br>
                           <?php endif; ?>
                           <?php echo e(ucwords($transaction->userMining?->status)); ?></h5>
                     </div>
            
                     <div class="stats-grid">
                        <div class="stat-row">
                           <span class="stat-label"><?php echo e(__('Conversion Rate')); ?></span>
                           <span class="stat-value">~<?php echo cryptoFormat($transaction->scheme->miner->coin->conversion_rate) .' / <sup>'. setting('site_currency', 'global'); ?></sup></span>
                        </div>
                        <div class="stat-row">
                           <span class="stat-label"><?php echo e(__('Last Mining')); ?></span>
                           <span class="stat-value"><?php echo e($transaction->userMining?->last_mining_time ? now()->parse($transaction->userMining?->last_mining_time)->diffForHumans() : 'N/A'); ?></span>
                        </div>
                        <div class="stat-row">
                           <span class="stat-label"><?php echo e(__('Next Mining')); ?></span>
                           <span class="stat-value"><?php echo e($transaction->userMining?->next_mining_time ? now()->parse($transaction->userMining?->next_mining_time)->diffForHumans() : 'N/A'); ?></span>
                        </div>
                        <div class="stat-row">
                           <span class="stat-label"><?php echo e(__('Total Minned Amount')); ?></span>
                           <span class="stat-value"><?php echo e(formatAmount($transaction->userMining?->total_mined_amount, $transaction->scheme->miner->coin->code, true)); ?></span>
                        </div>
                        <div class="stat-row">
                           <span class="stat-label"><?php echo e(__('Speed')); ?></span>
                           <span class="stat-value"><?php echo e($transaction->scheme->speed_amount.' '.$transaction->scheme->speed); ?></span>
                        </div>
                     </div>
                  </div>
               </div>
               <div class="mining-history-area">
                  <div class="history-grid">
                     <div class="history-row">
                         <span class="history-label"><?php echo e(__('Started At')); ?></span>
                         <span class="history-value"><?php echo e($transaction->created_at); ?></span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label"><?php echo e(__('Miner')); ?></span>
                         <span class="history-value"><?php echo e($transaction->scheme->miner->name); ?></span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label"><?php echo e(__('Speed')); ?></span>
                         <span class="history-value highlight-value">
                             <?php echo e($transaction->scheme->speed_amount); ?> <?php echo e($transaction->scheme->speed); ?>

                         </span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label"><?php echo e(__('Return /period')); ?></span>
                         <span class="history-value success-value">
                             <?php if($transaction->scheme->return_amount_type == 'fixed'): ?>
                                 <?php echo e(formatAmount($transaction->scheme->return_amount_value, $transaction->scheme->miner->coin->code, true)); ?>

                             <?php else: ?>
                                 <?php echo e(formatAmount($transaction->scheme->return_min_amount, $transaction->scheme->miner->coin->code, true)); ?>

                                 -
                                 <?php echo e(formatAmount($transaction->scheme->return_max_amount, $transaction->scheme->miner->coin->code, true)); ?>

                             <?php endif; ?>
                         </span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label"><?php echo e(__('Package Title')); ?></span>
                         <span class="history-value"><?php echo e($transaction->scheme->name); ?></span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label"><?php echo e(__('Package Price')); ?></span>
                         <span class="history-value">
                             <?php echo e(formatAmount($transaction->amount, setting('site_currency', 'global'), true)); ?>

                         </span>
                     </div>
                 
                     <div class="history-row">
                         <span class="history-label"><?php echo e(__('Total Period')); ?></span>
                         <span class="history-value"><?php echo e($transaction->scheme->return_period_max_number); ?></span>
                     </div>
                 
                     <?php if($transaction->userMining): ?>
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Mining Count')); ?></span>
                             <span class="history-value"><?php echo e($transaction->userMining?->mining_count); ?></span>
                         </div>
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Remaining Period')); ?></span>
                             <span class="history-value">
                                 <?php echo e($transaction->scheme->return_period_type == 'period' ? $transaction->scheme?->return_period_max_number - $transaction->userMining?->mining_count : 'Unlimited'); ?>

                             </span>
                         </div>
                 
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Total Mined Amount')); ?></span>
                             <span class="history-value">
                                 <?php echo e(formatAmount($transaction->userMining?->total_mined_amount, $transaction->scheme->miner->coin->code, true)); ?>

                             </span>
                         </div>
                 
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Last Mining Time')); ?></span>
                             <span class="history-value">
                                 <?php echo e($transaction->userMining?->last_mining_time ? now()->parse($transaction->userMining?->last_mining_time)->diffForHumans() : 'N/A'); ?>

                             </span>
                         </div>
                 
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Next Mining Time')); ?></span>
                             <span class="history-value">
                                 <?php echo e($transaction->userMining?->next_mining_time ? now()->parse($transaction->userMining?->next_mining_time)->diffForHumans() : 'N/A'); ?>

                             </span>
                         </div>
                 
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Status')); ?></span>
                             <span class="history-value">
                                 <?php echo e(ucwords($transaction->userMining?->status)); ?>

                             </span>
                         </div>
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Transaction ID')); ?></span>
                             <span class="history-value">
                                 <?php echo e(($transaction->tnx)); ?>

                             </span>
                         </div>
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Amount')); ?></span>
                             <span class="history-value">
                                 <?php echo e(trxAmountFormat($transaction,'amount')); ?>

                             </span>
                         </div>
                         <div class="history-row">
                             <span class="history-label"><?php echo e(__('Payment Method')); ?></span>
                             <span class="history-value">
                                 <?php echo e($transaction->method); ?>

                             </span>
                         </div>
                     <?php endif; ?>
                 </div>
               </div>
            </div>
      </div>
   </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('script'); ?>
   <?php if(isset($liveCalData['alreadyPassedTime']) && $transaction->userMining?->status == UserMiningStatus::Ongoing->value): ?>
   <script>
      $(document).ready(function () {

         const earningPerPeriod = <?php echo json_encode($liveCalData['earningPerPeriod']); ?>;
         const returnDuration = <?php echo e($liveCalData['returnDuration']); ?>;
         const alreadyPassedTime = <?php echo e($liveCalData['alreadyPassedTime']); ?>;
         const remainingTime = <?php echo e($liveCalData['remainingTime']); ?>;
         const earningPerSecond = <?php echo e($liveCalData['earningPerSecond']); ?>;
         const currentWalletBalance = <?php echo e($liveCalData['alreadyMinedAmountInBackground'] + $liveCalData['currentWalletBalance']); ?>;
   
         let elapsedTime = alreadyPassedTime;
         let currentEarnings = currentWalletBalance;
   
         function updateEarnings() {
            if (elapsedTime >= returnDuration) return;
   
            currentEarnings += earningPerSecond;
            elapsedTime += 1;
   
            const maxEarning = (earningPerPeriod[0] + earningPerPeriod[1]) / 2;
            if (currentEarnings > maxEarning) {
               currentEarnings = maxEarning;
            }
   
            $('.card-amount .amount').text(currentEarnings.toFixed(8)+' <?php echo e($transaction->scheme->miner->coin->code); ?>');
         }
   
         updateEarnings();
   
         setInterval(updateEarnings, 1000);
      });
   </script>
   <?php endif; ?>

<?php $__env->stopPush(); ?>

<?php echo $__env->make('frontend::layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/mining/details.blade.php ENDPATH**/ ?>