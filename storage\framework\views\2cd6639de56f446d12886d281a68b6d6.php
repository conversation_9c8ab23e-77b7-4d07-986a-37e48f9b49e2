<div class="site-input-groups row">
    <div class="<?php echo e($attributes->get('col-class','col-12')); ?> col-label">
        
        <?php echo e($attributes->get('label','File')); ?>


        <?php if($attributes->has('rec-size')): ?>{
            <i data-lucide="info" data-bs-toggle="tooltip" title=""
               data-bs-original-title="Recommended size: <?php echo e($attributes->get('rec-size','100X42px')); ?>"></i>
        }
            
        <?php endif; ?>
    </div>
    <?php
        $fieldName = $attributes->get('name','file');
        $defaultImage = $attributes->get('default-image','');
    ?>
    <div class="col-12">
        <div class="wrap-custom-file <?php echo e($errors->has($fieldName) ? 'has-error' : ''); ?>">
            <input <?php if($attributes->has('required')): echo 'required'; endif; ?> type="<?php echo e($attributes->get('type','file')); ?>" name="<?php echo e($fieldName); ?>"
                id="<?php echo e($fieldName); ?>" value="<?php echo e($defaultImage); ?>"
                accept=".jpeg, .jpg, .png" />
            <label for="<?php echo e(__($fieldName)); ?>" class="file-ok"
                style="background-image: url( <?php echo e(asset($defaultImage)); ?> )">
                <img class="upload-icon" src="<?php echo e(asset('global/materials/upload.svg')); ?>"
                    alt="" />
                <span><?php echo e(__('Upload') . ' ' . __($attributes->get('label','File'))); ?> </span>
            </label>
            <div style="<?php echo e($defaultImage && file_exists(public_path($defaultImage))); ?>" data-name="<?php echo e($fieldName); ?>"
                    data-title="<?php echo e(__('Upload') . ' ' . __($attributes->get('label','File'))); ?>"
                    class="close remove-img"><i data-lucide="x"></i></div>
        </div>
    </div>
</div><?php /**PATH E:\laragon\www\orexcoin\resources\views/components/admin/drag-and-drop.blade.php ENDPATH**/ ?>