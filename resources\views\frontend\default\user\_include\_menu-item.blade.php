@php
    $hasPermission = match ($navigation->route) {
        'user.addMoney' => (bool) setting('user_deposit', 'permission'),
        'user.withdrawMoney.index' => (bool) setting('user_withdraw', 'permission'),
        'user.tickets' => (bool) setting('user_ticket', 'permission'),
        'user.referral' => (bool) setting('sign_up_referral', 'permission'),
        default => true,
    };
@endphp
@if ($hasPermission)
<li class="slide {{ isActive($navigation->route) }}">
    <div class="clip-path">
       <div class="clip-path-inner">
          <a href="{{ route($navigation->route) }}" class="sidebar-menu-item {{ str($navigation->route)->contains('logout') ? 'has-logout' : '' }}">
             <div class="side-menu-icon">
                <iconify-icon icon="{{ $navigation->icon }}"></iconify-icon>                                                                                        
             </div>
             <span class="sidebar-menu-label">{{ __($navigation->name) }}</span>
          </a>
       </div>
    </div>
 </li>
@endif
