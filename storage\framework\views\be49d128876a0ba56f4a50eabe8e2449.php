<?php $__env->startSection('title'); ?>
    <?php echo e(__('User Dashboard')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('style'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <!-- Container fluid start-->
    <?php echo $__env->make('frontend::user._include.__kyc_alert', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <div class="col-xxl-12">
        <div class="row g-20">
            <div class="col-xxl-6 col-xl-7 col-lg-7 col-md-7">
                <div class="user-balance-card">
                    <?php if(auth()->user()->portfolio): ?>
                        <div class="level-badge">
                            <span>Level <?php echo e(auth()->user()->portfolio->level); ?></span>
                            <img src="<?php echo e(frontendAsset('images/icons/level-icon.png')); ?>" alt="level-icon">
                        </div>
                    <?php endif; ?>
                    <div class="main-balance">
                        <span class="balance-label"><?php echo e(__('Main Balance')); ?></span>
                        <h3 class="balance-value">
                            <span id="mainBalance">
                                <?php echo e(formatAmount(Auth::user()->balance ?? 0, setting('site_currency'), true)); ?>

                            </span>
                            <button class="icon-btn" onclick="toggleBalance('mainBalance', this)">
                                <iconify-icon icon="tabler:eye"></iconify-icon>
                            </button>
                        </h3>
                    </div>
                    <div class="profit-balance">
                        <div class="balance-label"><?php echo e(__('Last Login')); ?></div>
                        <div class="balance-value">
                            <h5 id="profitBalance">
                                <?php echo e(__(':time from :location, Operating System: :os',['time' => $user->lastActivity?->created_at->format('d-m-Y h:i:s a'), 'location' => $user->lastActivity?->location ?? 'Unknown', 'os' => getBrowser($user->lastActivity?->agent)['platform'] ?? 'Unknown'])); ?>

                            </h5>
                        </div>
                    </div>
                    <div class="card-bg" data-background="<?php echo e(frontendAsset('images/bg/balance-card-bg.png')); ?>"
                        style="background-image: url(<?php echo e(frontendAsset('images/bg/balance-card-bg.png')); ?>);"></div>
                </div>
            </div>
            <div class="col-xxl-6 col-xl-5 col-lg-5 col-md-5">
                <div class="user-referral-card">
                    <div class="referral-contents">
                        <h4 class="title"><?php echo e(__('Referral Link')); ?></h4>
                        <p class="description"><?php echo e(__('Share this referral link with your friends and earn money')); ?></p>
                    </div>
                    <div class="referral-input">
                        <div class="input">
                            <input type="text" id="referral-link"
                                value="<?php echo e(route('register', ['refer' => Auth::user()->referral_code])); ?>" readonly>
                            <button type="button" id="copy-btn" class="td-btn btn-xs grd-fill-btn-primary"
                                onclick="copyReferralLink()">
                                <span class="btn-icon">
                                    <iconify-icon icon="tabler:copy"></iconify-icon>
                                </span>
                                <span class="btn-text"><?php echo e(__('Copy Link')); ?></span>
                            </button>
                        </div>
                        <p class="description">
                            <?php echo e($user->referredTo->count() ?? 0); ?> <?php echo e(__('user joined from your referral')); ?>

                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xxl-12">
        <div class="dashboard-widget-area">
            <div class="dashboard-widget-grid">
                <!-- Total Deposit -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-01.png')); ?>" alt="Deposit Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Total Deposits')); ?></p>
                        <h3 class="card-value">
                            <?php echo e($currencySymbol . formatAmount($statistics['total_deposit'])); ?>

                        </h3>
                    </div>
                </div>

                <!-- Total Wallets -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-03.png')); ?>" alt="Wallet Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Total Wallets')); ?></p>
                        <h3 class="card-value"><?php echo e($statistics['total_wallets']); ?></h3>
                    </div>
                </div>

                <!-- Total Withdraws -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-04.png')); ?>" alt="Withdraw Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Total Withdraws')); ?></p>
                        <h3 class="card-value">
                            <?php echo e($currencySymbol . formatAmount($statistics['total_withdraw'])); ?>

                        </h3>
                    </div>
                </div>

                <!-- Referral Bonus -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-06.png')); ?>" alt="Bonus Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Referral Bonus')); ?></p>
                        <h3 class="card-value">
                            <?php echo e($currencySymbol . formatAmount($statistics['referral_bonuses'])); ?>

                        </h3>
                    </div>
                </div>

                <!-- Total Referrals -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-08.png')); ?>" alt="Referral Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Total Referrals')); ?></p>
                        <h3 class="card-value"><?php echo e($statistics['total_referral']); ?></h3>
                    </div>
                </div>

                <!-- Total Transactions -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-05.png')); ?>"
                            alt="Transaction Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Total Transactions')); ?></p>
                        <h3 class="card-value"><?php echo e($statistics['total_transactions']); ?></h3>
                    </div>
                </div>

                <!-- Total Tickets -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-10.png')); ?>" alt="Ticket Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Total Tickets')); ?></p>
                        <h3 class="card-value"><?php echo e($statistics['total_tickets']); ?></h3>
                    </div>
                </div>
                
                <!-- Total Miners -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-09.png')); ?>" alt="Ticket Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Total Miners')); ?></p>
                        <h3 class="card-value"><?php echo e($statistics['total_miners']); ?></h3>
                    </div>
                </div>
                <!-- Active Miners -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-07.png')); ?>" alt="Ticket Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Active Miners')); ?></p>
                        <h3 class="card-value"><?php echo e($statistics['total_active_miners']); ?></h3>
                    </div>
                </div>
                <!-- Completed Miners -->
                <div class="dashboard-widget-card">
                    <div class="icon">
                        <img src="<?php echo e(frontendAsset('/images/dashboard-widget/icon-10.png')); ?>" alt="Ticket Icon">
                    </div>
                    <div class="contents">
                        <p class="card-title"><?php echo e(__('Completed Miners')); ?></p>
                        <h3 class="card-value"><?php echo e($statistics['total_completed_miners']); ?></h3>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="col-xxl-12">
        <!-- my wallets box -->
        <div class="my-wallets-area">
            <div class="card-heading">
                <h4 class="title"><?php echo e(__('My Wallets')); ?></h4>
                <div class="link-inner">
                    <a class="td-underline-btn" href="<?php echo e(route('user.userWallet.index')); ?>">
                        <?php echo e(__('See all')); ?>

                    </a>
                </div>
            </div>
            <div class="my-wallets-inner">
                <div class="users-wallets-grid table-responsive">
                    <?php echo $__env->make('frontend::user._include.__main_wallet_card', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php $__currentLoopData = $userWallets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $userWallet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php echo $__env->make(
                            'frontend::user._include.__wallet_card',
                            [
                                'userWallet' => $userWallet,
                            ]
                        , array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xxl-12">
        <div class="recent-history-table">
            <div class="card-heading">
                <h4 class="title"><?php echo e(__('Recent History')); ?></h4>
                <div class="link-inner">
                    <a class="td-underline-btn" href="<?php echo e(route('user.transactions')); ?>">
                        <?php echo e(__('See all')); ?>

                    </a>
                </div>
            </div>
            <!-- Recent history table -->
                <?php echo $__env->make('frontend::user.transaction.table', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
            </div>
        </div>
        <div class="modal default-model is-verify-modal fade" id="verifyIdentity" tabindex="-1" aria-labelledby="verifyIdentityLabel" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-dialog-centered">
               <div class="modal-content">
                  <!-- modal if you need close btn -->
                  <!-- <button type="button" class="modal-btn-close" data-bs-dismiss="modal" aria-label="Close"><iconify-icon icon="tabler:x"> </button> -->
                  <div class="verify-identity-modal text-center">
                     <div class="modal-body">
                        <div class="verify-modal-wrapper">
                           <div class="verify-modal-icon">
                              <img src="<?php echo e(frontendAsset('images/icons/message.svg')); ?>" alt="Verify Message">
                           </div>
                           <div class="verify-modal-contents">
                           <h3 class="title"><?php echo e(__('We’re verifying your identity')); ?></h3>
                           <p>  <?php echo e(__('It should take a moment to verify you.')); ?></p>
                        </div>
                        </div>
                        <div class="modal-buttons mt-35">
                           <a href="<?php echo e(route('user.setting.index','kyc')); ?>" type="button" class="td-btn td-btn btn-chip grd-fill-btn-primary btn-m-w">
                              <span class="btn-text"><?php echo e(__('Submit Now')); ?></span>
                           </a>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
        </div>
        <?php echo $__env->make('frontend::user._include.__signup_bonus_popup', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
 <?php $__env->stopSection(); ?>
 
<?php $__env->startPush('js'); ?>
     <script>
         function toggleBalance(id, btn) {
             const el = document.getElementById(id);
            const icon = btn.querySelector('iconify-icon');

            // Save the original balance if not saved yet
           if (!el.dataset.original) {
               el.dataset.original = el.textContent;
             }

            const isHidden = el.textContent === '••••••';

             if (isHidden) {
               el.textContent = el.dataset.original;
              icon.setAttribute('icon', 'tabler:eye');
          } else {
             el.textContent = '••••••';
             icon.setAttribute('icon', 'tabler:eye-off');
           }
         }
      </script>

      <script>
        function copyReferralLink() {
            const referralLink = document.getElementById('referral-link');
            const copyBtn = document.getElementById('copy-btn'); // Use ID without dot
            const inputDiv = document.querySelector('.referral-input .input');
           const btnText = copyBtn.querySelector('.btn-text'); // This is inside the button

           referralLink.select();
            referralLink.setSelectionRange(0, 99999); // For mobile devices
            document.execCommand('copy');

            inputDiv.classList.add('copied');

             // Change only the text span
            btnText.textContent = 'Copied!';

          setTimeout(() => {
              inputDiv.classList.remove('copied');
              btnText.textContent = 'Copy Link';
           }, 2000);
        }
     </script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('frontend::layouts.user', ['gyClass' => 'gy-20'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/dashboard.blade.php ENDPATH**/ ?>