@use '../utils' as *;

/*----------------------------------------*/
/* Common Classes
/*----------------------------------------*/
.w-img {
    & img {
        width: 100%;
    }
}

.m-img {
    & img {
        max-width: 100%;
    }
}

.fix {
    overflow: hidden
}

.o-x-clip {
    overflow-x: clip;
}


.clear {
    clear: both;
}

.f-left {
    float: left
}

.f-right {
    float: right
}

.zi-1 {
    z-index: 1;
}

.zi-11 {
    z-index: 11;
}

.p-relative {
    position: relative;
}

.p-absolute {
    position: absolute;
}

.position-absolute {
    position: absolute;
}

.include-bg {
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
}

.hr-1 {
    border-top: 1px solid rgb(232, 232, 232);
}

.x-clip {
    overflow-x: clip;
}

.o-visible {
    overflow: visible;
}

.valign {
    @include flexbox();
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
}