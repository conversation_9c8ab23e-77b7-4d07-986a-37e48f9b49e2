@use '../utils' as *;

/*----------------------------------------*/
/* Default Spacing
/*----------------------------------------*/

.section_space {
    padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem);
    padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space_medium {
    padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem);
    padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space_small {
    padding-block-start: clamp(3.75rem, 5vw + 1rem, 3.125rem);
    padding-block-end: clamp(3.75rem, 5vw + 1rem, 3.125rem);
}

.section_space_top {
    padding-block-start: clamp(3.75rem, 5vw + 1rem, 3.125rem);
    padding-block-end: clamp(3.75rem, 5vw + 1rem, 3.125rem);
}

.section_space_bottom {
    padding-block-start: clamp(3.75rem, 5vw + 1rem, 3.125rem);
    padding-block-end: clamp(3.75rem, 5vw + 1rem, 3.125rem);
}

.section_space-top {
    padding-block-start: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_space-bottom {
    padding-block-end: clamp(3.75rem, 5vw + 1rem, 5rem);
}

.section_title_space {
    margin-bottom: 48px;

    @media #{$md} {
        margin-bottom: 44px;
    }

    @media #{$xs,$sm} {
        margin-bottom: 38px;
    }
}