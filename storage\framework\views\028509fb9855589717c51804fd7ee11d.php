<?php if($paginator->hasPages()): ?>
    <div class="table-pagination">
        <nav>
            <ul>
                <?php if($paginator->onFirstPage()): ?>
                    <li>
                        <a href="javascript:;">
                            <i class="hugeicons--arrow-left-01"></i>
                        </a>
                    </li>
                <?php else: ?>
                    <li>
                        <a href="<?php echo e($paginator->previousPageUrl()); ?>" rel="prev" aria-label="<?php echo app('translator')->get('pagination.previous'); ?>">
                            <i class="hugeicons--arrow-left-01"></i>
                        </a>
                    </li>
                <?php endif; ?>

                <?php $__currentLoopData = $elements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $element): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(is_string($element)): ?>
                        <li class="disabled" aria-disabled="true">
                            <span><?php echo e($element); ?></span>
                        </li>
                    <?php endif; ?>

                    <?php if(is_array($element)): ?>
                        <?php $__currentLoopData = $element; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $page => $url): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php if($page == $paginator->currentPage()): ?>
                                <li>
                                    <a class="current" href="#"><?php echo e($page); ?></a>
                                </li>
                            <?php else: ?>
                                <li>
                                    <a href="<?php echo e($url); ?>"><?php echo e($page); ?></a>
                                </li>
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                <?php if($paginator->hasMorePages()): ?>
                    <li>
                        <a href="<?php echo e($paginator->nextPageUrl()); ?>" rel="next" aria-label="<?php echo app('translator')->get('pagination.next'); ?>">
                            <i class="hugeicons--arrow-right-01"></i>
                        </a>
                    </li>
                <?php else: ?>
                    <li>
                        <a href="javascript:;">
                            <i class="hugeicons--arrow-right-01"></i>
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
<?php endif; ?>
<?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/_include/_pagination.blade.php ENDPATH**/ ?>