@use '../../utils' as *;

/*----------------------------------------*/
/* My wallets styles
/*----------------------------------------*/
.users-wallets-grid {
    display: flex;
    align-items: center;
    gap: 20px;
    padding-bottom: 10px;

    &::-webkit-scrollbar {
        width: 5px;
        height: 8px;
    }

    &::-webkit-scrollbar-track {
        background: #0f201e;
    }

    &::-webkit-scrollbar-thumb {
        background-image: linear-gradient(125deg, #7445FF 0%, #7445FF 100%);
        border-radius: 10px;

    }

    &::-webkit-scrollbar-thumb:hover {
        background: #7445FF;

    }

    .user-wallets-card {
        min-width: 297px;
        height: 152px;

        @media #{$xs,$sm} {
            min-width: 250px;
        }
    }
}

.user-wallets-card {
    border-radius: 16px;
    padding: 20px 30px;
    position: relative;
    z-index: 1;

    @media #{$xs,$lg,$xl,$xxl} {
        padding: 20px 20px;
    }

    .wallets-contents-inner {
        display: flex;
        flex-direction: column;
        height: 100%;
        justify-content: space-between;
        gap: 20px;

        .top-options {
            display: flex;
            gap: 8px;
            align-items: center;

            .thumb,
            .icon {
                flex: 0 0 auto;
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: inline-flex;
                align-items: center;
                justify-content: center;

                img {
                    width: 100%;
                }
            }

            .icon {
                background-color: var(--td-primary);

                .symbol {
                    color: var(--td-white);
                    font-weight: 500;
                    font-size: 18px;
                }
            }

            .wallets-info {
                .title {
                    font-size: 16px;
                    color: var(--td-white);
                    font-weight: 700;

                    span {
                        color: var(--td-white);
                    }

                    @media #{$xxl} {
                        font-size: 18px;
                        line-height: 1.2;
                    }
                }

                .currency {
                    font-size: 14px;
                    color: var(--td-white);
                    font-weight: 500;
                    text-transform: uppercase;
                }
            }
        }

        &.active {
            .top-options {

                .thumb,
                .icon {
                    background: var(--td-white);
                }

                .icon {
                    .symbol {
                        color: var(--td-heading);
                    }
                }
            }
        }

        .wallets-contents {
            .title {
                color: var(--td-white);
                font-size: 16px;
                font-style: normal;
                font-weight: 400;
            }

            .card-buttons {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-top: 16px;

                .card-btn {
                    border-radius: 26px;
                    background: transparent;
                    backdrop-filter: blur(25px);
                    padding: 5px 10px;
                    font-size: 13px;
                    color: var(--td-white);
                    width: 100%;
                    font-weight: 600;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    border: 1px solid var(--td-white);
                }
            }
        }
    }

    .wallets-pattern {
        position: absolute;
        top: 0;
        inset-inline-start: 0;
        width: 100%;
        height: 100%;
        background-repeat: no-repeat;
        border-radius: 16px;
        z-index: -1;
        background-size: 100% 100%;
    }

    .action-button {
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.75);
        background: rgba(255, 255, 255, 0.06);
        backdrop-filter: blur(12px);
        width: 36px;
        height: 36px;
        padding: 8px;
        align-items: center;
        color: var(--td-white);
        position: absolute;
        inset-inline-end: 18px;
        top: 12px;
    }
}