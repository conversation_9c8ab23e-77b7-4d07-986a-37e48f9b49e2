<?php $__env->startSection('title'); ?>
    <?php echo e(__('Miners')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="row">
                <div class="col">
                    <div class="title-content">
                        <h2 class="title"><?php echo e(__('Miners')); ?></h2>
                        <div>
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('miner-create')): ?>
                                <a href="<?php echo e(route('admin.miner.create')); ?>" class="title-btn">
                                    <i icon-name="plus-circle"></i>
                                    <?php echo e(__('Add New')); ?>

                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xl-12">
                <div class="site-card">
                    <div class="site-card-body table-responsive">
                        <div class="site-datatable">
                            <table id="dataTable" class="display data-table">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__('SL No')); ?></th>
                                        <th><?php echo e(__('Name')); ?></th>
                                        <th><?php echo e(__('Coin')); ?></th>
                                        <th><?php echo e(__('Renewable Energy')); ?></th>
                                        <th><?php echo e(__('Uptime')); ?></th>
                                        <th><?php echo e(__('Network Hashrate')); ?></th>
                                        <th><?php echo e(__('Status')); ?></th>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $miners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $miner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($key + 1); ?></td>
                                            <td><?php echo e($miner->name); ?></td>
                                            <td><?php echo e($miner->coin->name); ?> (<?php echo e($miner->coin->symbol); ?>)</td>
                                            <td><?php echo e($miner->renewable_energy); ?> %</td>
                                            <td><?php echo e($miner->uptime); ?> %</td>
                                            <td><?php echo e($miner->network_hashrate_amount); ?> <?php echo e($miner->network_hashrate); ?></td>
                                            <td>
                                                <?php if($miner->status == 'active'): ?>
                                                    <span class="site-badge success"><?php echo e(__('Active')); ?></span>
                                                <?php else: ?>
                                                    <span class="site-badge danger"><?php echo e(__('Inactive')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('miner-edit')): ?>
                                                    <a href="<?php echo e(route('admin.miner.edit', $miner->id)); ?>"
                                                        class="round-icon-btn primary-btn" data-bs-toggle="tooltip"
                                                        title="Edit Miner" data-bs-original-title="Edit Miner">
                                                        <i data-lucide="edit-3"></i>
                                                    </a>
                                                <?php endif; ?>
                                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('miner-delete')): ?>
                                                    <?php if (isset($component)) { $__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.delete-module-popup','data' => ['module' => 'Miner','method' => 'get','deleteRoute' => route('admin.miner.delete', $miner->id)]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.delete-module-popup'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['module' => 'Miner','method' => 'get','delete-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.miner.delete', $miner->id))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33)): ?>
<?php $attributes = $__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33; ?>
<?php unset($__attributesOriginal0af61fdba84e7b6b9bbef22c43ef9f33); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33)): ?>
<?php $component = $__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33; ?>
<?php unset($__componentOriginal0af61fdba84e7b6b9bbef22c43ef9f33); ?>
<?php endif; ?>
                                                <?php endif; ?>

                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <td colspan="8" class="text-center"><?php echo e(__('No Miners Found!')); ?></td>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>


<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/miner/index.blade.php ENDPATH**/ ?>