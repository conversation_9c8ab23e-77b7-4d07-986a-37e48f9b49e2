@use '../../utils' as *;

/*----------------------------------------*/
/* Notifications styles
/*----------------------------------------*/
.notifications-list-wrapper {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.notifications-list-grid {
    display: grid;
    align-items: center;
    grid-template-columns: 1fr auto;
    gap: 12px 16px;
    background-color: #FBF9FE;
    border: 1px solid rgba($heading, $alpha: 0.1);
    border-radius: rem(6);
    padding: 19px 20px;

    @media #{$xs,$sm,$md} {
        display: flex;
        flex-direction: column;
        align-items: start;
    }

    @include dark-theme {
        background-color: rgba($white, $alpha: 0.02);
        border-color: rgba($white, $alpha: 0.1);
    }

    &:nth-child(1) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #88333C;
                    }
                }
            }
        }
    }

    &:nth-child(2) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #33884D;
                    }
                }
            }
        }
    }

    &:nth-child(3) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #33884D;
                    }
                }
            }
        }
    }

    &:nth-child(4) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #808833;
                    }
                }
            }
        }
    }

    &:nth-child(5) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #6596F4;
                    }
                }
            }
        }
    }

    &:nth-child(6) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #800AF6;
                    }
                }
            }
        }
    }

    &:nth-child(6n+1) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #00B4D8;
                    }
                }
            }
        }
    }

    &:nth-child(6n+2) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #E56BFF;
                    }
                }
            }
        }
    }

    &:nth-child(6n+3) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #29B475;
                    }
                }
            }
        }
    }

    &:nth-child(6n+4) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #808833;
                    }
                }
            }
        }
    }

    &:nth-child(6n+5) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #6596F4;
                    }
                }
            }
        }
    }

    &:nth-child(6n+6) {
        .notifications-lists {
            a {
                .notification-item {
                    .icon {
                        background-color: #800AF6;
                    }
                }
            }
        }
    }
}

// Notification Styles
.notification-panel-box {
    position: relative;
}

.notification-panel {
    width: 440px;
    position: absolute;
    top: calc(100% + 12px);
    visibility: hidden;
    display: block;
    inset-inline-end: 0;
    z-index: 9;
    transform: translateY(-20px);
    opacity: 0;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
    border-radius: 8px;
    border: 1px solid #E1E4EA;
    background: var(--td-white);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.08), 0px 8px 26px 0px rgba(0, 0, 0, 0.12);
    overflow: hidden;

    @include dark-theme {
        border-color: #0B277A;
    }

    @media #{$xs} {
        width: 400px;
        inset-inline-end: 0px;
    }

    @media #{$mobile-sm} {
        width: 355px;
        inset-inline-end: 0px;
    }

    @media #{$mobile-xs} {
        width: 340px;
        inset-inline-end: -30px;
    }

    @media #{$mobile-xxs} {
        width: 300px;
        inset-inline-end: -50px;
    }

    @include dark-theme {
        background: #171C35;
    }

    &.active {
        visibility: visible;
        opacity: 1;
        transform: translateY(0px);
    }

    .notification-item {
        &:not(:last-child) {
            margin-bottom: 12px;
        }
    }
}

.notifications-inner {
    padding: 10px 20px 10px;
    height: 360px;
    overflow-y: scroll;
    margin-inline-end: 10px;
    margin-top: 10px;
    margin-bottom: 10px;

    &::-webkit-scrollbar {
        width: rem(5);
    }

    &::-webkit-scrollbar-track {
        background: rgba($white, $alpha: 0.12);
    }

    &::-webkit-scrollbar-thumb {
        background-image: linear-gradient(125deg, #555 0%, #555 100%);
        border-radius: rem(10);

    }
}

.notification-header {
    background: #FBF9F7;

    @include dark-theme {
        background: #1c2339;
    }

    .heading-top {
        @include flexbox();
        justify-content: space-between;
        align-items: center;
        padding: 20px 16px 10px;

        .title {
            font-size: 18px;
            font-family: var(--td-ff-body);
            font-weight: 700;
        }
    }
}

.notification-btn-close {
    width: 32px;
    height: 32px;
    @include inline-flex();
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    z-index: 2;
    font-size: 20px;
    background-color: rgba($heading, $alpha: 0.1);

    @include dark-theme {
        background-color: rgba($heading, $alpha: 0.3);
    }
}

.notifications-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    @media #{$xs} {
        margin-bottom: 12px;
    }

    button {
        font-size: 14px;
        font-weight: 500;

        &:hover {
            color: var(--td-white);
        }
    }
}

.notifications-middle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px 10px;

    .middle-buttons {
        display: flex;
        align-items: center;
        gap: 20px;

        @media #{$xs} {
            gap: 10px;
        }
    }
}

.notification-list {
    display: flex;

    .notification-item {
        display: flex;
        align-items: self-start;
        column-gap: 12px;

        .icon {
            width: 34px;
            height: 34px;
            flex: 0 0 auto;
            border-radius: 50%;
            background: #88333C;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: var(--td-white);

            img {
                width: 18px;
            }
        }

        .contents {
            .title {
                font-weight: 700;
                font-size: 16px;
            }

            .message {
                margin-top: 5px;
                font-size: 14px;
                margin-bottom: 0;
            }

            .time {
                font-size: 12px;
                margin-top: 10px;
                display: block;
            }
        }
    }
}

.notifications-lists {
    display: flex;
    flex-direction: column;
    row-gap: 18px;

    a {
        &:nth-child(1) {
            .notification-item {
                .icon {
                    background-color: #88333C;
                }
            }
        }

        &:nth-child(2) {
            .notification-item {
                .icon {
                    background-color: #33884D;
                }
            }
        }

        &:nth-child(3) {
            .notification-item {
                .icon {
                    background-color: #337788;
                }
            }
        }

        &:nth-child(4) {
            .notification-item {
                .icon {
                    background-color: #808833;
                }
            }
        }

        &:nth-child(5) {
            .notification-item {
                .icon {
                    background-color: #6596F4;
                }
            }
        }

        &:nth-child(6) {
            .notification-item {
                .thumb {
                    background-color: #800AF6
                }
            }
        }

        &:nth-child(6n+1) {
            .notification-item {
                .icon {
                    background-color: #00B4D8;
                }
            }
        }

        &:nth-child(6n+2) {
            .notification-item {
                .thumb {
                    background-color: #E56BFF;
                }
            }
        }

        &:nth-child(6n+3) {
            .notification-item {
                .icon {
                    background-color: #29B475;
                }
            }
        }

        &:nth-child(6n+4) {
            .notification-item {
                .icon {
                    background-color: #E74B54;
                }
            }
        }

        &:nth-child(6n+5) {
            .notification-item {
                .icon {
                    background-color: #6596F4;
                }
            }
        }

        &:nth-child(6n+6) {
            .notification-item {
                .icon {
                    background-color: #9e69d2
                }
            }
        }
    }
}

// notifications box
.notifications-box {
    .notifications-drop-btn {
        &::after {
            display: none;
        }
    }

    .dropdown-menu {
        background: #101016;
        backdrop-filter: blur(50px);
        @include border-radius(20px);
        width: 442px;
        top: 150% !important;
        padding: 24px 24px 24px;
        border: 0;
        inset-inline-end: 0;

        @media #{$xs,$sm,$md} {
            transform: translateX(35%);
        }

        @media #{$xxs} {
            transform: translateX(45%);
        }

        @media #{$xxs} {
            width: 350px;
            padding: 15px 15px;
        }

        @media (max-width: 360px) {
            width: 300px;
            padding: 15px 15px;
            inset-inline-end: -15px;
        }

        &:before {
            position: absolute;
            content: "";
            inset: 0;
            @include border-radius(20px);
            padding: 2px;
            background: linear-gradient(139.9deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.4) 100%);
            -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            z-index: -1;
        }
    }

    .notifications-top-content {
        @include flexbox();
        align-items: center;
        justify-content: space-between;
        margin-bottom: 27px;

        @media #{$xxs} {
            margin-bottom: 17px;
        }

        .title {
            color: var(--td-white);
            font-size: 16px;
            font-weight: 800;

            @media #{$xxs} {
                font-size: 14px;
            }
        }

        .link {
            color: var(--td-white);
            font-size: 14px;
            font-weight: 500;

            @media #{$xxs} {
                font-size: 12px;
            }

            &:hover {
                color: var(--td-primary);
            }
        }
    }

    .notifications-info-wrapper {
        height: 280px;
        overflow-y: scroll;
        scrollbar-width: thin;
        padding-inline-end: 5px;
    }

    .notifications-info-list {
        ul {
            li {
                list-style: none;

                .list-item {
                    @include flexbox();
                    align-items: start;
                    gap: 10px;
                    border: 1px solid rgba($color: $white, $alpha: .1);
                    @include border-radius(12px);
                    padding: 10px 10px;

                    .content {
                        .title {
                            font-size: 14px;
                            font-weight: 700;
                            color: var(--td-white);

                            @media #{$xxs} {
                                font-size: 12px;
                            }
                        }

                        .info {
                            font-size: 11px;
                            color: var(--td-white);
                        }
                    }

                    &:hover {
                        background: rgba($color: $white, $alpha: .1);
                    }
                }

                &:not(:last-child) {
                    margin-bottom: 6px;
                }
            }
        }
    }

    .notifications-bottom-content {
        margin-top: 24px;

        @media #{$xxs} {
            margin-top: 14px;
        }

        .notifications-btn {
            background: rgba($white, $alpha: 0.08);
            border: 1px solid rgba($white, $alpha: 0.08);
            @include border-radius(12px);
            width: 100%;
            @include inline-flex();
            align-items: center;
            justify-content: center;
            height: 38px;
            color: var(--td-white);
            font-size: 14px;
            font-weight: 800;
        }
    }
}