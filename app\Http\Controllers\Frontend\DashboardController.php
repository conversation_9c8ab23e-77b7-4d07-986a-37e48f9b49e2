<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\CoinStatus;
use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Enums\UserMiningStatus;
use App\Http\Controllers\Controller;
use App\Models\Coin;
use App\Models\Transaction;
use App\Models\UserWallet;
use App\Services\TransactionReport;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class DashboardController extends Controller
{
    protected TransactionReport $report;

    public function __construct(TransactionReport $report)
    {
        $this->report = $report;
    }

    public function dashboard(Request $request)
    {

        $data['earnings'] = [
            'amount' => [],
            'months' => [],
        ];

        $user = Auth::user();

        $transactions = Transaction::with('wallet.coin')->where('user_id', $user->id)
            ->when($request->filled('txn'), function ($query) use ($request) {
                $query->where('tnx', 'like', '%'.$request->input('txn').'%');
            })
            ->when($request->filled('status') && $request->input('status') != 'All', function ($query) use ($request) {
                $query->where('status', $request->input('status'));
            })
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        $wallets = UserWallet::query()
            ->with('coin')
            ->where('user_id', $user->id)
            ->oldest();

        $currencies = Coin::query()
            ->where('status', CoinStatus::Active)
            ->whereNotIn('id', $wallets->pluck('coin_id'))
            ->get();

        $userWallets = $wallets->get();

        $statistics = [
            'total_wallets' => $userWallets->count() + 1,
            'total_deposit' => Transaction::where('user_id', $user->id)->whereIn('type', [TxnType::Deposit, TxnType::ManualDeposit])->where('wallet_type', 'default')->where('status', TxnStatus::Success)->sum('amount'),
            'total_withdraw' => Transaction::where('user_id', $user->id)->whereIn('type', [TxnType::Withdraw, TxnType::WithdrawAuto])->where('wallet_type', 'default')->where('status', TxnStatus::Success)->sum('amount'),
            'total_payments' => Transaction::where('user_id', $user->id)->where('type', TxnType::PlanPurchase)->where('wallet_type', 'default')->where('status', TxnStatus::Success)->sum('amount'),
            'referral_bonuses' => Transaction::where('user_id', $user->id)->where('type', TxnType::Referral)->where('wallet_type', 'default')->where('status', TxnStatus::Success)->sum('amount'),
            'total_tickets' => $user->tickets()->count(),
            'total_referral' => $user->referrals()->count(),
            'total_transactions' => $user->transaction()->count(),
            'total_miners' => $user->minings()->count(),
            'total_active_miners' => $user->minings()->where('status', UserMiningStatus::Ongoing->value)->count(),
            'total_completed_miners' => $user->minings()->where('status', UserMiningStatus::Completed->value)->count(),
        ];

        return view('frontend::user.dashboard', ['data' => $data, 'statistics' => $statistics, 'transactions' => $transactions, 'userWallets' => $userWallets, 'currencies' => $currencies, 'user' => $user]);
    }

    public function qrcode()
    {
        $user = Auth::user();

        $myQRCode = QrCode::size(184)->generate($user->account_number);

        return view('frontend::user.qrcode', ['myQRCode' => $myQRCode]);
    }

    public function activityChartInfo(Request $request)
    {
        try {
            $user = Auth::user();
            $currentMonth = $request->get('month', Carbon::now()->month);
            $currentYear = $request->get('year', Carbon::now()->year);
            $wallet_id = $request->get('wallet_id', 'default');

            $daysInMonth = $this->report->getDaysInMonth($currentYear, $currentMonth);
            $transactions = $this->report->getTransactions($user->id, $currentMonth, $currentYear, $wallet_id, [TxnType::Deposit->value, TxnType::ManualDeposit->value, TxnType::Withdraw->value, TxnType::WithdrawAuto->value]);
            $data = $this->report->prepareChartData($transactions, $daysInMonth);
            $maxMinValues = $this->report->calculateMaxMinValues($data);

            return response()->json([
                'deposit' => $data['deposit'],
                'withdraw' => $data['withdraw'],
                'currentMonth' => $daysInMonth,
                'maxValue' => round($maxMinValues['max']) + 50,
                'minValue' => $maxMinValues['min'],
            ]);
        } catch (\Throwable $throwable) {
            throw $throwable;

            return response()->json([
                'status' => 'Error',
                'message' => $throwable->getMessage(),
            ]);
        }
    }
}
