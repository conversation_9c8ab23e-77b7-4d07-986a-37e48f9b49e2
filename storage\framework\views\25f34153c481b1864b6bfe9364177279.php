<div class="modal fade" id="addSubBal" tabindex="-1" aria-labelledby="addSubBalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-centered">
        <div class="modal-content site-table-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="addSubBalLabel">
                    <?php echo e(__('Balance Add or Subtract')); ?>

                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="<?php echo e(route('admin.user.balance-update', $user->id)); ?>" method="post">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="switch-field">
                                <input type="radio" id="addMon" name="type" value="add" checked />
                                <label for="addMon"><?php echo e(__('Add')); ?></label>
                                <input type="radio" id="addMon2" name="type" value="subtract" />
                                <label for="addMon2"><?php echo e(__('Subtract')); ?></label>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="site-input-groups mb-0">
                                <label for="wallet" class="input-label mb-1">
                                    <?php echo e(__('Select Wallet')); ?>

                                    <span class="required">*</span>
                                </label>
                                <select class="form-select" name="wallet_type" id="wallet" required>
                                    <option value="default" data-currency="<?php echo e(setting('site_currency', 'global')); ?>">
                                        <?php echo e(__('Main Wallet')); ?> (<?php echo e(setting('site_currency', 'global')); ?>)
                                    </option>
                                    <?php $__currentLoopData = $user->wallets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wallet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option data-currency="<?php echo e($wallet->coin->code); ?>"
                                            value="<?php echo e($wallet->id); ?>">
                                            <?php echo e($wallet->coin->name); ?> (<?php echo e($wallet->coin->code); ?>)
                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <div class="site-input-groups">
                                <label for="wallet" class="input-label mb-1">
                                    <?php echo e(__('Amount')); ?>

                                    <span class="required">*</span>
                                </label>
                                <div class="input-group joint-input">
                                    <span
                                        class="input-group-text balance-add-sub-currency"><?php echo e(setting('site_currency', 'global')); ?></span>
                                    <input type="text" name="amount"
                                        oninput="this.value = validateDouble(this.value)" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-12">
                            <button type="submit" class="site-btn primary-btn w-100">
                                <?php echo e(__('Apply Now')); ?>

                            </button>
                        </div>
                    </div>
                </form>

            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/user/include/__balance.blade.php ENDPATH**/ ?>