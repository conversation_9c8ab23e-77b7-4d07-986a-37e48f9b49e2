<?php

namespace App\Http\Controllers\Frontend;

use App\Enums\TxnStatus;
use App\Enums\TxnType;
use App\Facades\Txn\Txn;
use App\Http\Controllers\Controller;
use App\Models\Coin;
use App\Models\DepositMethod;
use App\Models\Scheme;
use App\Models\Transaction;
use App\Models\User;
use App\Models\UserWallet;
use App\Services\TransactionServices;
use App\Traits\ImageUpload;
use App\Traits\NotifyTrait;
use App\Traits\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PlanPurchaseController extends Controller
{
    use ImageUpload, NotifyTrait, Payment;

    protected $user;

    public function __construct()
    {
        $this->user = auth('web')->user();
    }

    public function index()
    {
        $user = Auth::user();

        if (! setting('kyc_plan_purchase') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return to_route('user.dashboard');
        } elseif (! $user->plan_purchase_status) {
            notify()->error(__('Plan purchase currently unavailable!'));

            return to_route('user.dashboard');
        }

        return view('frontend::user.plan-purchase.index');
    }

    public function checkout(Request $request, Scheme $scheme)
    {

        $user = Auth::user();

        if (! setting('kyc_plan_purchase') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC.'));

            return to_route('user.dashboard');
        } elseif (! $user->plan_purchase_status) {
            notify()->error(__('Plan purchase currently unavailable!'));

            return to_route('user.dashboard');
        }

        $priceData = $this->planPriceData($scheme->id);
        $gateways = DepositMethod::query()
            ->where('status', 1)
            ->get();

        $wallets = [];

        $wallets = UserWallet::query()
            ->with([
                'coin',
            ])
            ->whereBelongsTo($this->user)
            ->get();

        return view('frontend::user.mining.checkout', ['scheme' => $scheme, 'priceData' => $priceData, 'gateways' => $gateways, 'wallets' => $wallets]);
    }

    /**
     * get plan price data
     *
     * @param  int  $planId
     * @param  int  $userId
     * @return bool|array [planPrice, planFinalPrice, discountAmount, plan]
     */
    public function planPriceData($planId, $userId = null, $gateway_id = null)
    {
        $plan = Scheme::find($planId);

        if ($plan) {
            $user = $userId ? User::find($userId) : $this->user;
            $user->load('portfolio.features');
            $planPrice = $plan->price;
            $discountAmount = 0;
            $portfolioFeatures = $user->portfolio?->features;

            if ($portfolioFeatures) {
                if ($portfolioFeatures->discount_plan_purchase_type == 'percentage') {
                    $discountAmount = $plan->price * $portfolioFeatures->discount_amount_plan_purchase / 100;
                } else {
                    $discountAmount = $portfolioFeatures->discount_amount_plan_purchase;
                }
            }

            $planPrice = $plan->price - $discountAmount;
            if ($gateway_id) {
                $gateway = DepositMethod::find($gateway_id);
                if ($gateway) {
                    $planPrice = $gateway->getChargeAmount($planPrice)[2];
                }
            }

            return [
                'planPrice' => $plan->price,
                'planFinalPrice' => $planPrice,
                'discountAmount' => $discountAmount,
                'plan' => $plan,
            ];
        } else {
            return false;
        }
    }

    public function planPurchaseNow(Request $request)
    {
        $user = $this->user;

        $request->validate([
            'scheme_id' => 'required|exists:schemes,id',
            'gateway_code' => 'required_if:payment_method,gateway',
            'payment_method' => 'required',
        ]);

        if (! setting('kyc_plan_purchase') && ! $user->kyc) {
            notify()->error(__('Please verify your KYC before purchasing a plan.'));

            return back();
        }

        if ($request->payment_method != 'gateway') {
            return $this->planPurchaseWithBalance($request);
        } else {
            return $this->planPurchaseWithGateway($request);
        }

    }

    public function planPurchaseWithGateway(Request $request)
    {

        $user = $this->user;
        $wallet = 'default';

        $gatewayInfo = DepositMethod::find($request->gateway_code);

        if (! $gatewayInfo) {
            notify()->error(__('Gateway does not exist!'));

            return redirect()->back();
        }
        $planPriceData = self::planPriceData($request->scheme_id);
        if (! $planPriceData) {
            notify()->error(__('Plan not found'));

            return back();
        }

        $amount = $planPriceData['planFinalPrice'];

        if ($amount < $gatewayInfo->minimum_deposit || $amount > $gatewayInfo->maximum_deposit) {
            $currencySymbol = setting('currency_symbol', 'global');
            $message = __('Please Deposit the Amount within the range :symbol:min to :symbol:max', [
                'symbol' => $currencySymbol,
                'min' => $gatewayInfo->minimum_deposit,
                'max' => $gatewayInfo->maximum_deposit,
            ]);

            notify()->error($message);

            return redirect()->back();
        }

        [$payAmount, $charge, $finalAmount] = $gatewayInfo->getChargeAmount($amount);
        $depositType = TxnType::PlanPurchase;

        if ($request->manual_data !== null) {
            $depositType = TxnType::PlanPurchaseManual;
            $manualData = $request->manual_data;

            foreach ($manualData as $key => $value) {
                if (is_file($value)) {
                    $manualData[$key] = self::imageUploadTrait($value);
                }
            }

            $shortcodes = [
                '[[amount]]' => $planPriceData['planFinalPrice'],
                '[[charge]]' => $charge,
                '[[wallet]]' => $wallet,
                '[[currency]]' => setting('site_currency', 'global'),
                '[[gateway]]' => $gatewayInfo->name,
                '[[request_at]]' => date('d M, Y h:i A'),
                '[[total_amount]]' => $finalAmount,
                '[[request_link]]' => route('admin.deposit.manual.pending'),
                '[[site_title]]' => setting('site_title', 'global'),
            ];

            $this->sendNotify($user->email, 'admin_manual_deposit', 'Admin', $shortcodes, $user->phone, $user->id, route('admin.deposit.manual.pending'));
        }

        DB::beginTransaction();

        $txnInfo = (new Txn)->new($planPriceData['planFinalPrice'], charge: $charge, final_amount: $finalAmount, userWallet: $wallet, method: $gatewayInfo->gateway_code, description: 'Payment for Plan Purchase: '.$planPriceData['plan']->name, type: $depositType, status: TxnStatus::Pending, payCurrency: $gatewayInfo->currency, payAmount: $payAmount, userID: $user->id, relatedUserID: null, relatedModel: 'User', manualFieldData: $manualData ?? [], schemeId: $planPriceData['plan']->id);

        DB::commit();

        return self::depositAutoGateway($gatewayInfo->gateway_code, $txnInfo);

    }

    public function planPurchaseWithBalance(Request $request)
    {
        $user = $this->user;
        $planPriceData = self::planPriceData(planId: $request->scheme_id, userId: $user->id, gateway_id: null);
        if (! $planPriceData) {
            notify()->error(__('Plan not found'));

            return back();
        }

        $walletBalance = 0;

        if ($request->payment_method == 'default') {
            $walletBalance = $user->balance;
            $payCurrency = setting('site_currency', 'global');
            $walletName = 'Main Wallet';
        } else {
            $wallet = UserWallet::findOrFail($request->payment_method);
            $walletBalance = $wallet->balance;
            $payCurrency = $wallet->coin->code;
            $walletName = $wallet->coin->name;

            $planPriceData['planFinalPrice'] = $planPriceData['planFinalPrice'] * $wallet->coin->conversion_rate;
        }

        if ($walletBalance < $planPriceData['planFinalPrice']) {
            notify()->error(__('Insufficient balance'));

            return back();
        }

        try {
            DB::beginTransaction();

            $transaction = Transaction::create([
                'user_id' => $user->id,
                'description' => 'Payment for Plan Purchase: '.$planPriceData['plan']->name,
                'type' => TxnType::PlanPurchase,
                'amount' => $planPriceData['planFinalPrice'],
                'wallet_type' => $request->payment_method,
                'charge' => 0,
                'final_amount' => $planPriceData['planFinalPrice'],
                'method' => $walletName.' Wallet Balance',
                'status' => TxnStatus::Success,
                'scheme_id' => $planPriceData['plan']->id,
                'pay_currency' => $payCurrency,
                'pay_amount' => $planPriceData['planFinalPrice'],
            ]);

            (new TransactionServices)->paid($transaction);

            DB::commit();

            notify()->success(__('Plan purchased successfully'));

            return redirect()->route('user.mining.');
        } catch (\Throwable $throwable) {
            throw $throwable;
            DB::rollBack();

            notify()->error(__('Sorry! Something went wrong.'));

            return redirect()->back();
        }
    }

    public function priceConvert(Request $request, Coin $coin)
    {

        $amount = $request->float('amount', 0) ?? 0;
        $rate = $coin->conversion_rate;
        $finalAmount = $amount * $rate;

        return response()->json([
            'finalAmount' => $finalAmount,
            'currency' => $coin->code,
        ]);
    }
}
