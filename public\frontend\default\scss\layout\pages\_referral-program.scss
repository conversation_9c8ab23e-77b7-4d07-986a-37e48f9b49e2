@use '../../utils' as *;

/*----------------------------------------*/
/* Referral program styles
/*----------------------------------------*/
.referral-program-section {
    padding: 50px 50px;

    @media #{$xs} {
        padding: 30px 30px;
    }
}

.referral-program-wrapper {
    position: relative;
    margin-bottom: 30px;

    .referral-contents {
        max-width: 496px;
        margin: 0 auto;

        .title {
            margin-bottom: 34px;
        }

        .bonus-thumb {
            img {
                width: rem(260);
                height: rem(260);

                @media #{$xxs} {
                    width: rem(200);
                    height: rem(200);
                }
            }
        }
    }

}

.referral-form-input {
    form {
        position: relative;

        .form-input {

            height: 60px;
            border-radius: 2px;
            background: #F6F6F6;
            border: 1px solid rgba(34, 34, 35, 0.16);

            @include dark-theme {
                background: rgba(255, 255, 255, 0.04);
                border-color: rgba(255, 255, 255, 0.1);
                color: var(--td-white);
            }
        }
    }

    .referral-btn {
        position: absolute;
        inset-inline-end: 10px;
        top: 50%;
        transform: translateY(-50%);

        @media #{$xs} {
            width: 100%;
            position: inherit;
            transform: inherit;
            margin-top: 20px;
            top: inherit;
            left: inherit;
            inset-inline-end: 0;
        }

        .td-btn {
            height: 40px;

            @media #{$xs} {
                width: 100%;
            }
        }
    }

    .description {
        margin-top: 8px;
        color: var(--td-heading);
        font-size: 14px;
    }
}

.referral-bottom {
    margin-top: 50px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 30px 30px;

    .referral-status {
        .status-info {
            ul {
                li {
                    list-style: none;
                    display: flex;
                    gap: 12px;

                    &:not(:last-child) {
                        margin-bottom: 14px;
                    }
                }
            }
        }
    }

    .social-icons {
        display: flex;
        align-items: center;
        gap: 12px 12px;
        flex-wrap: wrap;
        margin-top: 16px;

        a {
            display: flex;
            flex-direction: row;
            justify-content: center;
            align-items: center;
            padding: 0px;
            gap: 10px;
            width: 30px;
            height: 30px;
            background: linear-gradient(90deg, rgba(195, 64, 192, 0.16) 0%, rgba(251, 64, 90, 0.16) 50%, rgba(247, 163, 74, 0.16) 100%);
            border-radius: 43px;

            &:hover {
                transform: translateY(-3px);
            }
        }
    }
}

.referral-bonus-shape {
    position: absolute;
    inset-inline-start: 50%;
    transform: translateX(-50%);
    top: 30%;
    z-index: -1;
    width: 486px;

    @include rtl {
        inset-inline-start: auto;
        inset-inline-end: 50%;
    }

    @media #{$xs} {
        width: 100%;
    }
}