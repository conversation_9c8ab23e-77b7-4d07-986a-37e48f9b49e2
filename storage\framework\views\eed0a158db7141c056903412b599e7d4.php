<?php $__env->startSection('title'); ?>
    <?php echo e(__('Deposit History')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('deposit_content'); ?>
    <div class="col-xl-12 col-md-12">
        <div class="site-card-body table-responsive">
            <div class="site-table table-responsive">
                <?php echo $__env->make('backend.deposit.include.__filter', ['status' => true], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                <table class="table">
                    <thead>
                        <tr>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'Date', 'field' => 'created_at'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'User', 'field' => 'user'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <th><?php echo e(__('Type')); ?></th>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'Transaction ID', 'field' => 'tnx'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'Amount', 'field' => 'amount'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'Charge', 'field' => 'charge'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'Gateway', 'field' => 'method'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'Wallet', 'field' => 'wallet'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                            <?php echo $__env->make('backend.filter.th', ['label' => 'Status', 'field' => 'status'], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $deposits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $deposit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <?php echo e($deposit->created_at); ?>

                                </td>
                                <td>
                                    <?php echo $__env->make('backend.transaction.include.__user', [
                                        'id' => $deposit->user_id,
                                        'name' => $deposit->user->username,
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </td>
                                <td>
                                    <?php echo e($deposit->type_text); ?>

                                </td>
                                <td><?php echo e(safe($deposit->tnx)); ?></td>
                                <td>
                                    <?php echo e(trxAmountFormat($deposit, 'final_amount')); ?>

                                </td>
                                <td>
                                    <?php echo e(trxAmountFormat($deposit, 'charge')); ?>

                                </td>
                                <td>
                                    <?php echo e(safe($deposit->method)); ?>

                                </td>
                                <td>
                                    <?php if($deposit->wallet_type == null || $deposit->wallet_type == 'default'): ?>
                                        <?php echo e(__('Main Wallet')); ?>

                                    <?php else: ?>
                                        <?php echo e($deposit?->wallet?->coin?->name); ?>

                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo $__env->make('backend.transaction.include.__txn_status', [
                                        'status' => $deposit->status->value,
                                    ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <td colspan="7" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                        <?php endif; ?>
                    </tbody>
                </table>

                <?php echo e($deposits->links('backend.include.__pagination')); ?>

            </div>
        </div>

    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.deposit.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/deposit/history.blade.php ENDPATH**/ ?>