<div class="site-input-groups">
    <label class="box-input-label">
        <?php echo e($attributes->get('label','Status')); ?>:
        <span class="text-danger"><?php echo e($attributes->has('required') ? '*' : ''); ?></span>
    </label>
    <?php
    $name = $attributes->get('name','status');
    $value = $attributes->get('value','active');
    ?>
    <div class="switch-field same-type">
        <input type="radio" id="radio-active-<?php echo e($name); ?>" name="<?php echo e($name); ?>" value="active" <?php echo e($value == 'active' ? 'checked' : ''); ?>>
        <label for="radio-active-<?php echo e($name); ?>"><?php echo e(__('Active')); ?></label>
        <input type="radio" id="radio-inactive-<?php echo e($name); ?>" name="<?php echo e($name); ?>" value="inactive" <?php echo e($value == 'inactive' ? 'checked' : ''); ?>>
        <label for="radio-inactive-<?php echo e($name); ?>"><?php echo e(__('Inactive')); ?></label>
    </div>
</div>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/components/admin/status-radio.blade.php ENDPATH**/ ?>