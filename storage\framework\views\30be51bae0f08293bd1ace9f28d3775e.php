<?php $__env->startSection('setting-title'); ?>
    <?php echo e(__('Page Settings')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('setting-content'); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12">
                <div class="site-card">
                    <div class="site-card-header">
                        <h3 class="title"><?php echo e(__('User Register Field Settings')); ?></h3>
                    </div>
                    <div class="site-card-body">
                        <form action="<?php echo e(route('admin.page.setting.update')); ?>" method="post" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>

                            <div class="site-input-groups">
                                <div class="row justify-content-center">
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Username:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="username-show" name="username_show"
                                                    <?php if(getPageSetting('username_show')): echo 'checked'; endif; ?> value="1" />
                                                <label for="username-show"><?php echo e(__('Show')); ?></label>
                                                <input type="radio" id="username-hide" name="username_show"
                                                    <?php if(!getPageSetting('username_show')): echo 'checked'; endif; ?> value="0" />
                                                <label for="username-hide"><?php echo e(__('Hide')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Username is:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="username-required" name="username_validation"
                                                    <?php if(getPageSetting('username_validation')): echo 'checked'; endif; ?> value="1" />
                                                <label for="username-required"><?php echo e(__('Required')); ?></label>
                                                <input type="radio" id="username-optional" name="username_validation"
                                                    <?php if(!getPageSetting('username_validation')): echo 'checked'; endif; ?> value="0" />
                                                <label for="username-optional"><?php echo e(__('Optional')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Phone Number:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="phone-show" name="phone_show"
                                                    <?php if(getPageSetting('phone_show')): echo 'checked'; endif; ?> value="1" />
                                                <label for="phone-show"><?php echo e(__('Show')); ?></label>
                                                <input type="radio" id="phone-hide" name="phone_show"
                                                    <?php if(!getPageSetting('phone_show')): echo 'checked'; endif; ?> value="0" />
                                                <label for="phone-hide"><?php echo e(__('Hide')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label"
                                                for=""><?php echo e(__('Phone Number is:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="phone-required" name="phone_validation"
                                                    <?php if(getPageSetting('phone_validation')): echo 'checked'; endif; ?> value="1" />
                                                <label for="phone-required"><?php echo e(__('Required')); ?></label>
                                                <input type="radio" id="phone-optional" name="phone_validation"
                                                    <?php if(!getPageSetting('phone_validation')): echo 'checked'; endif; ?> value="0" />
                                                <label for="phone-optional"><?php echo e(__('Optional')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Country:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="country-show" name="country_show"
                                                    <?php if(getPageSetting('country_show')): echo 'checked'; endif; ?> value="1" />
                                                <label for="country-show"><?php echo e(__('Show')); ?></label>
                                                <input type="radio" id="country-hide" name="country_show"
                                                    <?php if(!getPageSetting('country_show')): echo 'checked'; endif; ?> value="0" />
                                                <label for="country-hide"><?php echo e(__('Hide')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Country is:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="country-required" name="country_validation"
                                                    <?php if(getPageSetting('country_validation')): echo 'checked'; endif; ?> value="1" />
                                                <label for="country-required"><?php echo e(__('Required')); ?></label>
                                                <input type="radio" id="country-optional" name="country_validation"
                                                    <?php if(!getPageSetting('country_validation')): echo 'checked'; endif; ?> value="0" />
                                                <label for="country-optional"><?php echo e(__('Optional')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label"
                                                for=""><?php echo e(__('Referral Code:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="referral-code-show" name="referral_code_show"
                                                    <?php if(getPageSetting('referral_code_show')): echo 'checked'; endif; ?> value="1" />
                                                <label for="referral-code-show"><?php echo e(__('Show')); ?></label>
                                                <input type="radio" id="referral-code-hide" name="referral_code_show"
                                                    <?php if(!getPageSetting('referral_code_show')): echo 'checked'; endif; ?> value="0" />
                                                <label for="referral-code-hide"><?php echo e(__('Hide')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label"
                                                for=""><?php echo e(__('Referral code is:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="referral-code-required"
                                                    name="referral_code_validation" <?php if(getPageSetting('referral_code_validation')): echo 'checked'; endif; ?>
                                                    value="1" />
                                                <label for="referral-code-required"><?php echo e(__('Required')); ?></label>
                                                <input type="radio" id="referral-code-optional"
                                                    name="referral_code_validation" <?php if(!getPageSetting('referral_code_validation')): echo 'checked'; endif; ?>
                                                    value="0" />
                                                <label for="referral-code-optional"><?php echo e(__('Optional')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Gender:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="gender-show" name="gender_show"
                                                    <?php if(getPageSetting('gender_show')): echo 'checked'; endif; ?> value="1" />
                                                <label for="gender-show"><?php echo e(__('Show')); ?></label>
                                                <input type="radio" id="gender-hide" name="gender_show"
                                                    <?php if(!getPageSetting('gender_show')): echo 'checked'; endif; ?> value="0" />
                                                <label for="gender-hide"><?php echo e(__('Hide')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 col-sm-12 col-12">
                                        <div class="site-input-groups">
                                            <label class="box-input-label" for=""><?php echo e(__('Gender is:')); ?></label>
                                            <div class="switch-field">
                                                <input type="radio" id="gender-required" name="gender_validation"
                                                    <?php if(getPageSetting('gender_validation')): echo 'checked'; endif; ?> value="1" />
                                                <label for="gender-required"><?php echo e(__('Required')); ?></label>
                                                <input type="radio" id="gender-optional" name="gender_validation"
                                                    <?php if(!getPageSetting('gender_validation')): echo 'checked'; endif; ?> value="0" />
                                                <label for="gender-optional"><?php echo e(__('Optional')); ?></label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="site-btn-sm primary-btn"><?php echo e(__('Save Changes')); ?></button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.setting.index', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/page/default/setting.blade.php ENDPATH**/ ?>