<?php

use App\Http\Controllers\AppController;
use App\Http\Controllers\CronJobController;
use App\Http\Controllers\Frontend\AddMoneyController;
use App\Http\Controllers\Frontend\DashboardController;
use App\Http\Controllers\Frontend\HomeController;
use App\Http\Controllers\Frontend\KycController;
use App\Http\Controllers\Frontend\MiningController;
use App\Http\Controllers\Frontend\PageController;
use App\Http\Controllers\Frontend\PlanPurchaseController;
use App\Http\Controllers\Frontend\PortfolioController;
use App\Http\Controllers\Frontend\SettingController;
use App\Http\Controllers\Frontend\TicketController;
use App\Http\Controllers\Frontend\TransactionController;
use App\Http\Controllers\Frontend\UserController;
use App\Http\Controllers\Frontend\UserReferralController;
use App\Http\Controllers\Frontend\UserWalletController;
use App\Http\Controllers\Frontend\WithdrawAccountController;
use App\Http\Controllers\Frontend\WithdrawMoneyController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\StatusController;
use App\Http\Controllers\Test\TestContrller;
use Illuminate\Support\Facades\Route;

// Landing Pages
Route::controller(HomeController::class)->group(function () {
    Route::get('/', 'home')->name('home');
    Route::get('blog/{id}', 'blogDetails')->name('blog.details');
});

// Contact Form
Route::post('mail-send', [PageController::class, 'mailSend'])->name('mail-send');
Route::post('subscriber', [HomeController::class, 'subscribeNow'])->name('subscriber');

// User
Route::middleware('auth:web')->group(function () {
    Route::get('profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// User Dashboard
Route::group(['middleware' => array_filter(['auth:web', '2fa', 'check_deactivation', setting('email_verification', 'permission') ? 'verified' : null]), 'prefix' => 'user', 'as' => 'user.'], function () {
    Route::get('dashboard', [DashboardController::class, 'dashboard'])->name('dashboard');
    Route::get('qr-code', [DashboardController::class, 'qrcode'])->name('myQrcode');
    Route::get('activity-chart', [DashboardController::class, 'activityChartInfo'])->name('activity.chart');

    // user wallet
    Route::controller(UserWalletController::class)->middleware('check_feature:multiple_currency,kyc_wallet')->group(function () {
        Route::get('user-wallet', 'index')->name('userWallet.index');
        Route::get('user-wallet/create', 'create')->name('userWallet.create');
        Route::post('user-wallet/store', 'store')->name('userWallet.store');
        Route::post('user-wallet/{id}/delete', 'delete')->name('userWallet.delete');
        Route::post('user-wallet-wise-gateway-method', 'gatewayMethod')->name('userWallet.gatewayMethod')->withoutMiddleware('check_feature:multiple_currency,kyc_wallet');

    });

    // Setting
    Route::controller(SettingController::class)->group(function () {
        Route::put('/setting/update', 'update')->name('setting.update');
        Route::get('generate-qrcode', 'generateQrcode')->name('generate.qrcode');
        Route::post('2fa-action', 'twofaAction')->name('2fa.action');
        Route::get('setting/{type?}', 'index')->name('setting.index');
    });

    // KYC
    Route::controller(KycController::class)->middleware('check_feature:kyc_verification')->group(function () {
        Route::get('kyc-details', 'kycDetails')->name('kyc.details')->withoutMiddleware('check_feature:kyc_verification');
        Route::get('kyc/submission/{id}', 'kycSubmission')->name('kyc.submission');
        Route::post('kyc-submit', 'submit')->name('kyc.submit');
    });

    // Add Money
    Route::controller(AddMoneyController::class)->middleware('check_feature:user_deposit,kyc_deposit')->group(function () {
        Route::get('payment-gateway-info/{id}', 'gatewayInfo')->name('paymentGateway.info');
        Route::get('add-money-history', 'addMoneyHistory')->name('addMoney.history');
        Route::get('add-money', 'addMoney')->name('addMoney');
        Route::post('add-money', 'addMoneyNow')->name('addMoney.now');
    });

    // Withdraw Account
    Route::controller(WithdrawAccountController::class)->middleware('check_feature:user_withdraw,kyc_withdraw')->prefix('withdraw/account')->name('withdraw.account.')->group(function () {
        Route::get('', 'index')->name('index');
        Route::get('/create', 'create')->name('create');
        Route::post('/store', 'store')->name('store');
        Route::get('/{id}/edit', 'edit')->name('edit');
        Route::post('/{id}/update', 'update')->name('update');
        Route::post('/{id}/delete', 'delete')->name('delete');
        Route::post('methods', 'getMethods')->name('get.methods');
        Route::get('{id}/details', 'withdrawMethod')->name('details');
    });

    // Withdraw
    Route::controller(WithdrawMoneyController::class)->middleware('check_feature:user_withdraw,kyc_withdraw')->prefix('withdraw')->name('withdrawMoney.')->group(function () {
        Route::get('/history', 'withdrawMoneyHistory')->name('history');
        Route::get('/', 'withdrawMoney')->name('index');
        Route::post('/now', 'withdrawMoneyNow')->name('now');
    });

    // Referral
    Route::controller(UserReferralController::class)->middleware('check_feature:sign_up_referral')->group(function () {
        Route::get('referral', 'userReferral')->name('referral');
        Route::get('referral/list', 'userReferralList')->name('referral.list');
        Route::get('referral/tree', 'referralTree')->name('referral.tree');
    });

    // Portfolio
    Route::get('portfolio', [PortfolioController::class, 'index'])->name('portfolio');

    // Notifications
    Route::get('notification/all', [UserController::class, 'allNotification'])->name('notification.all');
    Route::get('notification-read/{id}', [UserController::class, 'readNotification'])->name('read-notification');

    Route::get('transactions', [TransactionController::class, 'transactions'])->name('transactions');

    // Mining
    Route::controller(MiningController::class)->prefix('mining')->name('mining.')->group(function () {
        Route::get('', 'index')->name('');
        Route::get('/history', 'miningHistory')->name('history');
        Route::get('/details/{transaction}', 'details')->name('details');
    });
    Route::controller(PlanPurchaseController::class)->middleware('check_feature:plan_purchase,kyc_plan_purchase')->prefix('plan-purchase')->name('plan-purchase.')->group(function () {
        Route::get('/checkout/{scheme}', 'checkout')->name('checkout');
        Route::post('/pay', 'planPurchaseNow')->name('pay');
        Route::get('/price-convert/{coin}', 'priceConvert')->name('price.convert');
    });

    Route::controller(TicketController::class)->middleware('check_feature:support_ticket')->group(function () {
        Route::get('tickets', 'tickets')->name('tickets');
        Route::get('tickets-create', 'create')->name('ticket.create');
        Route::post('tickets-store', 'store')->name('ticket.store');
        Route::get('ticket-show/{id}', 'show')->name('ticket.show');
        Route::post('reply', 'reply')->name('ticket.reply');
        Route::get('close-now/{uuid}', 'closeNow')->name('ticket.close.now');
        Route::get('reopen-now/{uuid}', 'reopenNow')->name('ticket.reopen');
    });

    Route::post('/2fa/verify', function () {
        return redirect(route('user.dashboard'));
    })->name('2fa.verify');
});

// Gateway status
Route::group(['controller' => StatusController::class, 'prefix' => 'status', 'as' => 'status.'], function () {
    Route::match(['get', 'post'], '/success', 'success')->name('success');
    Route::match(['get', 'post'], '/cancel', 'cancel')->name('cancel');
    Route::match(['get', 'post'], '/pending', 'pending')->name('pending');
});

// Translate
Route::get('language-update/{locale}', [HomeController::class, 'languageUpdate'])->name('language-update');

// Dynamic Page
Route::get('page/{section}', [PageController::class, 'getPage'])->name('dynamic.page');

// Without auth
Route::get('notification-tune', [AppController::class, 'notificationTune'])->name('notification-tune');

// Site cron job
Route::get('site-cron', [CronJobController::class, 'runCronJobs'])->name('cron.job');

// Theme Mode
Route::get('theme-mode', [HomeController::class, 'themeMode'])->name('theme-mode');

// Auth
require __DIR__.'/auth.php';

// Test Controller
Route::get('test', [TestContrller::class, 'index'])->name('test');
