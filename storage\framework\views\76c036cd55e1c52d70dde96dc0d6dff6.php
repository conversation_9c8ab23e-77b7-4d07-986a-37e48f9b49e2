<?php if(request('tab') == 'referral'): ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        'tab-pane fade',
        'show active' => request('tab') == 'referral',
    ]); ?>" id="pills-tree" role="tabpanel" aria-labelledby="pills-transactions-tab">
        <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="site-card">
                    <div class="site-card-header">
                        <h4 class="title"><?php echo e(__('Referral')); ?></h4>
                    </div>
                    <div class="site-card-body table-responsive">

                        
                        <?php if($user->referrals->count() > 0): ?>
                            <section class="management-hierarchy">
                                <div class="hv-container">
                                    <div class="hv-wrapper">
                                        <!-- tree component -->
                                        <?php echo $__env->make('backend.user.include.__tree', [
                                            'levelUser' => $user,
                                            'depth' => 1,
                                            'me' => true,
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    </div>
                                </div>
                            </section>
                        <?php else: ?>
                            <p class="text-center"><?php echo e(__('No Referral user found')); ?></p>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/user/include/__referral_tree.blade.php ENDPATH**/ ?>