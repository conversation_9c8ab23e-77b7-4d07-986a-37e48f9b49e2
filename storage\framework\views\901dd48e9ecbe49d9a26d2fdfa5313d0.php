<?php $__env->startSection('title'); ?>
    <?php echo e(__('Customer Details')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="container-fluid">
                <div class="row">
                    <div class="col">
                        <div class="title-content">
                            <h2 class="title"><?php echo e(__('Details of') . ' ' . $user->first_name . ' ' . $user->last_name); ?>

                            </h2>
                            <a href="<?php echo e(url()->previous()); ?>" class="title-btn"><i
                                    data-lucide="corner-down-left"></i><?php echo e(__('Back')); ?></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="container-fluid">
            <div class="row justify-content-center">
                <div class="col-xxl-3 col-xl-6 col-lg-8 col-md-6 col-sm-12">
                    <div class="profile-card">
                        <div class="top">
                            <div class="avatar">
                                <div class="avatar-face">
                                    <?php if(null != $user->avatar): ?>
                                        <div class="avatar-face">
                                            <img class="avatar-img" src="<?php echo e(asset($user->avatar)); ?>"
                                                alt="<?php echo e($user->full_name); ?>" />
                                        </div>
                                    <?php else: ?>
                                        <div class="avatar-text"><?php echo e($user->first_name[0] . $user->last_name[0]); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="title-des">
                                <h4><?php echo e($user->first_name . ' ' . $user->last_name); ?></h4>
                                <p><?php echo e(ucwords($user->city)); ?><?php if($user->city != ''): ?>
                                        ,
                                    <?php endif; ?><?php echo e($user->country); ?></p>
                                <?php if($user->activities->count() > 0): ?>
                                    <?php
                                        $lastLogin = $user->activities->sortByDesc('created_at')->first();
                                        $lastLoginDateTime = optional($lastLogin)->created_at->format('d-m-Y H:i:s');
                                    ?>
                                    <p><?php echo e(__('Last Login:')); ?> <?php echo e($lastLoginDateTime); ?></p>
                                <?php else: ?>
                                    <p><?php echo e(__('This user has not logged in yet.')); ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="btns">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer-mail-send')): ?>
                                    <span type="button" data-bs-toggle="modal" data-bs-target="#sendEmail"><a
                                            href="javascript:void(0);" class="site-btn-round blue-btn" data-bs-toggle="tooltip"
                                            title="" data-bs-original-title="Send Email"><i
                                                data-lucide="mail"></i></a></span>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer-login')): ?>
                                    <a href="<?php echo e(route('admin.user.login', $user->id)); ?>" target="_blank"
                                        class="site-btn-round green-btn" data-bs-toggle="tooltip" title=""
                                        data-bs-placement="top" data-bs-original-title="Login As User">
                                        <i data-lucide="user-plus"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer-balance-add-or-subtract')): ?>
                                    <span data-bs-toggle="modal" data-bs-target="#addSubBal">
                                        <a href="javascript:void(0);" type="button" class="site-btn-round primary-btn"
                                            data-bs-toggle="tooltip" title="" data-bs-placement="top"
                                            data-bs-original-title="Fund Add or Subtract">
                                            <i data-lucide="wallet"></i></a></span>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer-basic-manage')): ?>
                                    <a href="#" class="site-btn-round red-btn" id="deleteModal" data-bs-toggle="modal"
                                        data-bs-target="#delete" title="Delete User"><i data-lucide="trash-2"></i></a>

                                    <!-- Modal for Popup Box -->
                                    <?php echo $__env->make('backend.user.include.__delete_popup', ['id' => $user->id], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    <!-- Modal for Popup Box End-->
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="site-card">
                            <div class="site-card-body">
                                <div class="row">
                                    <div class="col-xl-12">
                                        <div class="admin-user-balance-card">
                                            <div class="wallet-name">
                                                <div class="name"><?php echo e(__('Main Wallet')); ?></div>
                                                <div class="chip-icon">
                                                    <img class="chip" src="<?php echo e(asset('backend/materials/chip.png')); ?>"
                                                        alt="" />
                                                </div>
                                            </div>
                                            <div class="wallet-info">
                                                <div class="wallet-id"><?php echo e($currency); ?></div>
                                                <div class="balance">
                                                    <?php echo e($currencySymbol . formatAmount($user->balance, $currency)); ?>

                                                </div>
                                            </div>
                                        </div>
                                        <?php $__currentLoopData = $user->wallets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wallet): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="admin-user-balance-card">
                                                <div class="wallet-name">
                                                    <div class="name"><?php echo e($wallet->coin?->name); ?></div>
                                                    <div class="chip-icon">
                                                        <img class="chip" src="<?php echo e(asset('backend/materials/chip.png')); ?>"
                                                            alt="" />
                                                    </div>
                                                </div>
                                                <div class="wallet-info">
                                                    <div class="wallet-id"><?php echo e($wallet->coin?->code); ?></div>
                                                    <div class="balance">
                                                        <?php echo e($wallet->coin?->symbol . formatAmount($wallet->balance, $wallet->coin)); ?>

                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User Status Update -->
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('all-type-status')): ?>
                            <?php echo $__env->make('backend.user.include.__status_update', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endif; ?>
                        <!-- User Status Update End-->
                    </div>
                </div>

                <div class="col-xxl-9 col-xl-12 col-lg-12 col-md-12 col-sm-12">
                    <div class="row">
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="data-card">
                                <div class="icon">
                                    <i data-lucide="credit-card"></i>
                                </div>
                                <div class="content">
                                    <h4 class="count"><?php echo e($statistics['total_deposit']); ?></h4>
                                    <p><?php echo e(__('Total Deposit')); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="data-card">
                                <div class="icon">
                                    <i data-lucide="message-circle"></i>
                                </div>
                                <div class="content">
                                    <h4 class="count"><?php echo e($statistics['total_tickets']); ?></h4>
                                    <p><?php echo e(__('Total Tickets')); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="data-card">
                                <div class="icon">
                                    <i data-lucide="box"></i>
                                </div>
                                <div class="content">
                                    <h4><span class="count"><?php echo e($statistics['total_withdraw']); ?></span> </h4>
                                    <p><?php echo e(__('Total Withdraw')); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="data-card">
                                <div class="icon">
                                    <i data-lucide="russian-ruble"></i>
                                </div>
                                <div class="content">
                                    <h4 class="count"><?php echo e($statistics['total_payments']); ?></h4>
                                    <p><?php echo e(__('Total Payments')); ?></p>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-4 col-lg-4 col-md-6 col-sm-6">
                            <div class="data-card">
                                <div class="icon">
                                    <i data-lucide="users"></i>
                                </div>
                                <div class="content">
                                    <h4 class="count"><?php echo e($statistics['total_referral']); ?></h4>
                                    <p><?php echo e(__('Total Referral')); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="site-tab-bars">
                        <ul class="nav nav-pills" id="pills-tab" role="tablist">
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['customer-basic-manage', 'customer-change-password'])): ?>
                                <li class="nav-item" role="presentation">
                                    <a href="<?php echo e(route('admin.user.edit', $user->id)); ?>"
                                        class="nav-link <?php echo e(!request()->has('tab') ? 'active' : ''); ?>"><i
                                            data-lucide="user"></i><?php echo e(__('Information')); ?></a>
                                </li>
                            <?php endif; ?>
                            <li class="nav-item" role="presentation">
                                <a href="<?php echo e(route('admin.user.edit', ['user' => $user->id, 'tab' => 'transactions'])); ?>"
                                    class="nav-link <?php echo e(request('tab') == 'transactions' ? 'active' : ''); ?>"><i
                                        data-lucide="cast"></i><?php echo e(__('Transactions')); ?></a>
                            </li>

                            <li class="nav-item" role="presentation">
                                <a href="<?php echo e(route('admin.user.edit', ['user' => $user->id, 'tab' => 'referral'])); ?>"
                                    class="nav-link <?php echo e(request('tab') == 'referral' ? 'active' : ''); ?>"><i
                                        data-lucide="network"></i><?php echo e(__('Referral')); ?></a>
                            </li>

                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['support-ticket-list', 'support-ticket-action'])): ?>
                                <li class="nav-item" role="presentation">
                                    <a href="<?php echo e(route('admin.user.edit', ['user' => $user->id, 'tab' => 'ticket'])); ?>"
                                        class="nav-link <?php echo e(request('tab') == 'ticket' ? 'active' : ''); ?>"><i
                                            data-lucide="wrench"></i><?php echo e(__('Ticket')); ?></a>
                                </li>
                            <?php endif; ?>

                        </ul>
                    </div>

                    <div class="tab-content" id="pills-tabContent">
                        <!-- basic Info -->
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['customer-basic-manage', 'customer-change-password'])): ?>
                            <?php echo $__env->make('backend.user.include.__basic_info', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endif; ?>

                        <?php echo $__env->make('backend.user.include.__transactions', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        <!-- Referral Tree -->
                        <?php echo $__env->make('backend.user.include.__referral_tree', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                        <!-- ticket -->
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['support-ticket-list', 'support-ticket-action'])): ?>
                            <?php echo $__env->make('backend.user.include.__ticket', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal for Send Email -->
    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer-mail-send')): ?>
        <?php echo $__env->make('backend.user.include.__mail_send', [
            'name' => $user->first_name . ' ' . $user->last_name,
            'id' => $user->id,
        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php endif; ?>
    <!-- Modal for Send Email-->

    <!-- Modal for balance -->
    <?php echo $__env->make('backend.user.include.__balance', ['id' => $user->id], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <!-- Modal for balance End-->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('script'); ?>
    <script>
        "use strict"

        $('#country').select2();

        // Delete
        $('body').on('click', '#deleteModal', function() {
            var id = "<?php echo e($user->id); ?>";

            var url = '<?php echo e(route('admin.user.destroy', ':id')); ?>';
            url = url.replace(':id', id);
            $('#deleteForm').attr('action', url);
            $('#delete').modal('toggle')
        });

        // Wallet wise currency change
        $('select[name=wallet_type]').on('change', function() {
            var currency = $(this).find(':selected').data('currency');
            $('.balance-add-sub-currency').text(currency);
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/user/edit.blade.php ENDPATH**/ ?>