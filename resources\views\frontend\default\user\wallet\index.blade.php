@extends('frontend::layouts.user')

@section('title')
    {{ __('My Wallets') }}
@endsection
@use('App\Enums\TxnType')
@use('App\Models\Coin')
@use('App\Enums\TxnStatus')

@section('content')
<div class="col-xxl-12">

    <!-- Page title wrapper -->
    <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
            <h3 class="page-title">{{ __('My Wallets') }}</h3>
        </div>
    </div>
    <!-- Page title wrapper -->
</div>
<div class="col-xxl-12">
    @include('frontend::user.wallet.__navbar')
</div>
<div class="col-xxl-12">
    <!-- All Wallets section start -->
    <div class="all-wallets-area">
       <div class="all-wallets-grid">
           @include('frontend.default.user._include.__main_wallet_card', ['showButtons' => true])
        @foreach ($userWallets as $userWallet)
                @include('frontend.default.user._include.__wallet_card', [
                    'userWallet' => $userWallet,
                    'walletRemovable' => true,
                    'showButtons' => true,
                ])
        @endforeach
       </div>
    </div>
    <!-- All Wallets section end -->
 </div>
 @include('frontend::user._include._confirm_delete')

@endsection
@push('js')
    <script>
        $(document).on('click', '.deleteData', function(e) {
            e.preventDefault();
            var routeurl = $(this).data('routeurl');
            var deleteModal = new bootstrap.Modal($("#dataFormDelete")[0])
            deleteModal.show();
            $('#dataDeleteForm').attr('action', routeurl);
        })
    </script>
@endpush