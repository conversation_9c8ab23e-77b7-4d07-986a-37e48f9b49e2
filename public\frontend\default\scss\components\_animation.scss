@use '../utils' as *;

/*----------------------------------------*/
/* Animations styles
/*----------------------------------------*/
// Animate ripple
@keyframes popupBtn {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }

  50% {
    transform: scale(1.4);
    opacity: 0.3;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// sticky-animation
@keyframes sticky {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0%);
  }
}

@keyframes tdSpinner {
  from {
    -webkit-transform: rotate(0turn);
    transform: rotate(0turn);
  }

  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn);
  }
}

// Banner shape 
@keyframes banner-shape-anim {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-10px);
  }
}

@keyframes banner-shape-anim-2 {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-15px);
  }
}

@keyframes banner-shape-anim-3 {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-40px);
  }
}

.upDown {
  animation: upDown 1s infinite alternate;
}

@keyframes upDown {
  0% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(-15px);
  }
}

@keyframes animationglob {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

// animation bounce
@keyframes icon-bounce {

  0%,
  100%,
  20%,
  50%,
  80% {
    @include transform(translateY(0));
  }

  40% {
    @include transform(translateY(-10px));
  }

  60% {
    @include transform(translateY(-5px));
  }
}

@keyframes animation_scale {
  from {
    transform: scale(1)
  }

  to {
    transform: scale(.7)
  }
}

@keyframes iconltr {
  49% {
    transform: translateX(30%);
  }

  50% {
    opacity: 0;
    transform: translateX(-30%);
  }

  51% {
    opacity: 1;
  }
}


@keyframes tada {
  0% {
    transform: scaleX(1);
  }

  10% {
    transform: scale3d(.9, .9, .9) rotate(-3deg);
  }

  20% {
    transform: scale3d(.9, .9, .9) rotate(-3deg);
  }

  30% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  50% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  70% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  90% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(3deg);
  }

  40% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }

  60% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }

  80% {
    transform: scale3d(1.1, 1.1, 1.1) rotate(-3deg);
  }

  to {
    transform: scaleX(1);
  }
}

.tada {
  animation-name: tada;
}