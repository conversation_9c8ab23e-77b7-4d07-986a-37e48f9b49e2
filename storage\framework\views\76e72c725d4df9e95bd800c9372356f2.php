<?php $__env->startSection('title'); ?>
    <?php echo e(__('Add New Miner')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <div class="main-content">
        <div class="page-title">
            <div class="row">
                <div class="col">
                    <div class="title-content">
                        <h2 class="title"><?php echo e(__('Add New Miner')); ?></h2>
                        <div>
                            <a href="<?php echo e(route('admin.miner.index')); ?>" class="title-btn">
                                <i icon-name="corner-down-left"></i>
                                <?php echo e(__('Back')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-xl-8">
                <div class="site-card">
                    <div class="site-card-body">
                        <form action="<?php echo e(route('admin.miner.store')); ?>" method="post">
                            <?php echo csrf_field(); ?>
                            <div class="row">
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label" for="name"><?php echo e(__('Name:')); ?></label>
                                        <input type="text" class="box-input" id="name" name="name" value="<?php echo e(old('name')); ?>" required />
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label" for="coin_id"><?php echo e(__('Coin:')); ?></label>
                                        <select class="form-select" id="coin_id" name="coin_id" required>
                                            <option value="" selected disabled><?php echo e(__('Select Coin')); ?></option>
                                            <?php $__currentLoopData = $coins; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $coin): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($coin->id); ?>" <?php echo e(old('coin_id') == $coin->id ? 'selected' : ''); ?>>
                                                    <?php echo e($coin->name); ?> (<?php echo e($coin->symbol); ?>)
                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <?php if (isset($component)) { $__componentOriginalad166283fcb1c13cc43904732a27ca08 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalad166283fcb1c13cc43904732a27ca08 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.admin.status-radio','data' => ['label' => 'Status','name' => 'status','value' => old('status','active')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.status-radio'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => 'Status','name' => 'status','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('status','active'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalad166283fcb1c13cc43904732a27ca08)): ?>
<?php $attributes = $__attributesOriginalad166283fcb1c13cc43904732a27ca08; ?>
<?php unset($__attributesOriginalad166283fcb1c13cc43904732a27ca08); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalad166283fcb1c13cc43904732a27ca08)): ?>
<?php $component = $__componentOriginalad166283fcb1c13cc43904732a27ca08; ?>
<?php unset($__componentOriginalad166283fcb1c13cc43904732a27ca08); ?>
<?php endif; ?>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups position-relative">
                                        <label class="box-input-label" for=""><?php echo e(__('Network Hashrate:')); ?></label>
                                        <div class="position-relative">
                                            <input type="text" class="box-input" data-validate="decimal" name="network_hashrate_amount" value="<?php echo e(old('network_hashrate_amount')); ?>">
                                            <div class="prcntcurr">
                                                <select name="network_hashrate" class="form-select">
                                                    <?php $__currentLoopData = $hashrates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hashrate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($hashrate); ?>" <?php echo e(old('network_hashrate') == $hashrate ? 'selected' : ''); ?>>
                                                            <?php echo e($hashrate); ?>

                                                        </option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label" for="renewable_energy"><?php echo e(__('Renewable Energy:')); ?></label>
                                        <div class="input-group joint-input">
                                            <input type="number" class="form-control" id="renewable_energy" name="renewable_energy" value="<?php echo e(old('renewable_energy')); ?>" step="1" min="1" max="100" required />
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-6">
                                    <div class="site-input-groups">
                                        <label class="box-input-label" for="uptime"><?php echo e(__('Uptime:')); ?></label>
                                        <div class="input-group joint-input">
                                            <input type="number" class="form-control" id="uptime" name="uptime" value="<?php echo e(old('uptime')); ?>" step="1" min="1" max="100" required />
                                            <span class="input-group-text">%</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xl-12">
                                    <button type="submit" class="site-btn primary-btn w-100">
                                        <?php echo e(__('Save Miner')); ?>

                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/miner/create.blade.php ENDPATH**/ ?>