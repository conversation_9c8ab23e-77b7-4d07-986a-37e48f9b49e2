<div class="col-xxl-12">
    <div class="row gy-30">
        <div class="col-xxl-12">
            <!-- KYC Verification area start -->
            <div class="KYC-verification-are default-area-style">
                <div class="heading-top">
                    <h5 class="title">{{ __('Verification Center') }}</h5>
                </div>
                <div class="default-content-inner">
                    <div class="verification-inner-wrapper">
                        <div class="identity-alert-buttons">
                            @forelse($data['kycs'] as $kyc)
                                <a href="{{ route('user.kyc.submission', [encrypt($kyc->id), 'type' => 'kyc']) }}"
                                   class="td-btn btn-chip grd-fill-btn-primary">
                                    <span class="btn-icon">
                                        <iconify-icon icon="tabler:cards"></iconify-icon>
                                    </span>
                                    <span class="btn-text">{{ $kyc->name }}</span>
                                </a>
                            @empty
                                <p class="mb-0">
                                    <i>{{ __('You have nothing to submit') }}</i>
                                </p>
                            @endforelse
                        </div>
                    </div>
                </div>
            </div>
            <!-- KYC Verification area end -->
        </div>
        
        <div class="col-xxl-12">
            <!-- KYC history area start -->
            <div class="KYC-verification-are default-area-style">
                <div class="heading-top">
                    <h5 class="title">{{ __('Verification History') }}</h5>
                </div>
                <div class="default-content-inner">
                    <div class="verification-inner-wrapper d-flex flex-column gap-3">
                        @forelse ($data['user_kycs'] as $user_kyc)
                            <div @class([
                                'identity-alert',
                                'danger' => $user_kyc->status == 'rejected',
                                'success' => $user_kyc->status == 'approved',
                                'info' => $user_kyc->status == 'pending'
                            ])>
                                <div class="left-contents">
                                    <div class="icon">
                                        <img src="{{ frontendAsset('images/icons/alert/' . ($user_kyc->status == 'rejected' ? 'danger-close.svg' : ($user_kyc->status == 'approved' ? 'success.svg' : 'info.svg'))) }}" alt="">
                                    </div>
                                    <div class="contents">
                                        <h4 class="title">{{ $user_kyc->kyc->name }}</h4>
                                        <div class="content">
                                            <p>{{ __('Submission date:') }} {{ date('d M Y h:i A', strtotime($user_kyc->created_at)) }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="right-actions">
                                    <span class="td-badge fill-badge-{{ match($user_kyc->status) {
                                        'approved' => 'success',
                                        'rejected' => 'danger',
                                        'pending' => 'warning',
                                        default => 'primary',
                                    } }}">{{ ucfirst($user_kyc->status) }}</span>
                                    <a class="td-underline-btn has-grad-one" href="javascript:void(0)" id="openModal" data-id="{{ $user_kyc->id }}">
                                        {{ __('View Details') }}
                                    </a>
                                </div>
                            </div>
                            @empty
                            <x-no-data-found class="mt-10" module="{{ __('KYC History') }}" />
                        @endforelse
        
                        
                    </div>
                </div>
            </div>
            <!-- KYC history area end -->
        </div>
        
        <!-- Modal placed once -->
        <div class="modal default-model fade" id="kycDetailsModal" tabindex="-1" aria-labelledby="kycDetailsModalLabel" aria-modal="true" role="dialog">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
        
                    <div class="modal-header">
                        <h1 class="modal-title fs-5" id="kycDetailsModalLabel">{{ __('KYC Details') }}</h1>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
        
                    <div class="modal-body pt-0">
                        <div class="modal-wallet-contents pt-3">
                        </div>
        
                        <div class="modal-buttons mt-3 d-flex gap-3 flex-wrap justify-content-end">
                            <button type="button" class="td-btn btn-chip white-btn-12 back-btn" data-bs-dismiss="modal" aria-label="Close">
                                <span class="btn-text">Cancel</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script>
        $(document).on('click', '#openModal', function() {
            "use strict";

            let id = $(this).data('id');

            $.get("{{ route('user.kyc.details') }}", {
                id: id
            }, function(response) {
                $('.modal-wallet-contents').html(response.html);
                $('#kycDetailsModal').modal('show');
            });
        });
    </script>
@endpush