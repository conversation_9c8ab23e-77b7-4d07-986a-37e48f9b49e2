@use '../../utils' as *;

/*----------------------------------------*/
/*  Header actions Styles
/*----------------------------------------*/
// Theme Switcher
.theme-switcher {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.currency-switcher {
    position: relative;

    .select2-container--default .select2-selection--single .select2-selection__arrow {
        inset-inline-end: -6px;
    }

    .select2-container--default:not([dir=rtl]) .select2-selection--single .select2-selection__rendered {
        padding-inline-start: 0;
    }

    .select2-container--default .select2-selection--single .select2-selection__rendered {
        padding-inline-end: 26px;
    }

    .defaults-select .select2-dropdown {
        min-width: 100px;
    }
}

.quick-action-item {
    .action-icon {
        width: 32px;
        height: 32px;
        background-color: rgba($heading, $alpha: 0.1);
        border-radius: 100px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: var(--td-heading);
        font-size: 18px;

        @include dark-theme {
            color: var(--td-white);
            background: rgba(255, 255, 255, 0.1);
        }

        &.notification-btn {

            iconify-icon {
                -webkit-animation: tada 1.5s ease infinite;
                animation: tada 1.5s ease infinite;
            }

            &.active {
                position: relative;

                &::before {
                    position: absolute;
                    content: "";
                    height: 8px;
                    width: 8px;
                    background-color: #FB405A;
                    border-radius: 50%;
                    top: 0;
                    inset-inline-end: 0;
                }
            }
        }
    }
}

// Profile contents
.profile-card-box {
    @include flexbox();
    align-items: end;
    justify-content: space-between;
    gap: 12px 12px;
    flex-wrap: wrap;
}

.profile-card {
    .profile-title {
        font-size: clamp(2rem, 1.5rem + 2vw, 2rem);
        margin-bottom: 10px;
    }
}

.create-project-container {
    position: relative;
}

.create-project-btn {
    padding: 10px 20px;
    background-color: #f5f7fa;
    border: 1px solid var(--td-border-primary);
    @include border-radius(5px);
    cursor: pointer;
}

// buttons-dropdown-menu
.buttons-dropdown-menu {
    display: none;
    position: absolute;
    top: calc(100% + 5px);
    inset-inline-end: 0;
    width: 220px;
    background: var(--td-white);
    border: 1px solid rgba(171, 178, 225, 0.3);
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.2);
    @include border-radius(8px);
    z-index: 1;

    ul {
        list-style: none;
        padding: 10px;
        margin: 0;
    }

    li {
        padding: 8px 15px;
        cursor: pointer;
        @include border-radius(8px);
        @include flexbox();
        align-items: center;
        gap: 8px;
        font-size: 14px;

        svg {
            height: 16px;
            width: 16px;
        }

        &:hover {
            background-color: var(--td-alice-blue);
        }
    }
}

// User dropdown
.user-profile-drop {
    position: relative;
    cursor: pointer;

    .dropdown-menu {
        top: calc(100% + 12px);
        inset-inline-end: 0;
        z-index: 9;
        background: var(--td-white);
        transform: translateY(-20px);
        opacity: 0;
        cursor: pointer;
        visibility: hidden;
        transition: all 0.3s cubic-bezier(0.25, 1.15, 0.35, 1.15);
        position: absolute;
        display: block;
        width: 282px;
        border-radius: 16px;
        padding-top: 0 !important;
        background: var(--td-white);
        border: 1px solid #D1D4DA;
        backdrop-filter: blur(48.5px);

        @include dark-theme {
            border-color: #0B277A;
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
        }

        @include dark-theme {
            background-color: #171C35;
        }

        &.show {
            visibility: visible;
            opacity: 1;
            transform: translateY(0px);
        }
    }

    .profile-info-list {
        display: flex;
        flex-direction: column;
        padding: 16px 16px;
        gap: 2px;

        @media #{$xs,$sm} {
            padding: 12px 12px;
        }

        .profile-info-item {
            border-radius: 8px;
            padding: 12px 15px;
            display: flex;
            gap: 8px;
            font-size: 14px;
            font-weight: 700;

            @include dark-theme {
                color: var(--td-text-primary);
            }

            .icon {
                display: inline-flex;
                align-items: center;
                font-size: 20px;
            }

            &:hover {
                background: rgba($color: $white, $alpha: .5);
                color: var(--td-heading);
            }

            &.profile-log-out {
                background-color: rgba(235, 78, 92, 0.16);
                color: var(--td-danger);

                .icon {
                    span {
                        color: var(--td-danger);
                    }
                }
            }
        }
    }
}

// switcher
.theme-switcher {
    .dark-mode {
        display: none;
    }
}

.dark-theme {
    .light-mode {
        display: none;
    }

    .dark-mode {
        display: inline-flex;
        align-items: center;
        justify-content: center;

        svg * {
            stroke: rgba($white, $alpha: .6);
        }
    }
}