@extends('frontend::layouts.user')

@section('title')
   {{ __('Mining') }}
@endsection
@use('App\Enums\TxnType')
@use('App\Models\Coin')
@use('App\Enums\UserMiningStatus')
@section('content')
   <div class="col-xxl-12">
      <div class="page-title-wrapper mb-16">
        <div class="page-title-contents">
          <h3 class="page-title">{{ __('Mining') }}</h3>
        </div>
      </div>
      @include('frontend::user.mining.include.navigation')
   </div>
   <div class="col-xxl-12">
        <form action="{{ route('user.mining.history') }}" method="get">
            <div class="filter-bar">
                <!-- Search Field -->
                <div class="filter-item">
                    <div class="clip-path-inner">
                        <div class="filter-bar-search">
                            <span class="search-icon">
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <g clip-path="url(#clip0_389_9256)">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.66668 1.83398C4.44502 1.83398 1.83334 4.44566 1.83334 7.66732C1.83334 10.889 4.44502 13.5007 7.66668 13.5007C10.8883 13.5007 13.5 10.889 13.5 7.66732C13.5 4.44566 10.8883 1.83398 7.66668 1.83398ZM0.833344 7.66732C0.833344 3.89337 3.89273 0.833984 7.66668 0.833984C11.4406 0.833984 14.5 3.89337 14.5 7.66732C14.5 9.37433 13.8741 10.9351 12.8393 12.1328L15.0202 14.3138C15.2155 14.509 15.2155 14.8256 15.0202 15.0209C14.825 15.2161 14.5084 15.2161 14.3131 15.0209L12.1321 12.8399C10.9345 13.8747 9.37368 14.5007 7.66668 14.5007C3.89273 14.5007 0.833344 11.4413 0.833344 7.66732Z" fill="#999999"></path>
                                    </g>
                                    <defs>
                                    <clipPath id="clip0_389_9256">
                                    <rect width="16" height="16" fill="white"></rect>
                                    </clipPath>
                                    </defs>
                                 </svg>
                            </span>
                            <input value="{{ request('txn') }}" class="input-box" name="txn" type="text" placeholder="Transaction ID">
                        </div>
                    </div>
                </div>

                <div class="filter-item">
                    <div class="clip-path-inner">
                       <div class="td-form-group has-multiple">
                          <div class="input-field-inner">
                             <div class="input-field">
                                <select name="status" class="defaultselect2">
                                    <option value="">{{ __('All') }}</option>
                                    @foreach (UserMiningStatus::cases() as $status)
                                        <option value="{{ $status->value }}" {{ request('status') == $status->value ? 'selected' : '' }}>
                                           {{ str($status->value)->headline() }}
                                        </option>
                                    @endforeach
                                    </select>
                             </div>
                          </div>
                       </div>
                    </div>
                 </div>
                <div class="filter-item">
                    <div class="clip-path-inner">
                       <div class="td-form-group has-multiple">
                          <div class="input-field-inner">
                             <div class="input-field">
                                <select name="wallet" class="defaultselect2">
                                    <option value="">{{ __('All') }}</option>
                                    @foreach ($user->wallets as $wallet)
                                        <option value="{{ $wallet->coin_id }}" {{ request('wallet') == $wallet->coin_id ? 'selected' : '' }}>
                                           {{ $wallet->coin->name }} ({{ str($wallet->coin->code)->trim() }})
                                        </option>
                                    @endforeach
                                    </select>
                             </div>
                          </div>
                       </div>
                    </div>
                 </div>
                <!-- Date Picker -->
                <div class="filter-item">
                    <div class="td-form-group has-right-icon">
                        <div class="input-field">
                            <input type="text" value="{{ request('date',today()) }}" name="date" id="datePicker" class="flatpickr form-control" placeholder="Select date" readonly>
                        </div>
                    </div>
                </div>
        
                <!-- Filter Button -->
                <div class="filter-item">
                    <button type="submit" class="td-btn btn-sm btn-chip grd-fill-btn-primary">
                        <span class="btn-text">{{ __('Search') }}</span>
                    </button>
                </div>
            </div>
        </form>
        <div class="recent-history-table">
            <div class="table-container table-responsive">
                <table class="td-table recent-table">
                    <thead>
                        <tr>
                            <th>{{ __('Transaction ID') }}</th>
                            <th>{{ __('Amount') }}</th>
                            <th>{{ __('Plan') }}</th>
                            <th>{{ __('Status') }}</th>
                            <th>{{ __('Miner') }}</th>
                            <th>{{ __('Total Mined Amount') }}</th>
                            <th>{{ __('Total Mined Count') }}</th>
                            <th>{{ __('Mining Time') }}</th>
                            <th>
                                {{ __('Details') }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse ($transactions as $transaction)
                            <tr>
                                <td><span>#{{ $transaction->tnx }}</span></td>
                                <td>
                                    {{ formatAmount($transaction->amount, $transaction->wallet?->coin, true) }}
                                </td>
                                <td>
                                    {{ $transaction->scheme?->name }}
                                </td>
                                <td>
                                    @php
                                        $statusClass = match ($transaction->userMining?->status) {
                                            'Success' => 'badge-success',
                                            'Pending' => 'badge-warning',
                                            'Failed' => 'badge-danger',
                                            default => 'badge-default',
                                        };
                                    @endphp
                                    <span class="td-badge {{ $statusClass }}">{{ ucwords($transaction->userMining?->status) }}</span>
                                </td>
                                <td>
                                    {{ $transaction->scheme?->miner?->name }}
                                </td>

                                <td>
                                    <span class="">{{ $transaction->userMining?->total_mined_amount.' '.$transaction->scheme?->miner?->coin?->code }}</span>
                                </td>
                                <td>
                                    {{ $transaction->userMining->mining_count ?? 0 }} of {{ $transaction->scheme?->return_period_type == 'period' ? $transaction->scheme?->return_period_max_number : 'Unlimited' }}
                                </td>
                                <td>
                                    {{ __('Last Mining: ') .($transaction->userMining?->last_mining_time ? now()->parse($transaction->userMining?->last_mining_time)->diffForHumans() : 'N/A') }}
                                    <br>
                                    {{ __('Next Mining: ') .($transaction->userMining?->next_mining_time ? now()->parse($transaction->userMining?->next_mining_time)->diffForHumans() : 'N/A') }}
                                </td>
                                <td>
                                    <a href="{{ route('user.mining.details', $transaction->id) }}" class="td-btn btn-xs grd-fill-btn-primary radius-30" data-bs-toggle="tooltip" title="Details">
                                        <span class="btn-icon">
                                            <iconify-icon icon="tabler:send"></iconify-icon>
                                        </span>
                                        <span class="btn-text">Details</span>
                                    </a>
                                </td>
            
                            </tr>
                        @empty
                            <tr>
                                <td colspan="{{ Route::is('user.mining.history') ? 11 : 10 }}" class="text-center">
                                        <x-no-data-found class="mt-10" module="{{ Route::is('user.mining.history') ? __('Mining') : __('Transaction') }}" />
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            @if (method_exists($transactions, 'links'))
            {{ $transactions->links() }}
            @endif
        </div>
   </div>
@endsection
@push('js')
<link rel="stylesheet" href="https://unpkg.com/flatpickr@4.6.13/dist/themes/dark.css">
<script src="{{ frontendAsset('js/flatpickr.js') }}"></script>
    <script>
         (function ($) {
         'use strict';
         flatpickr("#datePicker", {
            mode: "range",
            dateFormat: "Y-m-d",
            defaultDate: "{{ request('date',today()) }}"
         });
      })(jQuery);
    </script>
@endpush