@use '../../utils' as *;

/*----------------------------------------*/
/* Postbox styles
/*----------------------------------------*/
.postbox-main-wrapper {
	padding-inline-end: 60px;

	@media #{$xs,$sm,$md,$lg} {
		padding-inline-end: 0;
	}
}

.postbox-details-contents {
	p {
		&:not(:last-child) {
			margin-bottom: 30px;
		}
	}

	h2 {
		line-height: 1.27;
		margin-bottom: 16px;
	}

	h3 {
		line-height: 1.27;
		margin-bottom: 16px;
	}

	h5 {
		padding-bottom: 18px;
		border-bottom: 1px solid rgba($heading, $alpha: 0.1);
		margin-bottom: 18px;

		@include dark-theme {
			border-color: rgba($white, $alpha: 0.1);
		}
	}

	h6 {
		padding-bottom: 14px;
	}

	ul {
		list-style-type: disc;
		padding-inline-start: 28px;
		margin-bottom: 16px;

		span {
			font-size: 14px;
			margin: 0;
			color: var(--td-text-primary);

			@include dark-theme {
				color: #9A9DA7;
			}
		}

		li {
			position: relative;
			font-size: 16px;

			&:not(:last-child) {
				margin-bottom: 12px;
			}
		}
	}

	hr {
		border: 1px solid rgba($heading, $alpha: 0.16);

		@include dark-theme {
			border-color: rgba($white, $alpha: 0.1);
		}
	}
}

.postbox-thumb-inner {
	position: relative;
	padding: 1px;
	overflow: hidden;
	margin-bottom: 25px;

	&::before,
	&::after {
		position: absolute;
		inset-inline-start: 0;
		top: 0;
		transition: all .3s;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
		opacity: 0.6;
		content: "";
		clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
		border-radius: 2px;
	}

	.thumbnail {
		position: relative;
		z-index: 3;
		background: #04060a;
		clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
		gap: 12px;
		width: 100%;

		img {
			width: 100%;
		}

		&::before {
			position: absolute;
			top: 0;
			inset-inline-start: 0;
			content: "";
			background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
			z-index: -1;
			width: 100%;
			height: 100%;
		}
	}
}

.postbox-img {
	position: relative;
	z-index: 3;
	background: #04060a;
	clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
	gap: 12px;
	width: 100%;

	&::before {
		position: absolute;
		top: 0;
		inset-inline-start: 0;
		content: "";
		background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
		z-index: -1;
		width: 100%;
		height: 100%;
	}

	img {
		width: 100%;
		@include border-radius(16px);
	}
}

// Postbox meta
.postbox-meta {
	ul {
		display: flex;
		align-items: center;
		gap: 12px 20px;
		flex-wrap: wrap;
		padding-inline-start: 0 !important;

		li {
			list-style: none;
			display: flex;
			align-items: center;
			gap: 6px;
			list-style: none;
			margin: 0;
			padding: 0;
			position: relative;
			border-bottom: 0;

			&::before {
				display: none;
			}

			&:not(:last-child) {
				margin-bottom: 0;
			}

			.icon {
				font-size: 18px;
				display: inline-flex;
			}

			span {
				color: var(--td-text-primary);

				@include dark-theme {
					color: #9A9DA7;
				}
			}
		}
	}
}

// Postbox share 
.postbox-share {
	@include flexbox();
	align-items: center;
	gap: 16px;
	margin-top: 30px;

	h6 {
		padding-bottom: 0;
	}

	a {
		font-size: 16px;
		color: #BBBBBB;
		display: flex;
		flex-direction: row;
		gap: 10px;
		align-items: center;
		justify-content: center;

		&:hover {
			border-color: var(--td-primary);
		}
	}
}

// Tag cloud
.tagcloud-items {
	@include flexbox();
	gap: 8px;
	align-items: self-start;
}

.tagcloud-box {
	@include flexbox();
	align-items: center;
	flex-wrap: wrap;
	gap: 8px 8px;

	a {
		font-size: 14px;
		@include inline-flex();
		align-items: center;
		position: relative;
		text-transform: capitalize;
		;
		background-color: rgba(114, 128, 255, 0.1);
		border: 1px solid rgba(114, 128, 255, 0.2);
		color: rgba($heading, $alpha: 0.6);
		padding: 5px 14px;
		@include border-radius(4px);
		cursor: pointer;
		font-weight: 600;

		@include dark-theme {
			color: rgba($white, $alpha: 0.6);
		}
	}
}

// Sidebar widgets
.sidebar-sticky {
	position: sticky;
	top: 80px;
}

.sidebar-widget-inner {
	display: flex;
	flex-direction: column;
	gap: 30px;
}

.sidebar-widget-content-box {
	padding: 25px 30px 30px;
	background: #f6f6ff;
	border-radius: 16px;

	@include dark-theme {
		background: rgba(255, 255, 255, 0.04);
		border: 1px solid rgba($heading, $alpha: 0.16);
	}

	@media #{$xxs} {
		padding: 16px 16px 16px;
	}
}

.sidebar-wrapper {
	@media #{$xs,$sm,$md} {
		padding-inline-start: 0;
	}
}

.sidebar-widget-title {
	position: relative;
	display: inline-block;
	font-size: 23px;
	margin-bottom: 20px;

	@media #{$xs} {
		margin-bottom: 18px;
	}
}