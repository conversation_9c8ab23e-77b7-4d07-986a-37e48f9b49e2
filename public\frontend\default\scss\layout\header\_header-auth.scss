@use '../../utils' as *;

/*----------------------------------------*/
/*  Header auth styles
/*----------------------------------------*/

// Heder one styles
.header-auth-area {
  padding: 20px;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}

.header-auth-quick-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: end;

  .theme-switcher-item {
    background: #fefefe;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
    width: 40px;
    height: 40px;

    @include dark-theme {
      background: #16213F;
    }
  }

  .language-dropdown {
    background: #fefefe;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
    width: max-content;
    height: 40px;
    min-width: 80px;
    padding-inline-start: 14px;
    padding-inline-end: 10px;

    @include dark-theme {
      background: #16213F;
    }

    .language-nav .current_lang {
      margin-inline-end: 0px;
    }
  }

  .language-nav {
    .more_lang {
      .lang:hover {
        background: #dddddd;
        color: var(--td-heading);

        @include dark-theme {
          background: rgba($heading, $alpha: 0.3);
        }
      }
    }

    .translate_wrapper {

      &.active {
        .more_lang {
          background: var(--td-white) !important;
          background-color: rgb(255, 255, 255);
          top: 45px;
          inset-inline-start: auto;
          inset-inline-end: 0;

          @include dark-theme {
            background-color: #230c6b !important;
          }
        }
      }
    }

    .current_lang {
      .lang {
        svg * {
          @include dark-theme {
            stroke: #9a9da7;
          }
        }
      }
    }
  }
}