<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>OreXcoin - Documentation</title>
  <link rel="shortcut icon" href="images/favicon.png" type="image/x-icon" />
  <link rel="stylesheet" href="assets/css/bootstrap.min.css" />
  <link rel="stylesheet" href="assets/css/all.min.css" />
  <link rel="stylesheet" href="assets/css/style.css" />

  <link rel="preconnect" href="https://fonts.gstatic.com" />
  <link href="https://fonts.googleapis.com/css2?family=Jost:wght@400;700&display=swap" rel="stylesheet" />
</head>

<body>
  <header>
    <nav class="navbar navbar-expand-lg navbar-light bg-white">
      <div class="container-fluid">
        <a class="navbar-brand order-2 mr-auto ml-3 ml-lg-0" href="#">
          <img src="images/logo-dark.png" alt="" />
        </a>
        <button class="navbar-toggler order-1" type="button" data-toggle="collapse">
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="order-3">
          <ul class="navbar-nav ml-auto">
            <li class="nav-item active">
              <a class="btn custom-btn" href="https://codecanyon.net/user/tdevs/portfolio" target="_blank">Purchase
                Now</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>
  </header>

  <section class="container-fluid">
    <aside class="left-menu">
      <nav>
        <ul>
          <!-- Menu Title -->
          <li class="left-menu-title">
            <h6>
              <a href="#getting-started">Getting started</a>
            </h6>
          </li>

          <li class="left-menu-item">
            <a href="#introduction">
              <span>introduction</span>
            </a>
          </li>

          <li class="left-menu-item">
            <a href="#key-fatures">
              <span>Key Features</span>
            </a>
          </li>

          <li class="left-menu-item">
            <a href="#file-stricture">
              <span>folder structure</span>
            </a>
          </li>

          <li class="left-menu-item">
            <a href="#system-requirements">
              <span>system requirements</span>
            </a>
          </li>

          <!-- Menu Title -->
          <li class="left-menu-title">
            <h6>
              <a href="#how-to-install">How to install</a>
            </h6>
          </li>

          <li class="left-menu-item">
            <a href="#live-server">
              <span>Installation Steps</span>
            </a>
          </li>

          <!-- Menu Title -->
          <li class="left-menu-title">
            <h6>
              <a href="#admin-panel">Admin Panel</a>
            </h6>
          </li>

          <li class="left-menu-item">
            <a href="#admin-dashboard">
              <span>Dashboard</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-role-management">
              <span>Role Management</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-miners">
              <span>Miners</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-portfolio">
              <span>Portfolio</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-referral">
              <span>Referral</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-schemes">
              <span>Mining Schemes</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-gateways">
              <span>Gateways</span>
            </a>
          </li>

          <li class="left-menu-title">
            <h6>
              <a href="#admin-settings">Settings</a>
            </h6>
          </li>
          <li class="left-menu-item">
            <a href="#admin-settings">
              <span>Settings</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-plugins">
              <span>Plugins</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-language">
              <span>Language</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-custom_css">
              <span>Custom CSS</span>
            </a>
          </li>

          <li class="left-menu-title">
            <h6>
              <a href="#admin-pages">Site Content</a>
            </h6>
          </li>
          <li class="left-menu-item">
            <a href="#admin-section">
              <span>Dynamic Section</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-page">
              <span>Dynamic Page</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#admin-theme">
              <span>Dynamic Theme</span>
            </a>
          </li>


          <li class="left-menu-title">
            <h6>
              <a href="#user-dashboard">User Panel</a>
            </h6>
          </li>

          <li class="left-menu-item">
            <a href="#user-dashboard">
              <span>Dashboard</span>
            </a>
          </li>

          <li class="left-menu-item">
            <a href="#user-listing">
              <span>Listing</span>
            </a>
          </li>
          <li class="left-menu-item">
            <a href="#user-payment">
              <span>Payment</span>
            </a>
          </li>

          <li class="left-menu-item">
            <a href="#userChangePassword">
              <span>Change Password</span>
            </a>
          </li>



          <!-- Menu Title -->
          <li class="left-menu-title">
            <h6>
              <a href="#release-log">Thank you</a>
            </h6>
          </li>

          <li class="left-menu-item">
            <a href="#release-log">
              <span>Release log</span>
            </a>
          </li>
        </ul>
      </nav>
    </aside>
    <section class="main-content-area">
      <div class="main-content container-fluid">
        <!-- ============== Big Title Section ============================ -->
        <div class="mb-40 pb-40 border-bottom">
          <h3 id="getting-started" class="first-title">
            OrexCoin - Modern Cryptocurrency Mining & Earnings System
          </h3>
        </div>

        <!-- ============== Section ============================ -->
        <section>
          <h5 class="mb-40">Thank you for purchasing OrexCoin! �</h5>
          <h6><i>For free cPanel installation service please write us at <strong><u><EMAIL></u></strong> with
              your purchase code.</i></h6>

          <h4 id="introduction" class="second-title">Introduction</h4>
          <p>
            OrexCoin is a modern cryptocurrency mining and earnings platform designed to help users generate passive income through digital assets. Our platform offers secure cloud-based mining solutions, flexible investment plans, and comprehensive cryptocurrency management tools. It seamlessly integrates multiple cryptocurrencies with advanced security features including Google 2FA and KYC verification, providing a secure environment for digital mining, user management, and earnings tracking.
          </p>
        </section>

        <!-- ============== Section ============================ -->
        <section>
          <h4 id="key-features" class="second-title">Key Features</h4>

          <div class="row">
            <div class="col-md-6">
              <h6 class="mt-20 mb-15">⛏️ <strong>Mining & Earnings</strong></h6>
              <ul class="right">
                <li>Multiple Cryptocurrency Mining (Bitcoin, Ethereum, BNB, XRP)</li>
                <li>Flexible Mining Plans (Basic, Pro, Lifetime)</li>
                <li>Cloud-Based Mining Solutions</li>
                <li>Automatic Mining Returns</li>
                <li>Real-Time Mining Statistics</li>
                <li>Mining History & Analytics</li>
                <li>Customizable Mining Speeds</li>
                <li>Maintenance Fee Management</li>
              </ul>
            </div>

            <div class="col-md-6">
              <h6 class="mt-20 mb-15">💳 <strong>Payment & Wallets</strong></h6>
              <ul class="right">
                <li>25+ Payment Gateway Support</li>
                <li>Multi-Currency Wallet System</li>
                <li>Automatic and Manual Payments</li>
                <li>Cryptocurrency Integration</li>
                <li>Secure Wallet Management</li>
                <li>Top-up System</li>
                <li>Instant Withdrawals</li>
                <li>Transaction History</li>
              </ul>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <h6 class="mt-20 mb-15">👥 <strong>User Management & Security</strong></h6>
              <ul class="right">
                <li>User Registration & Authentication</li>
                <li>Two-Factor Authentication (2FA)</li>
                <li>KYC Verification System</li>
                <li>Role-Based Access Control</li>
                <li>Google reCAPTCHA Protection</li>
                <li>Account Security Settings</li>
                <li>Email Verification</li>
                <li>Password Security Management</li>
              </ul>
            </div>

            <div class="col-md-6">
              <h6 class="mt-20 mb-15">🎁 <strong>Referral & Rewards</strong></h6>
              <ul class="right">
                <li>Multi-Level Referral System</li>
                <li>Referral Commission Management</li>
                <li>Signup Bonus Programs</li>
                <li>Portfolio Badge System</li>
                <li>Achievement Rewards</li>
                <li>Loyalty Programs</li>
                <li>Bonus Distribution</li>
                <li>Referral Analytics</li>
              </ul>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <h6 class="mt-20 mb-15">📊 <strong>Analytics & Reporting</strong></h6>
              <ul class="right">
                <li>Real-Time Dashboard</li>
                <li>Mining Performance Analytics</li>
                <li>Earnings Reports</li>
                <li>User Activity Tracking</li>
                <li>Google Analytics Integration</li>
                <li>Financial Reports</li>
                <li>Mining Statistics</li>
                <li>Performance Metrics</li>
              </ul>
            </div>

            <div class="col-md-6">
              <h6 class="mt-20 mb-15">⚙️ <strong>Platform Management</strong></h6>
              <ul class="right">
                <li>Admin Dashboard</li>
                <li>Theme Management</li>
                <li>Multi-Language Support</li>
                <li>SEO Optimization</li>
                <li>Email Notifications</li>
                <li>Site Maintenance Mode</li>
                <li>GDPR Compliance</li>
                <li>Custom CSS Support</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- ============== Section ============================ -->
        <section>
          <h4 id="file-stricture" class="second-title">Folder Structure</h4>
          <ul class="folder">
            <li>
              'OreXcoin' is bundled with folders:
              <ul>
                <li>Documentation</li>
                <li>main.zip</li>
              </ul>
            </li>
          </ul>
        </section>

        <!-- ============== Section ============================ -->
        <section>
          <h4 id="system-requirements" class="second-title">
            System requirements
          </h4>
          <p>
            The application has a few system requirements, so you will need to
            make sure your server meets the following requirements:
          </p>
          <ul class="right">
            <li>PHP >= 8.1</li>
            <li>MySQL 5.7+</li>
          </ul>

          <p>PHP Extensions <small>(Most of the servers, Enabled By the default.)</small>:</p>
          <ul class="right">
            <li>Ctype Extension</li>
            <li>cURL Extension</li>
            <li>DOM Extension</li>
            <li>Fileinfo Extension</li>
            <li>Mbstring Extension</li>
            <li>OpenSSL Extension</li>
            <li>PDO Extension</li>
            <li>Session Extension</li>
            <li>Tokenizer Extension</li>
            <li>XML Extension</li>
          </ul>
        </section>

        <!-- ============== Big Title Section ============================ -->
        <div class="mb-40 pb-40 border-bottom">
          <h3 id="how-to-install" class="first-title">How to install</h3>
        </div>

        <!-- ============== Section ============================ -->
        <section>
          <h4 id="live-server" class="second-title">Installation Steps</h4>
          <p>
            After you purchase <code>OreXcoin</code>, you will get a zip file.
            Extracting the zip file you will get an
            <code>main.zip</code> file. You have to upload
            <code>main.zip</code> in your server and extract it.
          </p>
          <p>
            <strong>follow the instruction below:</strong>
          </p>

          <ul class="right">
            <li>
              <b>Create a <code>database</code> from your phpmyadmin. Then import the database file
                <code>OreXcoin.sql</code></b>
              from the <code>DB</code> folder on your created database.
            </li>
            <li>
              <b>Then go to project <code>.env</code> file from the root directory of the project. And change the
                DB_DATABASE,
                DB_USERNAME, DB_PASSWORD as your created database.</b>
            </li>
            <li>
              <b>If you upload it in <code>public_html</code> folder, then
                visit <code>your_domain_name.com</code> in browser</b>
            </li>
            <li>
              <b>If you upload it in a folder in
                <code>public_html</code> folder, then visit
                <code>your_domain_name.com/folder_name</code> in browser.
              </b>
            </li>
            <li>
              <b>If you upload it in your created
                <code>subdomain</code> folder, then visit
                <code>your_subdomain_name.com</code> in browser.
              </b>
            </li>
          </ul>
          <p><b>Then you can visit your website url and access the admin dashboard.</b></p>
          <h4 id="live-server" class="second-title">Admin Dashboard</h4>
          <p><code>your_domain_name.com/admin</code></p>
          <p>Email: <b><EMAIL></b></p>
          <p>Password: <b>12345678</b></p>
        </section>
        <!-- ============== Section ============================ -->
        <section>

        </section>

        <div class="mb-40 pb-40 border-bottom">
          <h3 id="admin-panel" class="first-title">Admin Panel</h3>
        </div>
        <section>
          <h4 id="admin-dashboard" class="second-title">Dashboard</h4>
          <div class="right">
            <div class="image-border">
              <img src="images/overview/admin-dashboard.png" alt="" class="img-responsive" alt="">
            </div>
            <br />
            <br />

            <p>The OrexCoin admin dashboard is a comprehensive control center designed specifically for cryptocurrency mining platform management. It provides administrators with complete oversight of all mining operations, user activities, and financial transactions.</p>

            <h6 class="mt-20 mb-15"><strong>📊 Key Statistics & Data Cards</strong></h6>
            <ul class="right">
              <li><strong>Total Users:</strong> Monitor the complete user base including miners, merchants, and agents</li>
              <li><strong>All Deposits:</strong> Track total deposit transactions across all payment gateways</li>
              <li><strong>All Coins:</strong> Overview of supported cryptocurrencies (Bitcoin, Ethereum, BNB, XRP, etc.)</li>
              <li><strong>Staff Management:</strong> Monitor admin staff and role-based access control</li>
              <li><strong>Mining Statistics:</strong> Real-time data on active mining operations and earnings</li>
              <li><strong>Payment Gateway Status:</strong> Monitor the health of 25+ integrated payment systems</li>
              <li><strong>Support Tickets:</strong> Track customer support requests and resolution status</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>📈 Advanced Analytics & Charts</strong></h6>
            <ul class="right">
              <li><strong>Site Statistics Chart:</strong> Interactive bar chart showing deposits, withdrawals, and plan purchases over time with date range filtering</li>
              <li><strong>Multi-Currency Support:</strong> Switch between different cryptocurrencies to view specific coin statistics</li>
              <li><strong>Top Country Statistics:</strong> Doughnut chart displaying user distribution by geographical location</li>
              <li><strong>Browser Analytics:</strong> Pie chart showing user browser preferences for platform optimization</li>
              <li><strong>Operating System Data:</strong> Platform usage statistics to guide technical decisions</li>
              <li><strong>Real-time Data:</strong> All charts update automatically with live data from the mining platform</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>⚡ Action Center & Notifications</strong></h6>
            <ul class="right">
              <li><strong>Pending Requests Alert:</strong> Immediate notifications for items requiring admin attention</li>
              <li><strong>KYC Verification Queue:</strong> Quick access to pending user verification requests</li>
              <li><strong>Deposit Approvals:</strong> Manual deposit confirmations and processing</li>
              <li><strong>Withdrawal Management:</strong> Review and approve cryptocurrency withdrawal requests</li>
              <li><strong>Support Ticket Alerts:</strong> Instant notifications for new customer support requests</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>👥 Latest Users Management</strong></h6>
            <ul class="right">
              <li><strong>Recent Registrations:</strong> View the 5 most recent user registrations with complete profile information</li>
              <li><strong>User Status Monitoring:</strong> Quickly identify active vs. deactivated accounts</li>
              <li><strong>Balance Overview:</strong> Monitor user main wallet balances across different cryptocurrencies</li>
              <li><strong>Quick Actions:</strong> Direct links to edit user profiles, manage balances, and update account settings</li>
              <li><strong>Account Security:</strong> Access to user security settings including 2FA and KYC status</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>⛏️ Latest Miners Activity</strong></h6>
            <ul class="right">
              <li><strong>Active Mining Operations:</strong> Real-time view of the 5 most recent mining activities</li>
              <li><strong>Mining Plan Details:</strong> Complete information about purchased mining schemes and their performance</li>
              <li><strong>Transaction Tracking:</strong> Direct links to transaction details and mining history</li>
              <li><strong>Mining Progress:</strong> Track mining count progress and next mining time for each operation</li>
              <li><strong>Earnings Monitoring:</strong> View total mined amounts and plan profitability</li>
              <li><strong>Status Management:</strong> Monitor ongoing, completed, and paused mining operations</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>🎯 Dashboard Benefits</strong></h6>
            <ul class="right">
              <li><strong>Centralized Control:</strong> Manage all aspects of the cryptocurrency mining platform from one interface</li>
              <li><strong>Real-time Insights:</strong> Make informed decisions with live data and comprehensive analytics</li>
              <li><strong>Efficient Workflow:</strong> Streamlined processes for handling user requests and platform maintenance</li>
              <li><strong>Security Monitoring:</strong> Keep track of platform security with user activity and login analytics</li>
              <li><strong>Performance Optimization:</strong> Use browser and OS data to optimize platform performance</li>
              <li><strong>Financial Oversight:</strong> Complete visibility into deposits, withdrawals, and mining earnings</li>
            </ul>

            <p class="mt-20">The dashboard is designed to provide administrators with everything needed to efficiently manage a cryptocurrency mining platform, from user onboarding to mining operations and financial transactions, all through an intuitive and secure interface.</p>
          </div>
        </section>

        <section>
          <h4 id="admin-role-management" class="second-title">Role Management</h4>
          <div class="right">
            <p>The Role Management feature allows the admin to create new roles and assign specific permissions,
              ensuring secure and efficient access control across the site.</p>
            <ul class="right">
              <li><strong>Add Role:</strong> Admins can create new roles to define user responsibilities and access
                levels. Examples of roles include Accountant, Staff, Manager, and more.</li>
              <li><strong>Permission:</strong> After creating a new role, specific permissions can be configured to
                determine what actions the role can perform and which areas of the site they can access, ensuring a
                tailored and secure experience.</li>
            </ul>
          </div>
        </section>

        <section>
          <h4 id="admin-miners" class="second-title">Miners</h4>
          <div class="right">
            <p>The Miners management system is the backbone of your cryptocurrency mining platform, allowing you to create and manage virtual mining hardware for different cryptocurrencies. Each miner represents a specific cryptocurrency mining operation with its own performance characteristics and capabilities.</p>

            <h6 class="mt-20 mb-15"><strong>⛏️ Miner Creation & Configuration</strong></h6>
            <ul class="right">
              <li><strong>Cryptocurrency Assignment:</strong> Associate each miner with a specific cryptocurrency (Bitcoin, Ethereum, BNB, XRP, etc.)</li>
              <li><strong>Miner Naming:</strong> Create descriptive names for miners like "Bitcoin Pro Miner" or "Ethereum Cloud Miner"</li>
              <li><strong>Renewable Energy Settings:</strong> Configure the percentage of renewable energy used (1-100%) for eco-friendly mining</li>
              <li><strong>Uptime Configuration:</strong> Set miner uptime percentage (1-100%) to simulate real-world mining conditions</li>
              <li><strong>Network Hashrate:</strong> Define the mining speed units (hash/s, Khash/s, Mhash/s, Ghash/s, Thash/s, etc.)</li>
              <li><strong>Hashrate Amount:</strong> Specify the actual mining power/speed for each miner</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>🔧 Miner Management Features</strong></h6>
            <ul class="right">
              <li><strong>Status Control:</strong> Activate or deactivate miners based on maintenance or market conditions</li>
              <li><strong>Multi-Currency Support:</strong> Create specialized miners for different cryptocurrencies</li>
              <li><strong>Scalability:</strong> Add unlimited miners to support growing user demand</li>
              <li><strong>Real-time Updates:</strong> Modify miner settings without affecting ongoing mining operations</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>📊 Miner Specifications</strong></h6>
            <ul class="right">
              <li><strong>Coin Integration:</strong> Each miner is linked to a specific cryptocurrency for targeted mining</li>
              <li><strong>Environmental Impact:</strong> Renewable energy percentage helps users choose eco-friendly options</li>
              <li><strong>Reliability Metrics:</strong> Uptime percentage indicates miner reliability and availability</li>
              <li><strong>Mining Power:</strong> Network hashrate defines the computational power and mining speed</li>
              <li><strong>Performance Indicators:</strong> Clear metrics help users understand mining capabilities</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>🎯 Business Benefits</strong></h6>
            <ul class="right">
              <li><strong>Flexible Mining Options:</strong> Offer different miners for various user preferences and budgets</li>
              <li><strong>Cryptocurrency Diversity:</strong> Support multiple cryptocurrencies through specialized miners</li>
              <li><strong>Transparent Operations:</strong> Provide clear miner specifications for user confidence</li>
              <li><strong>Scalable Infrastructure:</strong> Easily expand mining capacity by adding new miners</li>
              <li><strong>Market Responsiveness:</strong> Quickly adapt to cryptocurrency market changes</li>
            </ul>

            <p class="mt-20">The Miners system provides the foundation for your cryptocurrency mining platform, enabling you to offer diverse mining options across multiple cryptocurrencies while maintaining transparency and flexibility for your users.</p>
          </div>
        </section>

        <section>
          <h4 id="admin-portfolio" class="second-title">Portfolio</h4>
          <div class="right">
            <p>The Portfolio feature is a badge system for sellers based on their transaction volume. It rewards active sellers with special badges and bonuses as they reach different transaction thresholds.</p>
            <ul class="right">
              <li><strong>Level-Based System:</strong> Create different portfolio levels (e.g., Bronze, Silver, Gold) that sellers can achieve based on their transaction volume.</li>
              <li><strong>Transaction Tracking:</strong> The system automatically tracks seller transactions, excluding certain transaction types like Subtract, Refund, and Portfolio Bonus.</li>
              <li><strong>Automatic Rewards:</strong> When sellers reach a new portfolio level, they automatically receive the corresponding badge and bonus amount.</li>
              <li><strong>Customizable Bonuses:</strong> Set specific bonus amounts for each portfolio level to incentivize sellers to increase their sales.</li>
              <li><strong>Visual Badges:</strong> Upload custom icons for each portfolio level to create visually appealing badges that sellers can display on their profiles.</li>
              <li><strong>Detailed Descriptions:</strong> Add comprehensive descriptions for each portfolio level to explain the benefits and requirements.</li>
              <li><strong>Status Management:</strong> Easily activate or deactivate specific portfolio levels as needed.</li>
            </ul>
            <p>The Portfolio system helps motivate sellers to increase their transaction volume by providing visible recognition and financial incentives. It creates a gamified experience that encourages seller engagement and loyalty while rewarding your most active marketplace participants.</p>
          </div>
        </section>

        <section>
          <h4 id="admin-referral" class="second-title">Referral</h4>
          <div class="right">
            <p>The Referral system allows you to implement and manage a comprehensive affiliate program, encouraging users to refer others to your platform in exchange for rewards.</p>
            <ul class="right">
              <li><strong>Referral Program Management:</strong> Create and configure different referral programs with customizable commission rates and reward structures.</li>
              <li><strong>Multi-level Referrals:</strong> Set up multi-tier referral systems where users earn commissions not only from their direct referrals but also from subsequent levels.</li>
              <li><strong>Referral Links:</strong> Generate unique referral links for users to share with potential new members.</li>
              <li><strong>Commission Settings:</strong> Configure commission percentages for different actions (registration, plan purchase, etc.).</li>
              <li><strong>Withdrawal Management:</strong> Manage withdrawal requests from users to claim their referral earnings.</li>
            </ul>
            <p>The Referral system helps grow your user base organically while rewarding existing users for their loyalty and promotion efforts. It creates a win-win situation where both the platform and its users benefit from new member acquisition.</p>
          </div>
        </section>

        <section>
          <h4 id="admin-schemes" class="second-title">Mining Schemes</h4>
          <div class="right">
            <p>Mining Schemes are the core investment plans that users can purchase to start earning cryptocurrency through your platform. Each scheme defines the mining parameters, return schedules, and profitability structure for different types of mining operations.</p>

            <h6 class="mt-20 mb-15"><strong>📋 Scheme Configuration</strong></h6>
            <ul class="right">
              <li><strong>Scheme Naming:</strong> Create descriptive names like "Bitcoin Basic Plan" or "Ethereum Pro Plan"</li>
              <li><strong>Miner Assignment:</strong> Link each scheme to a specific miner for targeted cryptocurrency mining</li>
              <li><strong>Investment Price:</strong> Set the initial investment amount required to purchase the scheme</li>
              <li><strong>Custom Icons:</strong> Upload unique icons to visually distinguish different mining schemes</li>
              <li><strong>Detailed Descriptions:</strong> Provide comprehensive information about each scheme's benefits</li>
              <li><strong>Featured Status:</strong> Mark premium schemes as featured for increased visibility</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>💰 Return Configuration</strong></h6>
            <ul class="right">
              <li><strong>Return Amount Type:</strong> Choose between fixed returns or variable min-max ranges</li>
              <li><strong>Fixed Returns:</strong> Set exact return amounts for predictable earnings</li>
              <li><strong>Variable Returns:</strong> Define minimum and maximum return ranges for dynamic earnings</li>
              <li><strong>Return Period Type:</strong> Configure schemes for limited periods or lifetime returns</li>
              <li><strong>Mining Cycles:</strong> Set the maximum number of mining cycles for period-based schemes</li>
              <li><strong>Maximum Mining Amount:</strong> Define limits on total cryptocurrency that can be mined</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>⏰ Schedule Management</strong></h6>
            <ul class="right">
              <li><strong>Return Period Hours:</strong> Configure how often users receive mining returns (hourly, daily, weekly)</li>
              <li><strong>Custom Schedules:</strong> Create flexible schedules like "Every 24 hours" or "Every 168 hours (weekly)"</li>
              <li><strong>Schedule Integration:</strong> Link schemes to predefined schedules for consistent timing</li>
              <li><strong>Automated Processing:</strong> Returns are automatically processed based on the configured schedule</li>
              <li><strong>Time Zone Support:</strong> Schedules work across different time zones for global users</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>🏖️ Holiday Management</strong></h6>
            <ul class="right">
              <li><strong>Mining Holidays:</strong> Configure specific days when mining returns are paused</li>
              <li><strong>Multiple Holiday Selection:</strong> Choose multiple days like "Saturday, Sunday" for weekends</li>
              <li><strong>Flexible Holiday Options:</strong> Set holidays for any day of the week (Monday through Sunday)</li>
              <li><strong>Automatic Skipping:</strong> The system automatically skips return processing on configured holidays</li>
              <li><strong>Holiday Transparency:</strong> Users can see which days are designated as mining holidays</li>
              <li><strong>Custom Holiday Patterns:</strong> Create unique holiday schedules for different scheme types</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>🔧 Advanced Features</strong></h6>
            <ul class="right">
              <li><strong>Mining Speed Configuration:</strong> Set hash rates from hash/s to Qhash/s for different performance levels</li>
              <li><strong>Speed Amount:</strong> Define the actual mining power associated with each scheme</li>
              <li><strong>Maintenance Fees:</strong> Configure percentage-based or fixed maintenance fees</li>
              <li><strong>Feature Lists:</strong> Add custom features like "Daily Returns", "VIP Support", "Instant Withdrawal"</li>
              <li><strong>Status Management:</strong> Activate or deactivate schemes based on market conditions</li>
            </ul>

            <h6 class="mt-20 mb-15"><strong>📊 Scheme Examples</strong></h6>
            <ul class="right">
              <li><strong>Bitcoin Basic Plan:</strong> $100 investment, fixed $110 return, 2-day period, 24-hour cycles, Sunday holidays</li>
              <li><strong>Bitcoin Pro Plan:</strong> $500 investment, $520-$550 variable returns, lifetime plan, weekend holidays</li>
              <li><strong>Ethereum Starter:</strong> $200 investment, weekly returns, 168-hour cycles, Thursday holidays</li>
              <li><strong>BNB Power Plan:</strong> $500 investment, daily returns, maximum yield features, weekend holidays</li>
            </ul>

            <p class="mt-20">Mining Schemes provide the flexibility to create diverse investment opportunities while maintaining automated schedule and holiday management, ensuring a professional and reliable mining platform for your users.</p>
          </div>
        </section>

        <section>
          <h4 id="admin-gateways" class="second-title">Payment Gateways</h4>
          <div class="right">
            <p>We have integrated 25 payment gateways that can be managed directly from the dashboard, eliminating the
              need for any coding knowledge. This feature ensures a seamless and user-friendly setup process for
              handling payments.</p>
            <ul class="right">
              <li><strong>Activate:</strong> Easily enable the desired payment gateway with a single click.</li>
              <li><strong>Edit:</strong> Click the edit button to access and customize the gateway's configuration
                settings.</li>
              <li><strong>Credentials:</strong> Enter the required credentials, such as API keys or client secrets, and
                save the changes.</li>
              <li><strong>Ready to Use:</strong> Once all configurations are completed accurately, the gateway will be
                fully functional and ready to process payments.</li>
            </ul>
            <p>For example, when you click the edit button for PayPal, you can configure fields such as Logo, Name, Code
              Name, Client ID, Client Secret, App ID, Mode, and Status. By filling in these fields with the correct
              credentials and saving your changes, PayPal will be activated and ready to handle payment transactions
              efficiently.</p>
          </div>
        </section>

        <section>
          <div class="mb-40 pb-40 border-bottom">
            <h3 id="admin-panel" class="first-title">Settings</h3>
          </div>
          <section>
            <h4 id="admin-settings" class="second-title">Site Settings</h4>
            <div class="right">
              <p>The Site Settings section is the central control hub for configuring all core aspects of your OrexCoin cryptocurrency mining platform. This comprehensive settings panel allows administrators to customize branding, security, operations, and user experience without requiring any code modifications.</p>

              <div class="image-border mb-40">
                <img src="images/admin/site-settings.png" alt="Site Settings" class="img-responsive">
              </div>

              <h6 class="mt-20 mb-15"><strong>🌐 Global Settings</strong></h6>
              <ul class="right">
                <li><strong>Site Identity:</strong> Configure site title, tagline, and description to establish your mining platform's brand</li>
                <li><strong>Visual Branding:</strong> Upload custom logo, favicon, and site images to match your brand identity</li>
                <li><strong>Currency Configuration:</strong> Set the primary site currency and symbol for all financial displays</li>
                <li><strong>Timezone Management:</strong> Configure global timezone settings for accurate time displays across regions</li>
                <li><strong>Contact Information:</strong> Set primary contact email, phone, and address for user communications</li>
                <li><strong>Social Media Links:</strong> Add social media profiles for enhanced user engagement</li>
              </ul>

              <h6 class="mt-20 mb-15"><strong>🔐 Permission Settings</strong></h6>
              <ul class="right">
                <li><strong>Email Verification:</strong> Enable/disable mandatory email verification for new user registrations</li>
                <li><strong>KYC Verification:</strong> Control Know Your Customer verification requirements for enhanced security</li>
                <li><strong>User Registration:</strong> Manage user registration permissions and requirements</li>
                <li><strong>Debug Mode:</strong> Enable debug mode for development and troubleshooting purposes</li>
                <li><strong>Access Control:</strong> Define various permission levels for different user types</li>
                <li><strong>Security Protocols:</strong> Configure security measures and access restrictions</li>
              </ul>

              <h6 class="mt-20 mb-15"><strong>💰 Site Bonus & Fee Settings</strong></h6>
              <ul class="right">
                <li><strong>Referral Bonus:</strong> Set bonus amounts for successful user referrals to encourage growth</li>
                <li><strong>Signup Bonus:</strong> Configure welcome bonuses for new user registrations</li>
                <li><strong>Transaction Fees:</strong> Define platform fees for various mining and financial operations</li>
                <li><strong>Withdrawal Fees:</strong> Set fees for cryptocurrency withdrawals and transfers</li>
                <li><strong>Bonus Distribution:</strong> Configure automatic bonus distribution rules and timing</li>
              </ul>

              <h6 class="mt-20 mb-15"><strong>👤 Inactive User Management</strong></h6>
              <ul class="right">
                <li><strong>Account Deactivation:</strong> Enable automatic deactivation of inactive user accounts</li>
                <li><strong>Inactivity Period:</strong> Set the number of days before accounts are considered inactive</li>
                <li><strong>Inactive Account Fees:</strong> Configure fees for maintaining inactive accounts</li>
                <li><strong>Fee Amount:</strong> Set specific fee amounts for inactive account maintenance</li>
                <li><strong>Automated Processing:</strong> Automatic handling of inactive account policies</li>
              </ul>

              <h6 class="mt-20 mb-15"><strong>🔧 Site Maintenance</strong></h6>
              <ul class="right">
                <li><strong>Maintenance Mode:</strong> Enable maintenance mode to temporarily disable public access</li>
                <li><strong>Secret Key Access:</strong> Configure secret keys for admin access during maintenance</li>
                <li><strong>Maintenance Message:</strong> Customize messages displayed to users during maintenance</li>
                <li><strong>Scheduled Maintenance:</strong> Plan and schedule maintenance windows</li>
                <li><strong>Emergency Mode:</strong> Quick activation for urgent maintenance requirements</li>
              </ul>

              <h6 class="mt-20 mb-15"><strong>⚙️ System Settings</strong></h6>
              <ul class="right">
                <li><strong>Session Lifetime:</strong> Configure user session duration for security and performance</li>
                <li><strong>Cache Management:</strong> Control caching settings for optimal platform performance</li>
                <li><strong>Database Optimization:</strong> Configure database connection and performance settings</li>
                <li><strong>File Upload Limits:</strong> Set maximum file sizes for user uploads and documents</li>
                <li><strong>Performance Tuning:</strong> Optimize system performance parameters</li>
              </ul>

              <h6 class="mt-20 mb-15"><strong>🎯 Key Benefits</strong></h6>
              <ul class="right">
                <li><strong>No Code Required:</strong> All settings can be configured through the intuitive web interface</li>
                <li><strong>Real-time Updates:</strong> Changes take effect immediately without requiring system restarts</li>
                <li><strong>Comprehensive Control:</strong> Manage every aspect of your mining platform from one location</li>
                <li><strong>Security Focused:</strong> Built-in security settings to protect your platform and users</li>
                <li><strong>Scalable Configuration:</strong> Settings designed to grow with your mining platform</li>
                <li><strong>User Experience:</strong> Optimize settings for the best possible user experience</li>
              </ul>

              <p class="mt-20">The Site Settings panel provides complete control over your OrexCoin mining platform, allowing you to customize every aspect of the user experience while maintaining security and performance standards.</p>
            </div>
          </section>

          <section>
            <h4 id="admin-plugins" class="second-title">Plugins</h4>
            <div class="right">
              <p>We have integrated essential plugins to enhance the functionality of your site. These plugins give you
                full control and can be easily configured to meet your specific needs.</p>
              <ul class="right">
                <li><strong><a href="https://www.tawk.to/" target="_blank">Tawk Chat:</a></strong> A live chat platform
                  that enables seamless real-time communication with your users, improving customer support and
                  engagement.</li>
                <li><strong><a href="https://www.google.com/recaptcha/admin/create" target="_blank">Google
                      reCaptcha:</a></strong> A security feature that helps prevent automated or bot-generated requests,
                  ensuring the safety and integrity of your system.</li>
                <li><strong><a href="https://analytics.google.com/analytics/web/provision/#/provision"
                      target="_blank">Google Analytics:</a></strong> Provides comprehensive insights into your site’s
                  visitor traffic, user behavior, and performance metrics, helping you make informed decisions.</li>
                <li><strong><a href="https://developers.facebook.com/docs/messenger-platform/" target="_blank">Facebook
                      Messenger:</a></strong> A messaging service that enables you to interact with users in real-time
                  via Facebook Messenger, enhancing communication and customer support.</li>
              </ul>
            </div>
          </section>

          <section>
            <h4 id="admin-language" class="second-title">Language</h4>
            <div class="right">
              <p>The multilingual feature allows you to localize your site and make it more user-friendly. You can add
                any language, enabling users to view the site’s content in their native language.
                Providing content in the user’s preferred language enhances engagement and creates a more interactive
                experience.</p>
            </div>
          </section>
          <section>
            <h4 id="admin-custom_css" class="second-title">Custom CSS</h4>
            <div class="right">
              <p>This feature allows you to add custom CSS styles to your site effortlessly, without the need to modify
                or directly interact with the existing codebase.
                It provides a convenient way to tweak the visual appearance of your site, ensuring a unique and
                personalized design. You can use this functionality to override default styles,
                improve responsiveness, or introduce creative elements to align with your brand identity.</p>
            </div>
          </section>

          <div class="mb-40 pb-40 border-bottom">
            <h3 id="admin-panel" class="first-title">Site Content Manage</h3>
          </div>
          <section>
            <section>
              <h4 id="admin-pages" class="second-title">Site Content</h4>
              <div class="right">
                <p>You can easily manage all sections, pages, and themes directly from the dashboard. In sections, you
                  have the flexibility to customize
                  content such as images, button labels, icons, text, and other elements, giving you full control over
                  your site's layout and functionality.
                  Similarly, in pages, you can modify page-specific content like banners, images, buttons, and text to
                  ensure the information is up-to-date and
                  aligns with your branding. For themes, you can activate or deactivate your preferred design with a
                  single click, allowing you to refresh the
                  look and feel of your site effortlessly. This streamlined approach ensures that you can maintain a
                  professional and engaging website without
                  requiring any coding knowledge.</p>

              </div>
            </section>
            <section>
              <h4 id="admin-section" class="second-title">Dynamic Section</h4>
              <div class="right">
                <p>All site content is fully customizable without requiring any modifications to the code or database.
                  You have complete control over each section,
                  allowing you to tailor the content to meet your specific requirements. Under the "Landing Page" menu,
                  all sections are conveniently accessible,
                  enabling you to update images, text, buttons, and other elements effortlessly. This functionality
                  ensures a seamless and user-friendly experience,
                  empowering you to keep your site dynamic and aligned with your vision without any technical expertise.
                </p>

              </div>
            </section>
            <section>
              <h4 id="admin-page" class="second-title">Dynamic Page</h4>
              <div class="right">
                <p>Similar to sections, all page content is fully customizable, allowing you to update images, text,
                  buttons, and other elements with ease.
                  You have the flexibility to activate or deactivate pages as needed, ensuring that your site remains
                  relevant and up-to-date. Additionally,
                  you can create new pages effortlessly without writing any code, making it simple to expand your site
                  as your needs evolve. This feature empowers
                  you to maintain complete control over your site's structure and content, all through an intuitive and
                  user-friendly interface.</p>

              </div>
            </section>

            <section>
              <h4 id="admin-theme" class="second-title">Dynamic Theme</h4>
              <div class="right">
                <p>In Theme Management, the admin can activate any available theme with just a few clicks. Additionally,
                  you can upload any HTML template to customize the design of your site.
                  While uploading a new template, it is essential to include <code>@asset('landing asset')</code> for
                  all the assets to ensure they are correctly linked and functional.
                  This feature provides flexibility to update the site's appearance without extensive technical
                  knowledge, allowing you to maintain a fresh and engaging user interface. Moreover,
                  switching between themes is seamless, ensuring minimal downtime for your site.</p>
              </div>
            </section>

            <div class="mb-40 pb-40 border-bottom">
              <h3 id="user-dashboard" class="first-title">User Panel</h3>
            </div>

            <section>
              <h4 id="user-dashboard" class="second-title">Dashboard</h4>
              <div class="right">
                <p>The user dashboard provides a comprehensive overview of all essential information. Here, users can
                  view their product listings, orders, earnings, and payment details. Additionally, users can access
                  detailed seller & buyer analytics, including:</p>
                <ul>
                  <li>Total views of their products</li>
                  <li>Total orders received (for seller)</li>
                  <li>Revenue generated (for seller) </li>
                  <li>Purchased (for buyer) </li>
                  <li>And More...</li>

                </ul>
                <p>This feature allows sellers to track their performance over time and make data-driven decisions to
                  improve their sales and visibility on the platform.</p>
                <div class="row">
                  <div class="col-lg-6">
                    <div class="image-border">
                      <img src="images/user/dashboard.png" alt="" class="img-responsive" alt="">
                    </div>
                  </div>
                </div>
              </div>
              <br></br>

              <ul class="right">
                <li><strong>Products:</strong> Users can browse all available digital products added by sellers. By
                  clicking on a specific product,
                  users can view detailed information such as description, price, discount, and quantity. From
                  this page, users can easily proceed to purchase the product by clicking the "Buy Now" button.</li>
                <br></br>
                <li id="user-checkout"><strong>Checkout:</strong> In the checkout process, users need to review their
                  cart items, then
                  select a payment method. Users can also apply discount codes
                  if available.</li>
                <br></br>
                <div class="row">
                  <div class="col-lg-6">
                    <div class="image-border">
                      <img src="images/user/checkout.png" alt="" class="img-responsive" alt="">
                    </div>
                  </div>
                </div>
                <br></br>
                <li><strong>Topup:</strong> Users have access to a digital wallet where they can manage their funds.
                  They can view their balance,
                  add funds through various payment methods, and use the top-up balance for purchases.</li>
                <br></br>
                <li id="user-payment"><strong>Payment:</strong> After the total amount is calculated, users will proceed
                  directly to payment. They will be prompted to choose a payment gateway to complete the transaction.
                  The system supports both direct payments and manual payments. For direct payments, the transaction is
                  immediately confirmed. For manual payments, the admin can add payment options that require
                  verification. In this case, the purchase will be pending until the admin verifies and approves the
                  payment, ensuring flexibility in payment methods while maintaining control over transactions.</li>
                <br></br>
                <div class="row">
                  <div class="col-lg-6">
                    <div class="image-border">
                      <img src="images/user/payment.png" alt="" class="img-responsive" alt="">
                    </div>
                  </div>
                </div>
              </ul>
            </section>

            <h4 id="user-listing" class="second-title">Listing</h4>

            <div class="right">
              <ul>
                <li><strong>Coupon Management:</strong> Create, edit, and manage discount coupons for products. Set coupon codes, discount amounts, expiration dates, and usage limits.</li>

                <li><strong>Product Listings:</strong> Add new products, edit existing ones, and manage their details such
                  as name, description, price, and images. You can also set discounts and manage inventory.
                  <h6 class="second-title ml-4"><strong>How to Add a Listing:</strong></h6>
                  <ul class="">
                    <li>Navigate to the "<strong>Add Listing</strong>" section in the admin panel.</li>
                    <li>Choose the appropriate category for your product.</li>
                    <li>Fill in the product details, including name, description, and price.</li>
                    <li>Upload high-quality images of the product.</li>
                    <li>Set any applicable discounts.</li>
                    <li>Specify the quantity available for sale.</li>
                    <li>Click "Submit" to add the listing to your marketplace.</li>
                    <li>Now Select delivery method. If you select auto delivery method, then you have to add delivery
                      items. If you select manual delivery method, then you don't have to add delivery items.</li>
                    <li>Review all information for accuracy before submitting.</li>
                  </ul>

                </li>
              </ul>
            </div>


            <h4 id="userChangePassword" class="second-title">Privacy & Security</h4>
            <div class="right">
              <p>Users have access to various security features to enhance their account protection and manage their
                privacy settings:</p>
              <ul>
                <li><strong>Two-Factor Authentication (2FA):</strong> Users can enable 2FA for an additional layer of
                  security.</li>
                <li><strong>Email Change with Verification:</strong> Users can update their email address with a
                  verification process to ensure account security.</li>
                <li><strong>Password Change:</strong> Users can change their password regularly to maintain account
                  security.</li>
                <li><strong>Show Follower and Following List:</strong> Users can manage the visibility of their follower
                  and following lists.</li>
                <li><strong>Accept Chat from Listing Details Page:</strong> Users can control who can initiate chat with
                  them from product listing pages.</li>
              </ul>
              <div class="row">
                <div class="col-lg-6">
                  <div class="image-border">
                    <img src="images/user/security-settings.png" alt="" class="img-responsive" alt="">
                  </div>
                </div>
              </div>
            </div>

            <!-- ============== Big Title Section ============================ -->
            <div class="mb-40 pb-40">
              <h3 id="release-log" class="first-title">Thank you</h3>
              <p>
                Again, thank you for purchasing OreXcoin. If you need some help, or
                support please use email <a href="mailto:<EMAIL>"><EMAIL></a> or contact form
                via
                codecanyon
                <a href="https://codecanyon.net/user/tdevs" target="_blank">Profile site</a>
                <br />
                Hope you happy with the OreXcoin, all the best with your business.
              </p>

              <h3 class="second-title">Release Logs</h3>
              <div class="code-block">
                Version 1.0 (05 Jul, 2025)
              </div>

              <p>
                &copy;
                <script>
                  var d = new Date();
                  document.write(d.getFullYear());
                </script>
                <a href="https://codecanyon.net/user/tdevs"> Tdevs</a>
              </p>
            </div>
      </div>

    </section>
  </section>
  <script src="assets/js/jquery.2.2.4.min.js"></script>
  <script src="assets/js/jquery.nicescroll.min.js"></script>
  <script src="assets/js/bootstrap.min.js"></script>
  <script src="assets/js/custom.js"></script>
</body>

</html>