<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('customer-mail-send')): ?>
    <button type="button" data-id="<?php echo e($user->id); ?>" data-name="<?php echo e($user->first_name . ' ' . $user->last_name); ?>"
        class="send-mail round-icon-btn blue-btn" data-bs-toggle="tooltip" data-bs-original-title="<?php echo e(__('Send Email')); ?>">
        <i data-lucide="mail"></i>
    </button>
<?php endif; ?>
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['customer-basic-manage', 'customer-balance-add-or-subtract', 'customer-change-password', 'all-type-status'])): ?>
    <a href="<?php echo e(route('admin.user.edit', $user->id)); ?>" class="round-icon-btn primary-btn" data-bs-toggle="tooltip"
        data-bs-original-title="<?php echo e(__('Edit User')); ?>"><i data-lucide="edit-3"></i>
    </a>
    <button type="button" class="round-icon-btn red-btn" id="deleteModal" data-id="<?php echo e($user->id); ?>"
        data-name="<?php echo e($user->name); ?>" data-bs-toggle="tooltip" data-bs-original-title="<?php echo e(__('Delete User')); ?>">
        <i data-lucide="trash-2"></i>
    </button>
<?php endif; ?>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/user/include/__action.blade.php ENDPATH**/ ?>