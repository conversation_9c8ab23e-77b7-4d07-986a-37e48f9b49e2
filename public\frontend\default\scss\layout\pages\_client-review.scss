@use '../../utils' as *;

/*----------------------------------------*/
/* Client review styles
/*----------------------------------------*/

.our-client-glow {
    .glow-one {
        position: absolute;
        top: -48px;
        inset-inline-start: 50%;
        transform: translateX(-50%);
        z-index: -1;

        @include rtl {
            inset-inline-start: auto;
            inset-inline-end: 50%;
        }
    }
}

// swiper pagination
.bd-pagination {
    @include flexbox();
    gap: 10px;

    .swiper-pagination-bullet {
        width: 7px;
        height: 7px;
        border-radius: 0;
        margin: 0 !important;
        background-color: #CCE2FA;
        opacity: 1;
        border-radius: 10px;
        transition: .3s;
    }

    .swiper-pagination-bullet-active {
        background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
        width: 19px;
    }
}

.client-review-item {
    position: relative;
    padding: 1px;
    display: block;


    &::before {
        position: absolute;
        inset-inline-start: 0;
        top: 0;
        transition: all .3s;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
        opacity: 0.6;
        content: "";
        clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
        border-radius: 2px;
    }

    .clip-path {
        position: relative;
        z-index: 3;
        background: var(--td-white);
        clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
        gap: 12px;
        width: 100%;

        @include dark-theme {
            background: #04060a;
        }

        &::before {
            position: absolute;
            top: 0;
            inset-inline-start: 0;
            content: "";
            background: linear-gradient(90deg, rgba(71, 118, 230, 0.1) 81.54%, rgba(142, 84, 233, 0.1) 118.26%);
            z-index: -1;
            width: 100%;
            height: 100%;

            @include dark-theme {
                background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) 81.54%, rgba(142, 84, 233, 0.2) 118.26%);
            }
        }
    }

    .clip-inner {
        padding: 32px 32px;

        @media #{$xxs} {
            padding: 24px 24px;
        }
    }

    .review-contents {
        margin-bottom: 30px;
    }

}

.admin-item {
    display: flex;
    align-items: center;
    gap: 16px;

    .admin-thumbnail {
        img {
            width: 50px;
            height: 50px;
        }
    }

    .admin-info {
        .admin-name {
            font-size: 20px;
        }

        .admin-designation {
            font-size: 16px;
            margin-top: 2px;
            display: block;
        }
    }
}