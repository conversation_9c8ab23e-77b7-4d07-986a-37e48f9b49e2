<?php
    $hasPermission = match ($navigation->route) {
        'user.addMoney' => (bool) setting('user_deposit', 'permission'),
        'user.withdrawMoney.index' => (bool) setting('user_withdraw', 'permission'),
        'user.tickets' => (bool) setting('user_ticket', 'permission'),
        'user.referral' => (bool) setting('sign_up_referral', 'permission'),
        default => true,
    };
?>
<?php if($hasPermission): ?>
<li class="slide <?php echo e(isActive($navigation->route)); ?>">
    <div class="clip-path">
       <div class="clip-path-inner">
          <a href="<?php echo e(route($navigation->route)); ?>" class="sidebar-menu-item <?php echo e(str($navigation->route)->contains('logout') ? 'has-logout' : ''); ?>">
             <div class="side-menu-icon">
                <iconify-icon icon="<?php echo e($navigation->icon); ?>"></iconify-icon>                                                                                        
             </div>
             <span class="sidebar-menu-label"><?php echo e(__($navigation->name)); ?></span>
          </a>
       </div>
    </div>
 </li>
<?php endif; ?>
<?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/_include/_menu-item.blade.php ENDPATH**/ ?>