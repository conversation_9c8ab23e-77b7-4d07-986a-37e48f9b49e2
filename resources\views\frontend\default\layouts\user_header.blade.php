@use('App\Models\Language')
@php
   $user = Auth::user();
   $notifications = App\Models\Notification::query()
      ->where('for', 'user')
      ->where('user_id', $user->id)
      ->latest()
      ->get();
   $totalUnreadCount = App\Models\Notification::query()
      ->where('for', 'user')
      ->where('user_id', $user->id)
      ->where('read', 0)
      ->count();
@endphp

<div class="app-page-header">
   <div class="app-dashboard-header">
      <div class="left-contents">
         <div class="user-welcome-info">
            <button class="toggle-sidebar d-xl-none">
               <span class="bar-icon">
                  <svg width="7" height="10" viewBox="0 0 7 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                     <path d="M5.25977 8.53022L1.73977 5.00022L5.25977 1.47021" fill="white" />
                     <path d="M5.25977 8.53022L1.73977 5.00021L5.25977 1.47021" stroke="#171717" stroke-width="1.8"
                        stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
               </span>
            </button>
            <h3 class="welcome-text">{{ __('Welcome in') }}, {{ setting('site_title', 'global') }}</h3>
         </div>
      </div>
      <div class="right-contents">
         <div class="header-right">
            <div class="header-quick-actions d-flex align-items-center">
               <x-theme-lang-switcher />
               <div class="others-actions">
                  <!-- Notification dropdown -->
                  <div class="notification-panel-box">
                     <div class="quick-action-item">
                        <button type="button" class="action-icon notification-btn {{ $totalUnreadCount > 0 ? 'active' : '' }}">
                           <iconify-icon icon="tabler:bell">
                        </button>
                     </div>

                     <!-- Notification content here -->
                     <!-- Notification content here -->
                     <div class="notification-panel">
                        <div class="notification-header">
                           <div class="heading-top">
                              <h3 class="title">{{ __('Notifications') }}</h3>
                              <a href="{{ route('user.notification.all') }}"
                                 class="td-btn btn-xs btn-chip grd-fill-btn-primary">
                                 <span class="btn-text">{{ __('All') }}</span>
                              </a>
                           </div>
                           <div class="notifications-middle">
                              <div class="middle-buttons">
                                 <button type="button" class="notification-btn">
                                    <a href="{{ route('user.notification.all') }}" class="text">{{ __('View All') }}</a>
                                    <span class="count">{{ $totalUnreadCount }}</span>
                                 </button>
                              </div>
                              @if ($totalUnreadCount > 0)
                              <button type="button" class="notification-btn">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:checks"></iconify-icon>
                                 </span>
                                 <a href="{{ route('user.read-notification', 0) }}"
                                    class="text">{{ __('Mark all as read') }} </a>
                              </button>
                              @endif
                           </div>
                        </div>
                        <div class="notifications-inner">
                           <div class="notifications-lists">
                              @forelse ($notifications as $notification)
                                 <a href="{{ route('user.read-notification', $notification->id) }}">
                                    <div class="notification-list">
                                       <div class="notification-item">
                                          <div class="icon">
                                          <span class="icon"><iconify-icon icon="tabler:bell"></iconify-icon></span>
                                          </div>
                                          <div class="contents">
                                          <h5 class="title">{{ $notification->title }}</h5>
                                          <p class="message">{{ $notification->message }}</p>
                                          <small class="time">{{ $notification->created_at->diffForHumans() }}</small>
                                          </div>
                                       </div>
                                    </div>
                                 </a>
                                 @empty
                                 <x-no-data-found class="mt-10" module="{{ __('New Notification') }}" />
                              @endforelse
                           </div>
                        </div>
                     </div>
                  </div>

                  <!-- User Profile -->
                  <!-- User Profile -->
                  <div class="user-profile-drop">
                     <div class="quick-action-item">
                        <button type="button" class="action-icon">
                           <iconify-icon icon="tabler:user-circle"></iconify-icon>
                        </button>
                     </div>
                     <div class="dropdown-menu">
                        <div class="profile-info-wrapper">
                           <div class="profile-info-list">
                              <a class="profile-info-item" href="{{ route('user.setting.index') }}">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:user"></iconify-icon>
                                 </span>
                                 <span class="text">{{ __('Profile Setting') }}</span>
                              </a>
                              <a class="profile-info-item" href="{{ route('user.setting.index', ['type' => 'password']) }}">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                 </span>
                                 <span class="text">{{ __('Change Password') }}</span>
                              </a>
                              @if (setting('fa_verification', 'permission'))
                              <a class="profile-info-item" href="{{ route('user.setting.index',['type' => 'security']) }}">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:shield-lock"></iconify-icon>
                                 </span>
                                 <span class="text">{{ __('Security Setting') }}</span>
                              </a>
                              @endif
                              <a class="profile-info-item" href="{{ route('user.tickets') }}">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:headset"></iconify-icon>
                                 </span>
                                 <span class="text">{{ __('Support Tickets') }}</span>
                              </a>
                              <a class="profile-info-item profile-log-out" href="{{ route('logout') }}">
                                 <span class="icon">
                                    <iconify-icon icon="tabler:lock"></iconify-icon>
                                 </span>
                                 <span class="text">{{ __('Log Out') }}</span>
                              </a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
</div>