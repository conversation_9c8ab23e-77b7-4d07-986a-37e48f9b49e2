<?php
    $moduleName = $attributes->get('module', 'Item');
    $deleteRoute = $attributes->get('delete-route', 'admin.miner.delete');
    $formMethod = $attributes->get('method', 'post');
?>

<span type="button" id="deleteModal">
    <button class="round-icon-btn red-btn deleteData" data-bs-toggle="tooltip" data-routeUrl="<?php echo e($deleteRoute); ?>"
        title="<?php echo e(__('Delete ' . $moduleName)); ?>" data-bs-original-title="<?php echo e(__('Delete ' . $moduleName)); ?>">
        <i data-lucide="trash-2"></i>
    </button>
</span>

<?php $__env->startSection('script'); ?>
    <div class="modal fade" id="deletemodule" tabindex="-1" aria-labelledby="deleteModuleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md modal-dialog-centered">
            <div class="modal-content site-table-modal">
                <div class="modal-body popup-body">
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    <div class="popup-body-text centered">
                        <div class="info-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                data-lucide="alert-triangle" icon-name="alert-triangle"
                                class="lucide lucide-alert-triangle">
                                <path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3"></path>
                                <path d="M12 9v4"></path>
                                <path d="M12 17h.01"></path>
                            </svg>
                        </div>
                        <div class="title">
                            <h4><?php echo e(__('Are you sure?')); ?></h4>
                        </div>
                        <p>
                            <?php echo e(__('You want to delete this ' . $moduleName . '?')); ?>

                        </p>
                        <div class="action-btns">
                            <form id="deletemoduleForm" method="<?php echo e(!in_array(strtolower($formMethod), ['post', 'get']) ? 'post' : $formMethod); ?>">
                                <?php echo csrf_field(); ?>
                                <?php if($formMethod == 'delete'): ?>
                                    <?php echo method_field($formMethod); ?>
                                <?php endif; ?>
                                <button type="submit" class="site-btn-sm primary-btn me-2">
                                    <i icon-name="check"></i>
                                    <?php echo e(__('Confirm')); ?>

                                </button>
                                <a href="javascript:void(0)" class="site-btn-sm red-btn" type="button"
                                    data-bs-dismiss="modal" aria-label="Close">
                                    <i icon-name="x"></i>
                                    <?php echo e(__('Cancel')); ?>

                                </a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $(".deleteData").on('click', function () {
            var routeurl = $(this).data('routeurl');
            $('#deletemoduleForm').attr('action', routeurl);
            $("#deletemodule").modal('show');
        });
    </script>
<?php $__env->stopSection(); ?><?php /**PATH E:\laragon\www\orexcoin\resources\views/components/admin/delete-module-popup.blade.php ENDPATH**/ ?>