@use '../../utils' as *;

/*----------------------------------------*/
/* Referral tree styles
/*----------------------------------------*/
.tree {
    .tree-item {
        position: relative;
        margin-bottom: 5px;
        margin-top: 5px;

        &:before {
            content: "";
            position: absolute;
            top: 16px;
            inset-inline-start: -20px;
            width: 20px;
            height: 1px;
            background-color: #334166;
        }

        &:first-child {
            &:before {
                top: 16px;
            }
        }

        &:not(.parent) {
            .tree-content {
                padding-inline-start: 16px;
            }
        }
    }

    .tree-item.parent {
        cursor: pointer;
    }

    .tree-content {
        padding: 2px 0;
        display: flex;
        align-items: center;
    }

    .toggle-icon {
        display: inline-block;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        margin-inline-end: 8px;

        img {
            vertical-align: middle;
        }
    }

    .tree-children {
        position: relative;
        padding-inline-start: 20px;
        margin-inline-start: 10px;
        border-inline-start: 1px solid #334166;
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .tree-item.root {
        &:before {
            display: none;
        }
    }

    .tree-item.collapsed {
        .tree-children {
            display: none;
        }
    }

    .name {
        color: var(--td-text-primary);
        font-size: 14px;

        &.main-parent {
            font-size: 24px;
            line-height: 1;
            font-weight: 700;
            color: var(--td-white);

            @media #{$xs} {
                font-size: 20px;
            }

            @media #{$xxs} {
                font-size: 18px;
            }
        }
    }
}