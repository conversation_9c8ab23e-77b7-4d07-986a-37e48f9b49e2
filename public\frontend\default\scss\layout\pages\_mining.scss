@use '../../utils' as *;

/*----------------------------------------*/
/* Mining styles
/*----------------------------------------*/
.history-table-wrapper {
    margin: 0 auto;
    overflow: hidden;
    padding-bottom: 30px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -31.74%, rgba(9, 70, 255, 0.06) 88.89%);
    border-radius: 30px;
    position: relative;
    z-index: 1;

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        padding: 1px;
        background: linear-gradient(180deg, rgba($heading, $alpha: 0.16) 0%, rgba($heading, $alpha: 0.16) 100%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        border-radius: 30px;
        z-index: -1;

        @include dark-theme {
            background: linear-gradient(180deg, rgba(11, 39, 122, 1) 0%, rgba(0, 148, 255, 1) 100%);
        }
    }

    .header {
        padding: 16px 30px;
        color: var(--td-heading);
        font-weight: bold;
        font-size: 18px;
        background: rgba(255, 255, 255, 0.08);

        @include dark-theme {
            color: var(--td-heading);
        }
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 18px 30px;
        text-align: start;
        border-bottom: 1px solid rgba($heading, $alpha: 0.16);
        color: #8d8fa5;

        @include dark-theme {
            border-color: rgba($white, $alpha: 0.16);
        }
    }

    td {
        font-weight: 400;

        &:last-child {
            text-align: end;
        }
    }

    .button-wrapper {
        padding: 0 30px;
        display: flex;
        align-items: center;
        gap: 16px;
        flex-wrap: wrap;
        margin-top: 30px;
    }
}

.mining-history-details-grid {
    display: grid;
    grid-template-columns: 554px 1fr;

    @media #{$xxl,$xl} {
        grid-template-columns: 500px 1fr;
    }

    @media #{$lg} {
        grid-template-columns: 430px 1fr;
    }

    @media #{$xs,$sm,$md} {
        grid-template-columns: 1fr;
    }
}

.mining-history-card-wrapper {
    display: flex;
    padding: 30px 30px;
    align-items: center;
    justify-content: center;

    @media #{$xxs} {
        padding: 30px 16px;
    }
}

.mining-history-card {
    position: relative;
    border-radius: 40px 30px 30px;
    padding: 40px 30px 30px;
    width: 345px;
    margin: 1px;
    margin-top: 22px;
    background: #F4EEFD;
    z-index: 2;

    &::before {
        position: absolute;
        content: "";
        inset: 0;
        border-radius: 40px 30px 30px;
        padding: 1px;
        background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
        -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
        -webkit-mask-composite: xor;
        mask-composite: exclude;
        z-index: -1;
    }

    @include dark-theme {
        background-color: #16213F;
        box-shadow: 0px 8px 30px 0px #C840B7 inset;
        backdrop-filter: blur(19.5px);
    }

    @media #{$xxs} {
        width: 100%;
    }

    .card-amount {
        text-align: center;

        .amount {
            font-weight: 800;
            font-size: 24px;
        }

        .change {
            font-size: 16px;
            color: #A28DFF;
            text-align: center;
            margin-top: 8px;
            font-weight: 500;
        }
    }

    .stats-grid {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-top: 20px;

        .stat-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: rgba($heading, $alpha: 0.6);

            @include dark-theme {
                color: #9A9DA7;
            }

            .stat-name {
                font-weight: 800;
            }

            .stat-label,
            .stat-value {
                font-size: 16px;
                font-weight: 600;
                color: rgba($heading, $alpha: 0.6);

                @include dark-theme {
                    color: #9A9DA7;
                }
            }
        }
    }

    .btn-inner {
        text-align: center;
        mask-type: 20px;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px dashed rgba($heading, $alpha: 0.16);

        @include dark-theme {
            border-color: rgba($white, $alpha: 0.16);
        }

    }

    .card-badge {
        position: absolute;
        bottom: calc(100% - 20px);
        left: 50%;
        transform: translateX(-50%);

        .clip-badge {
            color: var(--td-heading);
        }
    }
}

.mining-history-area {
    border-inline-start: 1px solid rgba($heading, $alpha: 0.16);

    @include dark-theme {
        border-color: rgba($white, $alpha: 0.16);
    }

    .history-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
        padding-top: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid rgba($heading, $alpha: 0.16);
        padding-left: 30px;
        padding-right: 30px;

        &:last-child {
            border-bottom: 0;
        }

        .history-label {
            font-family: var(--td-ff-heading);
            font-size: 16px;
            font-weight: 800;
            flex: 1;
        }

        @include dark-theme {
            border-color: rgba($white, $alpha: 0.16);
        }
    }
}