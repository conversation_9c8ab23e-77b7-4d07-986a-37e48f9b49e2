<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="55337a7e-5b7e-44e0-b12e-9d7a5184f466" name="Default Changelist" comment="" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FUSProjectUsageTrigger">
    <session id="2071976293">
      <usages-collector id="statistics.lifecycle.project">
        <counts>
          <entry key="project.closed" value="2" />
          <entry key="project.open.time.0" value="1" />
          <entry key="project.open.time.4" value="1" />
          <entry key="project.opened" value="2" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.open">
        <counts>
          <entry key="html" value="1" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.open">
        <counts>
          <entry key="HTML" value="1" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.extensions.edit">
        <counts>
          <entry key="html" value="348" />
        </counts>
      </usages-collector>
      <usages-collector id="statistics.file.types.edit">
        <counts>
          <entry key="HTML" value="348" />
        </counts>
      </usages-collector>
    </session>
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/index.html">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="220">
              <caret line="94" lean-forward="true" selection-start-line="94" selection-end-line="94" />
              <folding>
                <element signature="n#section#0;n#se#0;n#body#0;n#html#0;n#!!top" />
                <element signature="e#4727#4920#0" />
                <element signature="e#4999#5933#0" />
                <element signature="e#6012#7458#0" />
                <element signature="e#7537#7885#0" />
                <element signature="e#7964#8904#0" />
                <element signature="e#8993#9121#0" />
                <element signature="e#9200#10928#0" />
                <element signature="e#9669#10848#0" />
                <element signature="e#11088#11210#0" />
                <element signature="e#11219#12182#0" />
                <element signature="n#div#5;n#div#0;n#section#0;n#section#0;n#body#0;n#html#0;n#!!top" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <replaceStrings>
      <replace />
    </replaceStrings>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/index.html" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="227" />
    <option name="y" value="154" />
    <option name="width" value="1400" />
    <option name="height" value="1000" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="Documentation" type="b2602c69:ProjectViewProjectNode" />
              <item name="Documentation" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="C:/laragon/www/digibank" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="55337a7e-5b7e-44e0-b12e-9d7a5184f466" name="Default Changelist" comment="" />
      <created>*************</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>*************</updated>
      <workItem from="1737892398633" duration="2285000" />
      <workItem from="1737949965556" duration="8896000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="11181000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1936" height="1048" extended-state="6" />
    <editor active="true" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.13205537" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Database Changes" order="8" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="9" show_stripe_button="false" />
      <window_info anchor="bottom" id="Terminal" order="10" />
      <window_info anchor="bottom" id="Event Log" order="11" side_tool="true" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Database" order="3" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="**********" />
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/index.html">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="220">
          <caret line="94" lean-forward="true" selection-start-line="94" selection-end-line="94" />
          <folding>
            <element signature="n#section#0;n#se#0;n#body#0;n#html#0;n#!!top" />
            <element signature="e#4727#4920#0" />
            <element signature="e#4999#5933#0" />
            <element signature="e#6012#7458#0" />
            <element signature="e#7537#7885#0" />
            <element signature="e#7964#8904#0" />
            <element signature="e#8993#9121#0" />
            <element signature="e#9200#10928#0" />
            <element signature="e#9669#10848#0" />
            <element signature="e#11088#11210#0" />
            <element signature="e#11219#12182#0" />
            <element signature="n#div#5;n#div#0;n#section#0;n#section#0;n#body#0;n#html#0;n#!!top" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
</project>