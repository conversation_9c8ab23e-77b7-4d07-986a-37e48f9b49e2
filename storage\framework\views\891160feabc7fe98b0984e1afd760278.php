<?php if(request('tab') == 'ticket'): ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['tab-pane fade', 'show active' => request('tab') == 'ticket']); ?>" id="pills-ticket" role="tabpanel" aria-labelledby="pills-ticket-tab">
        <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="site-card">
                    <div class="site-card-header">
                        <h4 class="title"><?php echo e(__('Support Tickets')); ?></h4>
                    </div>
                    <div class="site-card-body table-responsive">
                        <div class="site-table">
                            <div class="table-filter">
                                <form action="" method="get">
                                    <input type="hidden" name="tab" value="ticket">
                                    <div class="filter d-flex">
                                        <div class="search">
                                            <label for=""><?php echo e(__('Search:')); ?></label>
                                            <input type="text" name="query" value="<?php echo e(request('query')); ?>" />
                                        </div>
                                        <button class="apply-btn" type="submit"><i
                                                data-lucide="search"></i><?php echo e(__('Search')); ?></button>
                                    </div>
                                </form>
                            </div>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => 'Ticket Name',
                                            'field' => 'title',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => 'Opening Date',
                                            'field' => 'created_at',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => 'Status',
                                            'field' => 'status',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <th><?php echo e(__('Action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $tickets; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $ticket): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td>
                                                <?php echo $__env->make('backend.ticket.include.__name', [
                                                    'title' => $ticket->title,
                                                    'uuid' => $ticket->uuid,
                                                    'user_id' => $ticket->user_id,
                                                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                            </td>
                                            <td><?php echo e($ticket->created_at); ?></td>
                                            <td>
                                                <?php echo $__env->make('backend.ticket.include.__status', [
                                                    'status' => $ticket->status,
                                                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                            </td>
                                            <td>
                                                <?php echo $__env->make('backend.ticket.include.__action', [
                                                    'uuid' => $ticket->uuid,
                                                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <td colspan="7" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                            <?php echo e($tickets->links()); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/user/include/__ticket.blade.php ENDPATH**/ ?>