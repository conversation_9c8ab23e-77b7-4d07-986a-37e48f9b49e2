<?php if($kyc->status == 'approved'): ?>
    <p>
        <strong>
            <?php echo e(__('Status:')); ?>

        </strong>
        <span class="app-badge badge-success">
            <?php echo e(__('Approved')); ?>

        </span>
    </p>
<?php elseif($kyc->status == 'rejected'): ?>
    <p>
        <strong>
            <?php echo e(__('Status:')); ?>

        </strong>
        <span class="app-badge badge-danger">
            <?php echo e(__('Rejected')); ?>

        </span>
    </p>
<?php elseif($kyc->status == 'pending'): ?>
    <p>
        <strong>
            <?php echo e(__('Status:')); ?>

        </strong>
        <span class="app-badge badge-warning">
            <?php echo e(__('Pending')); ?>

        </span>
    </p>
<?php endif; ?>
<p>
    <strong>
        <?php echo e(__('Message from admin:')); ?>

    </strong>
    <span class="text-highlight">
        <?php echo e($kyc->message); ?>

    </span>
</p>

<?php $__currentLoopData = $kyc->data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <p>
        <strong><?php echo e($key); ?>:</strong>
        <span class="text-highlight">
            <?php if(is_string($value) && preg_match('/\.(jpg|jpeg|png|gif|bmp)$/i', $value)): ?>
                <a href="<?php echo e(asset($value)); ?>" target="_blank" rel="noopener noreferrer"><?php echo e(__('Click to view')); ?></a>
            <?php else: ?>
                <?php echo e($value); ?>

            <?php endif; ?>
        </span>
    </p>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php /**PATH E:\laragon\www\orexcoin\app\Providers/../../resources/views/frontend/default/user/kyc/include/__kyc_details_modal.blade.php ENDPATH**/ ?>