{"__meta": {"id": "01JWT1F08WQ2TXTPY6X2R9J07X", "datetime": "2025-06-03 10:40:45", "utime": **********.085142, "method": "GET", "uri": "/site-cron?run_action=3", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.551874, "end": **********.085154, "duration": 0.5332801342010498, "duration_str": "533ms", "measures": [{"label": "Booting", "start": **********.551874, "relative_start": 0, "end": **********.768622, "relative_end": **********.768622, "duration": 0.*****************, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.76863, "relative_start": 0.*****************, "end": **********.085155, "relative_end": 9.5367431640625e-07, "duration": 0.****************, "duration_str": "317ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.781354, "relative_start": 0.*****************, "end": **********.783363, "relative_end": **********.783363, "duration": 0.002009153366088867, "duration_str": "2.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.083596, "relative_start": 0.****************, "end": **********.083866, "relative_end": **********.083866, "duration": 0.00026988983154296875, "duration_str": "270μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "31MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.16.0", "PHP Version": "8.3.19", "Environment": "local", "Debug Mode": "Enabled", "URL": "orexcoin.test", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 399, "nb_statements": 397, "nb_visible_statements": 399, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13637********0002, "accumulated_duration_str": "136ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 297 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `sessions` where `id` = 'oGjwXvI2S05jRt9urdB3N1br7wpMijx1NNUSl9lh' limit 1", "type": "query", "params": [], "bindings": ["oGjwXvI2S05jRt9urdB3N1br7wpMijx1NNUSl9lh"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.7883708, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php:96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "orexcoin", "explain": null, "start_percent": 0, "width_percent": 0.572}, {"sql": "select * from `languages` where `is_default` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 87}], "start": **********.791363, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "helpers.php:382", "source": {"index": 16, "namespace": null, "name": "app/helpers.php", "file": "E:\\laragon\\www\\orexcoin\\app\\helpers.php", "line": 382}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2Fhelpers.php:382", "ajax": false, "filename": "helpers.php", "line": "382"}, "connection": "orexcoin", "explain": null, "start_percent": 0.572, "width_percent": 0.323}, {"sql": "select * from `cron_jobs` where `cron_jobs`.`id` = '3'", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.798061, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:34", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 34}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:34", "ajax": false, "filename": "CronJobController.php", "line": "34"}, "connection": "orexcoin", "explain": null, "start_percent": 0.895, "width_percent": 0.44}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 108}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.800612, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CronJobController.php:108", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 108}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:108", "ajax": false, "filename": "CronJobController.php", "line": "108"}, "connection": "orexcoin", "explain": null, "start_percent": 1.335, "width_percent": 0}, {"sql": "select * from `users` where `status` = 1 order by `users`.`id` asc limit 500 offset 0", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.804431, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "CronJobController.php:111", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:111", "ajax": false, "filename": "CronJobController.php", "line": "111"}, "connection": "orexcoin", "explain": null, "start_percent": 1.335, "width_percent": 1.701}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = 1 and `user_minings`.`user_id` is not null and `status` = 'ongoing'", "type": "query", "params": [], "bindings": [1, "ongoing"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 113}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.809308, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:113", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 113}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:113", "ajax": false, "filename": "CronJobController.php", "line": "113"}, "connection": "orexcoin", "explain": null, "start_percent": 3.036, "width_percent": 0.469}, {"sql": "select * from `schemes` where `schemes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.811429, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "CronJobController.php:118", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:118", "ajax": false, "filename": "CronJobController.php", "line": "118"}, "connection": "orexcoin", "explain": null, "start_percent": 3.505, "width_percent": 0.792}, {"sql": "select * from `transactions` where `transactions`.`id` = '202' limit 1", "type": "query", "params": [], "bindings": ["202"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.814212, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:142", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:142", "ajax": false, "filename": "CronJobController.php", "line": "142"}, "connection": "orexcoin", "explain": null, "start_percent": 4.297, "width_percent": 0.367}, {"sql": "select * from `miners` where `miners`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.815878, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 4.664, "width_percent": 0.257}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.816688, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 4.92, "width_percent": 0.279}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return of #TRXFQBCNOF56Q', 'Mining_Return', '110.********', 1, 0, '110.********', 'System', 'Success', 4, 'TRX8P7XHPHMHJ', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return of #TRXFQBCNOF56Q", "Mining_Return", "110.********", 1, 0, "110.********", "System", "Success", 4, "TRX8P7XHPHMHJ", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8182468, "duration": 0.00056********000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:140", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:140", "ajax": false, "filename": "CronJobController.php", "line": "140"}, "connection": "orexcoin", "explain": null, "start_percent": 5.199, "width_percent": 0.411}, {"sql": "update `user_minings` set `mining_count` = 1, `total_mined_amount` = 110, `last_mining_time` = '2025-06-03 10:40:44', `next_mining_time` = '2025-06-04 10:40:44', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 4", "type": "query", "params": [], "bindings": [1, 110, "2025-06-03 10:40:44", "2025-06-04 10:40:44", "2025-06-03 10:40:44", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8197448, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:153", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:153", "ajax": false, "filename": "CronJobController.php", "line": "153"}, "connection": "orexcoin", "explain": null, "start_percent": 5.61, "width_percent": 0.418}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.821311, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 6.028, "width_percent": 0.396}, {"sql": "select * from `portfolios` where `portfolios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8228269, "duration": 0.00056********000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 6.424, "width_percent": 0.411}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = 23 and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8247662, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:160", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:160", "ajax": false, "filename": "CronJobController.php", "line": "160"}, "connection": "orexcoin", "explain": null, "start_percent": 6.834, "width_percent": 0.323}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8258011, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:173", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:173", "ajax": false, "filename": "CronJobController.php", "line": "173"}, "connection": "orexcoin", "explain": null, "start_percent": 7.157, "width_percent": 0.198}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return Boost of #TRXFQBCNOF56Q', 'Boosted_Mining_Return', 11, 1, 0, 11, 'System', 'Success', 4, 'TRXZ3O2IBM2DB', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return Boost of #TRXFQBCNOF56Q", "Boosted_Mining_Return", 11, 1, 0, 11, "System", "Success", 4, "TRXZ3O2IBM2DB", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.82671, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:168", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:168", "ajax": false, "filename": "CronJobController.php", "line": "168"}, "connection": "orexcoin", "explain": null, "start_percent": 7.355, "width_percent": 0.235}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.827971, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:185", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:185", "ajax": false, "filename": "CronJobController.php", "line": "185"}, "connection": "orexcoin", "explain": null, "start_percent": 7.59, "width_percent": 0.249}, {"sql": "update `user_minings` set `status` = 'completed', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 4", "type": "query", "params": [], "bindings": ["completed", "2025-06-03 10:40:44", 4], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.828887, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:188", "ajax": false, "filename": "CronJobController.php", "line": "188"}, "connection": "orexcoin", "explain": null, "start_percent": 7.839, "width_percent": 0.183}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8297179, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:212", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:212", "ajax": false, "filename": "CronJobController.php", "line": "212"}, "connection": "orexcoin", "explain": null, "start_percent": 8.022, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Maintenance Fee of #TRXFQBCNOF56Q', 'Maintenance_Fee', 2.75, 1, 0, 2.75, 'System', 'Success', 4, 'TRXZTGEGKVPAK', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Maintenance Fee of #TRXFQBCNOF56Q", "Maintenance_Fee", 2.75, 1, 0, 2.75, "System", "Success", 4, "TRXZTGEGKVPAK", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.830583, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:207", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:207", "ajax": false, "filename": "CronJobController.php", "line": "207"}, "connection": "orexcoin", "explain": null, "start_percent": 8.213, "width_percent": 0.198}, {"sql": "select * from `portfolios` where `portfolios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 221}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.831307, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:221", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 221}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:221", "ajax": false, "filename": "CronJobController.php", "line": "221"}, "connection": "orexcoin", "explain": null, "start_percent": 8.411, "width_percent": 0.183}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.832086, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:248", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:248", "ajax": false, "filename": "CronJobController.php", "line": "248"}, "connection": "orexcoin", "explain": null, "start_percent": 8.594, "width_percent": 0.403}, {"sql": "select * from `schemes` where `schemes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8330991, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:118", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:118", "ajax": false, "filename": "CronJobController.php", "line": "118"}, "connection": "orexcoin", "explain": null, "start_percent": 8.998, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = '209' limit 1", "type": "query", "params": [], "bindings": ["209"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8339238, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:142", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:142", "ajax": false, "filename": "CronJobController.php", "line": "142"}, "connection": "orexcoin", "explain": null, "start_percent": 9.218, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.834673, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 9.408, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.835412, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 9.577, "width_percent": 0.22}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return of #TRXIAPYC6CBDJ', 'Mining_Return', '110.********', 1, 0, '110.********', 'System', 'Success', 6, 'TRXIYLRX4VAXC', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return of #TRXIAPYC6CBDJ", "Mining_Return", "110.********", 1, 0, "110.********", "System", "Success", 6, "TRXIYLRX4VAXC", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.83649, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:140", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:140", "ajax": false, "filename": "CronJobController.php", "line": "140"}, "connection": "orexcoin", "explain": null, "start_percent": 9.797, "width_percent": 0.308}, {"sql": "update `user_minings` set `mining_count` = 1, `total_mined_amount` = 110, `last_mining_time` = '2025-06-03 10:40:44', `next_mining_time` = '2025-06-04 10:40:44', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 6", "type": "query", "params": [], "bindings": [1, 110, "2025-06-03 10:40:44", "2025-06-04 10:40:44", "2025-06-03 10:40:44", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.837807, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:153", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:153", "ajax": false, "filename": "CronJobController.php", "line": "153"}, "connection": "orexcoin", "explain": null, "start_percent": 10.105, "width_percent": 0.301}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.838924, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 10.406, "width_percent": 0.271}, {"sql": "select * from `portfolios` where `portfolios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8398068, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 10.677, "width_percent": 0.191}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = 23 and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8405561, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:160", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:160", "ajax": false, "filename": "CronJobController.php", "line": "160"}, "connection": "orexcoin", "explain": null, "start_percent": 10.867, "width_percent": 0.205}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8413918, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:173", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:173", "ajax": false, "filename": "CronJobController.php", "line": "173"}, "connection": "orexcoin", "explain": null, "start_percent": 11.073, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return Boost of #TRXIAPYC6CBDJ', 'Boosted_Mining_Return', 11, 1, 0, 11, 'System', 'Success', 6, 'TRXH61W7NDRZN', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return Boost of #TRXIAPYC6CBDJ", "Boosted_Mining_Return", 11, 1, 0, 11, "System", "Success", 6, "TRXH61W7NDRZN", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.842258, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:168", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:168", "ajax": false, "filename": "CronJobController.php", "line": "168"}, "connection": "orexcoin", "explain": null, "start_percent": 11.263, "width_percent": 0.22}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.843107, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:185", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:185", "ajax": false, "filename": "CronJobController.php", "line": "185"}, "connection": "orexcoin", "explain": null, "start_percent": 11.483, "width_percent": 0.22}, {"sql": "update `user_minings` set `status` = 'completed', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 6", "type": "query", "params": [], "bindings": ["completed", "2025-06-03 10:40:44", 6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8439739, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:188", "ajax": false, "filename": "CronJobController.php", "line": "188"}, "connection": "orexcoin", "explain": null, "start_percent": 11.703, "width_percent": 0.191}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.844806, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:212", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:212", "ajax": false, "filename": "CronJobController.php", "line": "212"}, "connection": "orexcoin", "explain": null, "start_percent": 11.894, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Maintenance Fee of #TRXIAPYC6CBDJ', 'Maintenance_Fee', 2.75, 1, 0, 2.75, 'System', 'Success', 6, 'TRXC7VEAHGVEY', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Maintenance Fee of #TRXIAPYC6CBDJ", "Maintenance_Fee", 2.75, 1, 0, 2.75, "System", "Success", 6, "TRXC7VEAHGVEY", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.845654, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:207", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:207", "ajax": false, "filename": "CronJobController.php", "line": "207"}, "connection": "orexcoin", "explain": null, "start_percent": 12.085, "width_percent": 0.205}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.846457, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:248", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:248", "ajax": false, "filename": "CronJobController.php", "line": "248"}, "connection": "orexcoin", "explain": null, "start_percent": 12.29, "width_percent": 0.213}, {"sql": "select * from `schemes` where `schemes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8472052, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:118", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:118", "ajax": false, "filename": "CronJobController.php", "line": "118"}, "connection": "orexcoin", "explain": null, "start_percent": 12.503, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = '210' limit 1", "type": "query", "params": [], "bindings": ["210"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.848073, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:142", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:142", "ajax": false, "filename": "CronJobController.php", "line": "142"}, "connection": "orexcoin", "explain": null, "start_percent": 12.723, "width_percent": 0.213}, {"sql": "select * from `miners` where `miners`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8491, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 12.935, "width_percent": 0.205}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.849839, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 13.141, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return of #TRXPG4TEXP8GV', 'Mining_Return', '110.********', 1, 0, '110.********', 'System', 'Success', 7, 'TRXDDRSEWSBFF', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return of #TRXPG4TEXP8GV", "Mining_Return", "110.********", 1, 0, "110.********", "System", "Success", 7, "TRXDDRSEWSBFF", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8507671, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:140", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:140", "ajax": false, "filename": "CronJobController.php", "line": "140"}, "connection": "orexcoin", "explain": null, "start_percent": 13.324, "width_percent": 0.242}, {"sql": "update `user_minings` set `mining_count` = 1, `total_mined_amount` = 110, `last_mining_time` = '2025-06-03 10:40:44', `next_mining_time` = '2025-06-04 10:40:44', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 7", "type": "query", "params": [], "bindings": [1, 110, "2025-06-03 10:40:44", "2025-06-04 10:40:44", "2025-06-03 10:40:44", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8518472, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:153", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:153", "ajax": false, "filename": "CronJobController.php", "line": "153"}, "connection": "orexcoin", "explain": null, "start_percent": 13.566, "width_percent": 0.205}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.852973, "duration": 0.00052********000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 13.771, "width_percent": 0.381}, {"sql": "select * from `portfolios` where `portfolios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.854286, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 14.153, "width_percent": 0.301}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = 23 and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8553371, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:160", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:160", "ajax": false, "filename": "CronJobController.php", "line": "160"}, "connection": "orexcoin", "explain": null, "start_percent": 14.453, "width_percent": 0.242}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8562758, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:173", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:173", "ajax": false, "filename": "CronJobController.php", "line": "173"}, "connection": "orexcoin", "explain": null, "start_percent": 14.695, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return Boost of #TRXPG4TEXP8GV', 'Boosted_Mining_Return', 11, 1, 0, 11, 'System', 'Success', 7, 'TRXDAQZV1RQPQ', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return Boost of #TRXPG4TEXP8GV", "Boosted_Mining_Return", 11, 1, 0, 11, "System", "Success", 7, "TRXDAQZV1RQPQ", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.857179, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:168", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:168", "ajax": false, "filename": "CronJobController.php", "line": "168"}, "connection": "orexcoin", "explain": null, "start_percent": 14.886, "width_percent": 0.242}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.858055, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:185", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:185", "ajax": false, "filename": "CronJobController.php", "line": "185"}, "connection": "orexcoin", "explain": null, "start_percent": 15.128, "width_percent": 0.506}, {"sql": "update `user_minings` set `status` = 'completed', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 7", "type": "query", "params": [], "bindings": ["completed", "2025-06-03 10:40:44", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8593228, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:188", "ajax": false, "filename": "CronJobController.php", "line": "188"}, "connection": "orexcoin", "explain": null, "start_percent": 15.634, "width_percent": 0.183}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8601398, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:212", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:212", "ajax": false, "filename": "CronJobController.php", "line": "212"}, "connection": "orexcoin", "explain": null, "start_percent": 15.817, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Maintenance Fee of #TRXPG4TEXP8GV', 'Maintenance_Fee', 2.75, 1, 0, 2.75, 'System', 'Success', 7, 'TRX37AI4FOS71', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Maintenance Fee of #TRXPG4TEXP8GV", "Maintenance_Fee", 2.75, 1, 0, 2.75, "System", "Success", 7, "TRX37AI4FOS71", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8609931, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:207", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:207", "ajax": false, "filename": "CronJobController.php", "line": "207"}, "connection": "orexcoin", "explain": null, "start_percent": 16.008, "width_percent": 0.198}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.861783, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:248", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:248", "ajax": false, "filename": "CronJobController.php", "line": "248"}, "connection": "orexcoin", "explain": null, "start_percent": 16.206, "width_percent": 0.22}, {"sql": "select * from `schemes` where `schemes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.862803, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:118", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:118", "ajax": false, "filename": "CronJobController.php", "line": "118"}, "connection": "orexcoin", "explain": null, "start_percent": 16.426, "width_percent": 0.213}, {"sql": "select * from `transactions` where `transactions`.`id` = '211' limit 1", "type": "query", "params": [], "bindings": ["211"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8636272, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:142", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:142", "ajax": false, "filename": "CronJobController.php", "line": "142"}, "connection": "orexcoin", "explain": null, "start_percent": 16.639, "width_percent": 0.213}, {"sql": "select * from `miners` where `miners`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8646479, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 16.851, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8653, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 17.013, "width_percent": 0.235}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return of #TRXBVYZEEQ8W7', 'Mining_Return', '110.********', 1, 0, '110.********', 'System', 'Success', 8, 'TRX0YFSEEFB16', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return of #TRXBVYZEEQ8W7", "Mining_Return", "110.********", 1, 0, "110.********", "System", "Success", 8, "TRX0YFSEEFB16", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.866344, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:140", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:140", "ajax": false, "filename": "CronJobController.php", "line": "140"}, "connection": "orexcoin", "explain": null, "start_percent": 17.247, "width_percent": 0.235}, {"sql": "update `user_minings` set `mining_count` = 1, `total_mined_amount` = 110, `last_mining_time` = '2025-06-03 10:40:44', `next_mining_time` = '2025-06-04 10:40:44', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 8", "type": "query", "params": [], "bindings": [1, 110, "2025-06-03 10:40:44", "2025-06-04 10:40:44", "2025-06-03 10:40:44", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8672981, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:153", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:153", "ajax": false, "filename": "CronJobController.php", "line": "153"}, "connection": "orexcoin", "explain": null, "start_percent": 17.482, "width_percent": 0.22}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.868392, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 17.702, "width_percent": 0.264}, {"sql": "select * from `portfolios` where `portfolios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.869276, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 17.966, "width_percent": 0.235}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = 23 and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.87011, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:160", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:160", "ajax": false, "filename": "CronJobController.php", "line": "160"}, "connection": "orexcoin", "explain": null, "start_percent": 18.2, "width_percent": 0.22}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.871117, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:173", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:173", "ajax": false, "filename": "CronJobController.php", "line": "173"}, "connection": "orexcoin", "explain": null, "start_percent": 18.42, "width_percent": 0.242}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return Boost of #TRXBVYZEEQ8W7', 'Boosted_Mining_Return', 11, 1, 0, 11, 'System', 'Success', 8, 'TRXQGWKWOK69Y', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return Boost of #TRXBVYZEEQ8W7", "Boosted_Mining_Return", 11, 1, 0, 11, "System", "Success", 8, "TRXQGWKWOK69Y", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.872106, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:168", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:168", "ajax": false, "filename": "CronJobController.php", "line": "168"}, "connection": "orexcoin", "explain": null, "start_percent": 18.662, "width_percent": 0.235}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.872961, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:185", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:185", "ajax": false, "filename": "CronJobController.php", "line": "185"}, "connection": "orexcoin", "explain": null, "start_percent": 18.897, "width_percent": 0.213}, {"sql": "update `user_minings` set `status` = 'completed', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 8", "type": "query", "params": [], "bindings": ["completed", "2025-06-03 10:40:44", 8], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8737888, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:188", "ajax": false, "filename": "CronJobController.php", "line": "188"}, "connection": "orexcoin", "explain": null, "start_percent": 19.11, "width_percent": 0.176}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.874601, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:212", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:212", "ajax": false, "filename": "CronJobController.php", "line": "212"}, "connection": "orexcoin", "explain": null, "start_percent": 19.286, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Maintenance Fee of #TRXBVYZEEQ8W7', 'Maintenance_Fee', 2.75, 1, 0, 2.75, 'System', 'Success', 8, 'TRXWHINIAUNM3', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Maintenance Fee of #TRXBVYZEEQ8W7", "Maintenance_Fee", 2.75, 1, 0, 2.75, "System", "Success", 8, "TRXWHINIAUNM3", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.875461, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:207", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:207", "ajax": false, "filename": "CronJobController.php", "line": "207"}, "connection": "orexcoin", "explain": null, "start_percent": 19.469, "width_percent": 0.198}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.876234, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:248", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:248", "ajax": false, "filename": "CronJobController.php", "line": "248"}, "connection": "orexcoin", "explain": null, "start_percent": 19.667, "width_percent": 0.205}, {"sql": "select * from `schemes` where `schemes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.876972, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:118", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:118", "ajax": false, "filename": "CronJobController.php", "line": "118"}, "connection": "orexcoin", "explain": null, "start_percent": 19.872, "width_percent": 0.227}, {"sql": "select * from `transactions` where `transactions`.`id` = '212' limit 1", "type": "query", "params": [], "bindings": ["212"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.877806, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:142", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:142", "ajax": false, "filename": "CronJobController.php", "line": "142"}, "connection": "orexcoin", "explain": null, "start_percent": 20.1, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.878564, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 20.29, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8792038, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 20.459, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return of #TRXQORCOCYFWF', 'Mining_Return', '110.********', 1, 0, '110.********', 'System', 'Success', 9, 'TRXXUEYHJABII', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return of #TRXQORCOCYFWF", "Mining_Return", "110.********", 1, 0, "110.********", "System", "Success", 9, "TRXXUEYHJABII", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8800142, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:140", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:140", "ajax": false, "filename": "CronJobController.php", "line": "140"}, "connection": "orexcoin", "explain": null, "start_percent": 20.642, "width_percent": 0.205}, {"sql": "update `user_minings` set `mining_count` = 1, `total_mined_amount` = 110, `last_mining_time` = '2025-06-03 10:40:44', `next_mining_time` = '2025-06-04 10:40:44', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 9", "type": "query", "params": [], "bindings": [1, 110, "2025-06-03 10:40:44", "2025-06-04 10:40:44", "2025-06-03 10:40:44", 9], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8809152, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:153", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:153", "ajax": false, "filename": "CronJobController.php", "line": "153"}, "connection": "orexcoin", "explain": null, "start_percent": 20.848, "width_percent": 0.198}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8817601, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 21.046, "width_percent": 0.227}, {"sql": "select * from `portfolios` where `portfolios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.882526, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 21.273, "width_percent": 0.176}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = 23 and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.883295, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:160", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:160", "ajax": false, "filename": "CronJobController.php", "line": "160"}, "connection": "orexcoin", "explain": null, "start_percent": 21.449, "width_percent": 0.264}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.88434, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:173", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:173", "ajax": false, "filename": "CronJobController.php", "line": "173"}, "connection": "orexcoin", "explain": null, "start_percent": 21.713, "width_percent": 0.227}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return Boost of #TRXQORCOCYFWF', 'Boosted_Mining_Return', 11, 1, 0, 11, 'System', 'Success', 9, 'TRXGGMDUPKZ8P', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return Boost of #TRXQORCOCYFWF", "Boosted_Mining_Return", 11, 1, 0, 11, "System", "Success", 9, "TRXGGMDUPKZ8P", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.885236, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:168", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:168", "ajax": false, "filename": "CronJobController.php", "line": "168"}, "connection": "orexcoin", "explain": null, "start_percent": 21.94, "width_percent": 0.213}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.886167, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:185", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:185", "ajax": false, "filename": "CronJobController.php", "line": "185"}, "connection": "orexcoin", "explain": null, "start_percent": 22.153, "width_percent": 0.227}, {"sql": "update `user_minings` set `status` = 'completed', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 9", "type": "query", "params": [], "bindings": ["completed", "2025-06-03 10:40:44", 9], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.887043, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:188", "ajax": false, "filename": "CronJobController.php", "line": "188"}, "connection": "orexcoin", "explain": null, "start_percent": 22.38, "width_percent": 0.22}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.88802, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:212", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:212", "ajax": false, "filename": "CronJobController.php", "line": "212"}, "connection": "orexcoin", "explain": null, "start_percent": 22.6, "width_percent": 0.242}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Maintenance Fee of #TRXQORCOCYFWF', 'Maintenance_Fee', 2.75, 1, 0, 2.75, 'System', 'Success', 9, 'TRXRDBRHKFRYL', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Maintenance Fee of #TRXQORCOCYFWF", "Maintenance_Fee", 2.75, 1, 0, 2.75, "System", "Success", 9, "TRXRDBRHKFRYL", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.888999, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:207", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 207}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:207", "ajax": false, "filename": "CronJobController.php", "line": "207"}, "connection": "orexcoin", "explain": null, "start_percent": 22.842, "width_percent": 0.22}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.889837, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:248", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 248}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:248", "ajax": false, "filename": "CronJobController.php", "line": "248"}, "connection": "orexcoin", "explain": null, "start_percent": 23.062, "width_percent": 0.205}, {"sql": "select * from `schemes` where `schemes`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8905811, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:118", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 118}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:118", "ajax": false, "filename": "CronJobController.php", "line": "118"}, "connection": "orexcoin", "explain": null, "start_percent": 23.268, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = '213' limit 1", "type": "query", "params": [], "bindings": ["213"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.891404, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:142", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 142}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:142", "ajax": false, "filename": "CronJobController.php", "line": "142"}, "connection": "orexcoin", "explain": null, "start_percent": 23.488, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = '3' limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8921359, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 23.678, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.892776, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:145", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 145}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:145", "ajax": false, "filename": "CronJobController.php", "line": "145"}, "connection": "orexcoin", "explain": null, "start_percent": 23.847, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return of #TRXEBHN4UDE3F', 'Mining_Return', '110.********', 1, 0, '110.********', 'System', 'Success', 10, 'TRXQMXS4WWTWG', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return of #TRXEBHN4UDE3F", "Mining_Return", "110.********", 1, 0, "110.********", "System", "Success", 10, "TRXQMXS4WWTWG", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.893593, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:140", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 140}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:140", "ajax": false, "filename": "CronJobController.php", "line": "140"}, "connection": "orexcoin", "explain": null, "start_percent": 24.03, "width_percent": 0.198}, {"sql": "update `user_minings` set `mining_count` = 1, `total_mined_amount` = 110, `last_mining_time` = '2025-06-03 10:40:44', `next_mining_time` = '2025-06-04 10:40:44', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 10", "type": "query", "params": [], "bindings": [1, 110, "2025-06-03 10:40:44", "2025-06-04 10:40:44", "2025-06-03 10:40:44", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.894499, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:153", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 153}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:153", "ajax": false, "filename": "CronJobController.php", "line": "153"}, "connection": "orexcoin", "explain": null, "start_percent": 24.228, "width_percent": 0.198}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8953412, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 24.426, "width_percent": 0.227}, {"sql": "select * from `portfolios` where `portfolios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.896107, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:159", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 159}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:159", "ajax": false, "filename": "CronJobController.php", "line": "159"}, "connection": "orexcoin", "explain": null, "start_percent": 24.654, "width_percent": 0.183}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = 23 and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8968108, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:160", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 160}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:160", "ajax": false, "filename": "CronJobController.php", "line": "160"}, "connection": "orexcoin", "explain": null, "start_percent": 24.837, "width_percent": 0.191}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.897538, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:173", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 173}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:173", "ajax": false, "filename": "CronJobController.php", "line": "173"}, "connection": "orexcoin", "explain": null, "start_percent": 25.027, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (1, 'Mining Return Boost of #TRXEBHN4UDE3F', 'Boosted_Mining_Return', 11, 1, 0, 11, 'System', 'Success', 10, 'TRXYSRIUIENKC', '2025-06-03 10:40:44', '2025-06-03 10:40:44')", "type": "query", "params": [], "bindings": [1, "Mining Return Boost of #TRXEBHN4UDE3F", "Boosted_Mining_Return", 11, 1, 0, 11, "System", "Success", 10, "TRXYSRIUIENKC", "2025-06-03 10:40:44", "2025-06-03 10:40:44"], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.89835, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:168", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 168}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:168", "ajax": false, "filename": "CronJobController.php", "line": "168"}, "connection": "orexcoin", "explain": null, "start_percent": 25.211, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = '2025-06-03 10:40:44' where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6", "type": "query", "params": [], "bindings": ["2025-06-03 10:40:44", 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.8991032, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:185", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 185}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:185", "ajax": false, "filename": "CronJobController.php", "line": "185"}, "connection": "orexcoin", "explain": null, "start_percent": 25.401, "width_percent": 0.462}, {"sql": "update `user_minings` set `status` = 'completed', `user_minings`.`updated_at` = '2025-06-03 10:40:44' where `id` = 10", "type": "query", "params": [], "bindings": ["completed", "2025-06-03 10:40:44", 10], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.900259, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 188}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:188", "ajax": false, "filename": "CronJobController.php", "line": "188"}, "connection": "orexcoin", "explain": null, "start_percent": 25.863, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = 1 and `user_wallets`.`user_id` is not null and `coin_id` = 6 limit 1", "type": "query", "params": [], "bindings": [1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 111}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}], "start": **********.9010248, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "CronJobController.php:212", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 212}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:212", "ajax": false, "filename": "CronJobController.php", "line": "212"}, "connection": "orexcoin", "explain": null, "start_percent": 26.032, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9018369, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 26.215, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.902314, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 26.406, "width_percent": 0.301}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.902873, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 26.707, "width_percent": 0.286}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.903606, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 26.993, "width_percent": 0.279}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.904279, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 27.271, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9047182, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 27.469, "width_percent": 0.22}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.90535, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 27.689, "width_percent": 0.235}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.906039, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 27.924, "width_percent": 0.213}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9065762, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 28.137, "width_percent": 0.235}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.90709, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 28.371, "width_percent": 0.183}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9075491, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 28.555, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.908021, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 28.753, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.908544, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 28.936, "width_percent": 0.213}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.909064, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 29.149, "width_percent": 0.205}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.909546, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 29.354, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9099739, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 29.523, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.910479, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 29.706, "width_percent": 0.198}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9109612, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 29.904, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.911353, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 30.102, "width_percent": 0.205}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.911852, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 30.307, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.912298, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 30.498, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9126399, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 30.659, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9131298, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 30.835, "width_percent": 0.191}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9137099, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 31.026, "width_percent": 0.191}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.91419, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 31.217, "width_percent": 0.235}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.914664, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 31.451, "width_percent": 0.176}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9150739, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 31.627, "width_percent": 0.183}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9155078, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 31.811, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9160001, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 31.987, "width_percent": 0.213}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.916485, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 32.199, "width_percent": 0.198}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.916941, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 32.397, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.917367, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 32.566, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9178529, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 32.742, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.918312, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 32.932, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9186962, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 33.13, "width_percent": 0.198}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.91927, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 33.328, "width_percent": 0.286}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.919939, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 33.614, "width_percent": 0.257}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.920493, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 33.871, "width_percent": 0.286}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9214542, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 34.157, "width_percent": 0.337}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922416, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 34.494, "width_percent": 0.279}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.923133, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 34.773, "width_percent": 0.286}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9237208, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 35.059, "width_percent": 0.183}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9241621, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 35.242, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.924645, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 35.44, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925212, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 35.624, "width_percent": 0.293}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.925946, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 35.917, "width_percent": 0.271}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.926646, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 36.188, "width_percent": 0.213}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927272, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 36.401, "width_percent": 0.227}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927905, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 36.628, "width_percent": 0.249}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928512, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 36.878, "width_percent": 0.22}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928957, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 37.098, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929509, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 37.318, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929965, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 37.508, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9303188, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 37.677, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930835, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 37.86, "width_percent": 0.235}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.931608, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 38.095, "width_percent": 0.235}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932156, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 38.33, "width_percent": 0.235}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.932641, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 38.564, "width_percent": 0.176}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93305, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 38.74, "width_percent": 0.191}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.933496, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 38.931, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.933997, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 39.107, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9344592, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 39.297, "width_percent": 0.198}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93491, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 39.495, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935371, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 39.693, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.93593, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 39.869, "width_percent": 0.249}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.936548, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 40.119, "width_percent": 0.242}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.937039, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 40.361, "width_percent": 0.286}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.937842, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 40.647, "width_percent": 0.271}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.938478, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 40.918, "width_percent": 0.183}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.938885, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 41.101, "width_percent": 0.293}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.939707, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 41.395, "width_percent": 0.293}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.94062, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 41.688, "width_percent": 0.279}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.941257, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 41.967, "width_percent": 0.249}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.941772, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 42.216, "width_percent": 0.176}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942186, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 42.392, "width_percent": 0.205}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9426708, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 42.597, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943213, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 42.781, "width_percent": 0.22}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9437292, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 43.001, "width_percent": 0.213}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9442098, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 43.213, "width_percent": 0.176}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9446468, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 43.389, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945155, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 43.573, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.945621, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 43.763, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9460092, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 43.961, "width_percent": 0.213}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946523, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 44.174, "width_percent": 0.183}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946978, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 44.357, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947406, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 44.555, "width_percent": 0.22}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9479792, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 44.775, "width_percent": 0.198}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.948601, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 44.973, "width_percent": 0.198}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949109, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 45.171, "width_percent": 0.235}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9495919, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 45.406, "width_percent": 0.176}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950008, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 45.582, "width_percent": 0.191}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9504678, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 45.773, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950983, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 45.949, "width_percent": 0.198}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951478, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 46.147, "width_percent": 0.198}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951943, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 46.345, "width_percent": 0.176}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.952415, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 46.52, "width_percent": 0.293}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953344, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 46.814, "width_percent": 0.499}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954618, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 47.312, "width_percent": 0.484}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955591, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 47.796, "width_percent": 0.491}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957285, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 48.288, "width_percent": 0.271}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9580991, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 48.559, "width_percent": 0.227}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.958636, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 48.786, "width_percent": 0.235}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959306, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 49.021, "width_percent": 0.249}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9600601, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 49.27, "width_percent": 0.22}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.960627, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 49.49, "width_percent": 0.242}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961138, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 49.732, "width_percent": 0.183}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9615762, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 49.916, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9620569, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 50.114, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.962588, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 50.297, "width_percent": 0.213}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.963157, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 50.51, "width_percent": 0.235}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.963737, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 50.744, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9642642, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 50.942, "width_percent": 0.198}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.964822, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 51.14, "width_percent": 0.205}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965343, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 51.346, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.965739, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 51.544, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966291, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 51.764, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.966738, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 51.954, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967094, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 52.123, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.967599, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 52.299, "width_percent": 0.191}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.968225, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 52.49, "width_percent": 0.191}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.968714, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 52.68, "width_percent": 0.249}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.96925, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 52.93, "width_percent": 0.257}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.969829, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 53.186, "width_percent": 0.213}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.970347, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 53.399, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9711142, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 53.589, "width_percent": 0.337}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.971924, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 53.927, "width_percent": 0.323}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9726062, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 54.249, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.973129, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 54.447, "width_percent": 0.205}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.973705, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 54.653, "width_percent": 0.205}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9742322, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 54.858, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9746351, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 55.056, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.975179, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 55.276, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9756289, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 55.467, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9759889, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 55.635, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9765031, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 55.811, "width_percent": 0.198}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977129, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 56.009, "width_percent": 0.22}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.977669, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 56.229, "width_percent": 0.242}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978168, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 56.471, "width_percent": 0.183}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.978599, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 56.655, "width_percent": 0.198}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.979095, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 56.853, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9796262, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 57.036, "width_percent": 0.205}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.980133, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 57.241, "width_percent": 0.205}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.980609, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 57.447, "width_percent": 0.176}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981051, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 57.623, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9815621, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 57.806, "width_percent": 0.198}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982053, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 58.004, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.982448, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 58.202, "width_percent": 0.213}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.98297, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 58.415, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.983422, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 58.605, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9837618, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 58.767, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.984277, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 58.95, "width_percent": 0.205}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9849012, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 59.155, "width_percent": 0.205}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.985415, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 59.361, "width_percent": 0.242}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.985955, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 59.603, "width_percent": 0.279}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.986589, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 59.881, "width_percent": 0.271}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.987235, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 60.153, "width_percent": 0.235}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.988163, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 60.387, "width_percent": 0.293}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.988885, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 60.681, "width_percent": 0.249}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9894588, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 60.93, "width_percent": 0.191}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.989967, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 61.12, "width_percent": 0.205}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.99054, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 61.326, "width_percent": 0.213}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.991076, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 61.538, "width_percent": 0.205}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9914992, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 61.744, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9920518, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 61.964, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.992549, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 62.154, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9929118, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 62.323, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.993432, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 62.506, "width_percent": 0.205}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.994069, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 62.712, "width_percent": 0.213}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9945931, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 62.924, "width_percent": 0.242}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9951048, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 63.166, "width_percent": 0.183}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.995535, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 63.35, "width_percent": 0.191}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.995991, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 63.54, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.996504, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 63.716, "width_percent": 0.213}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9970062, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 63.929, "width_percent": 0.198}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.997468, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 64.127, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.997895, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 64.296, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9983869, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 64.472, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9988482, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 64.662, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.999231, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 64.86, "width_percent": 0.205}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9997299, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 65.066, "width_percent": 0.183}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.000162, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 65.249, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.000517, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 65.41, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0010078, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 65.586, "width_percent": 0.183}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.001592, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 65.77, "width_percent": 0.191}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.002072, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 65.96, "width_percent": 0.213}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.002553, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 66.173, "width_percent": 0.205}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0030391, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 66.378, "width_percent": 0.205}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.003543, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 66.584, "width_percent": 0.249}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0043988, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 66.833, "width_percent": 0.293}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.005105, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 67.126, "width_percent": 0.264}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.005678, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 67.39, "width_percent": 0.176}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.006135, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 67.566, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0066679, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 67.757, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0071428, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 67.947, "width_percent": 0.191}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.007527, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 68.138, "width_percent": 0.22}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008055, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 68.358, "width_percent": 0.183}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008486, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 68.541, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.008827, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 68.703, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.009316, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 68.879, "width_percent": 1.159}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.011253, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 70.037, "width_percent": 0.205}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.011763, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 70.243, "width_percent": 0.235}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.012244, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 70.477, "width_percent": 0.191}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.012672, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 70.668, "width_percent": 0.191}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.013119, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 70.859, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.01362, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 71.035, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.014076, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 71.225, "width_percent": 0.198}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.014528, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 71.423, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.014947, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 71.592, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.015435, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 71.768, "width_percent": 0.183}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.015889, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 71.951, "width_percent": 0.191}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0162702, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 72.142, "width_percent": 0.205}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.016764, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 72.347, "width_percent": 0.183}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.017191, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 72.531, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0175269, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 72.692, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0180118, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 72.868, "width_percent": 0.183}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.018574, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 73.051, "width_percent": 0.191}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.019104, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 73.242, "width_percent": 0.374}, {"sql": "select * from `portfolios` where `portfolios`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.019843, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 73.616, "width_percent": 0.249}, {"sql": "select * from `portfolio_features` where `portfolio_features`.`portfolio_id` = ? and `portfolio_features`.`portfolio_id` is not null limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.020401, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 73.865, "width_percent": 0.227}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.021082, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 74.093, "width_percent": 0.271}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.021881, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 74.364, "width_percent": 0.323}, {"sql": "update `user_wallets` set `balance` = `balance` + 121, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.022586, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 74.687, "width_percent": 0.22}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.023094, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 74.907, "width_percent": 0.176}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0235448, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 75.082, "width_percent": 0.183}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0240588, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 75.266, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0245261, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 75.456, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0249171, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 75.654, "width_percent": 0.213}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.025411, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 75.867, "width_percent": 0.191}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.025848, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 76.058, "width_percent": 0.191}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.02628, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 76.248, "width_percent": 0.191}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0267231, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 76.439, "width_percent": 0.198}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.02719, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 76.637, "width_percent": 0.213}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0276518, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 76.85, "width_percent": 0.191}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.028087, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 77.04, "width_percent": 0.183}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.028505, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 77.224, "width_percent": 0.205}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.028961, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 77.429, "width_percent": 0.183}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.029406, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 77.612, "width_percent": 0.213}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0299408, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 77.825, "width_percent": 0.176}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0303059, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 78.001, "width_percent": 0.169}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.030659, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 78.17, "width_percent": 0.169}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0310109, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 78.338, "width_percent": 0.169}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0313568, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 78.507, "width_percent": 0.183}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.031728, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 78.69, "width_percent": 0.176}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.032115, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 78.866, "width_percent": 0.191}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.03258, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 79.057, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0330238, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 79.248, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0333629, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 79.409, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.033879, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 79.6, "width_percent": 0.205}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0344932, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 79.805, "width_percent": 0.205}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.035018, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 80.01, "width_percent": 0.249}, {"sql": "update `user_wallets` set `balance` = `balance` + 110.********, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.035618, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 80.26, "width_percent": 0.279}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.036212, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 80.538, "width_percent": 0.183}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0366921, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 80.722, "width_percent": 0.213}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.037297, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 80.934, "width_percent": 0.235}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0379112, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 81.169, "width_percent": 0.213}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.038324, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 81.382, "width_percent": 0.213}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.038837, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 81.594, "width_percent": 0.191}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.039276, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 81.785, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0396159, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 81.946, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0401068, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 82.122, "width_percent": 0.191}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04069, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 82.313, "width_percent": 0.191}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0411642, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 82.503, "width_percent": 0.22}, {"sql": "update `user_wallets` set `balance` = `balance` + 110.********, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.041704, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 82.723, "width_percent": 0.191}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0421388, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 82.914, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0425608, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 83.083, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.043083, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 83.259, "width_percent": 0.191}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0435472, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 83.449, "width_percent": 0.198}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.043929, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 83.647, "width_percent": 0.205}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0443642, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 83.853, "width_percent": 0.198}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0448399, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 84.051, "width_percent": 0.183}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.045258, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 84.234, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.045596, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 84.395, "width_percent": 0.176}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0460818, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 84.571, "width_percent": 0.183}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04664, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 84.755, "width_percent": 0.183}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.047097, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 84.938, "width_percent": 0.213}, {"sql": "update `user_wallets` set `balance` = `balance` + 110.********, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0476208, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 85.151, "width_percent": 0.191}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.048056, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 85.341, "width_percent": 0.169}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.048471, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 85.51, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0489788, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 85.701, "width_percent": 0.183}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.049447, "duration": 0.00028********0000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 85.884, "width_percent": 0.205}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0498378, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 86.089, "width_percent": 0.191}, {"sql": "select * from `transactions` where `transactions`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0503008, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 86.28, "width_percent": 0.183}, {"sql": "select * from `miners` where `miners`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0507228, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 86.463, "width_percent": 0.161}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.051077, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 86.625, "width_percent": 0.418}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.052075, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 87.043, "width_percent": 0.257}, {"sql": "update `user_minings` set `mining_count` = ?, `total_mined_amount` = ?, `last_mining_time` = ?, `next_mining_time` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.052913, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 87.299, "width_percent": 0.271}, {"sql": "select * from `users` where `users`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.053616, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 87.571, "width_percent": 0.293}, {"sql": "update `user_wallets` set `balance` = `balance` + 110.********, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.054405, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 87.864, "width_percent": 0.22}, {"sql": "update `user_minings` set `status` = ?, `user_minings`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0549078, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 88.084, "width_percent": 0.176}, {"sql": "select * from `user_wallets` where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.055379, "duration": 0.00026********0000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 88.26, "width_percent": 0.191}, {"sql": "insert into `transactions` (`user_id`, `description`, `type`, `amount`, `wallet_type`, `charge`, `final_amount`, `method`, `status`, `user_mining_id`, `tnx`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.055936, "duration": 0.00524, "duration_str": "5.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 88.451, "width_percent": 3.842}, {"sql": "update `user_wallets` set `balance` = `balance` - 2.75, `user_wallets`.`updated_at` = ? where `user_wallets`.`user_id` = ? and `user_wallets`.`user_id` is not null and `coin_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.061446, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 92.293, "width_percent": 0.22}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.06188, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 92.513, "width_percent": 0.198}, {"sql": "select * from `user_minings` where `user_minings`.`user_id` = ? and `user_minings`.`user_id` is not null and `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0623, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 92.711, "width_percent": 0.183}, {"sql": "select * from `schemes` where `schemes`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.062722, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 92.894, "width_percent": 0.213}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 258}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 48}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\laragon\\www\\orexcoin\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}], "start": **********.068437, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CronJobController.php:258", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/CronJobController.php", "file": "E:\\laragon\\www\\orexcoin\\app\\Http\\Controllers\\CronJobController.php", "line": 258}, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:258", "ajax": false, "filename": "CronJobController.php", "line": "258"}, "connection": "orexcoin", "explain": null, "start_percent": 93.107, "width_percent": 0}, {"sql": "insert into `cron_job_logs` (`cron_job_id`, `started_at`, `ended_at`, `error`, `updated_at`, `created_at`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0688128, "duration": 0.0053, "duration_str": "5.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 93.107, "width_percent": 3.886}, {"sql": "update `cron_jobs` set `next_run_at` = ?, `last_run_at` = ?, `cron_jobs`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.074827, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "orexcoin", "explain": null, "start_percent": 96.993, "width_percent": 3.007}]}, "models": {"data": {"App\\Models\\UserWallet": {"value": 68, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUserWallet.php:1", "ajax": false, "filename": "UserWallet.php", "line": "?"}}, "App\\Models\\User": {"value": 35, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUser.php:1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\UserMining": {"value": 35, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FUserMining.php:1", "ajax": false, "filename": "UserMining.php", "line": "?"}}, "App\\Models\\Scheme": {"value": 35, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FScheme.php:1", "ajax": false, "filename": "Scheme.php", "line": "?"}}, "App\\Models\\Transaction": {"value": 24, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FTransaction.php:1", "ajax": false, "filename": "Transaction.php", "line": "?"}}, "App\\Models\\Miner": {"value": 24, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FMiner.php:1", "ajax": false, "filename": "Miner.php", "line": "?"}}, "App\\Models\\Portfolio": {"value": 21, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FPortfolio.php:1", "ajax": false, "filename": "Portfolio.php", "line": "?"}}, "App\\Models\\PortfolioFeature": {"value": 20, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FPortfolioFeature.php:1", "ajax": false, "filename": "PortfolioFeature.php", "line": "?"}}, "App\\Models\\CronJob": {"value": 1, "xdebug_link": {"url": "vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FModels%2FCronJob.php:1", "ajax": false, "filename": "CronJob.php", "line": "?"}}}, "count": 263, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "https://orexcoin.test/site-cron?run_action=3", "action_name": "cron.job", "controller_action": "App\\Http\\Controllers\\CronJobController@runCronJobs", "uri": "GET site-cron", "controller": "App\\Http\\Controllers\\CronJobController@runCronJobs<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:23\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"vscode://file/E%3A%2Flaragon%2Fwww%2Forexcoin%2Fapp%2FHttp%2FControllers%2FCronJobController.php:23\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/CronJobController.php:23-73</a>", "middleware": "web", "duration": "546ms", "peak_memory": "34MB", "response": "Redirect to https://orexcoin.test/admin/cron-jobs", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-555990464 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>run_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555990464\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-14718072 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-14718072\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2118332020 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"734 characters\">acceptCookies=true; XSRF-TOKEN=eyJpdiI6IkRWNE9NMXVJdm90QjF5RysxTS91dlE9PSIsInZhbHVlIjoiV2oybVp6S1JaM3lwdFJURFJmZWYrNTBQd0Fxcm02SXVKU1FkMHhCMEgzSW92YTNxVXFiREYxKzFrV055dTRvaDI4YkNxc0RFRm1Cc08venBOZzlCWnljR25jckl2a2VjVWJWMVF4K2xjY0NRenpCRDExbGJuTStGVy82c081TDkiLCJtYWMiOiIyMzcwZTkwOGZhYTMyNzg0YzE4YjZmMWZjODFlNDAxYzJhNGYxZWZmMWNmYzM0MjE4MjkyYzQ1OTdlZjYzYzY2IiwidGFnIjoiIn0%3D; orexcoin_session=eyJpdiI6IllHRzdLRk9SNGM0TzIvV29PNFpRTGc9PSIsInZhbHVlIjoiMkdWRFdzU2NPcFMra3NvclVOVmtMTFFXT2VObGdWNXJMRm9aeXN2RGpDZ0E2K2ZLbzVLYmVIS0Y5ZVNXbmVnNzZ3ZVppTCs4MGFkczlMaFpNeHpaNklYUHJPcExieGdXbFNSd1oycDJub08vV21JNm1tL0J6UENHY2lSN2M1d2giLCJtYWMiOiIzNjYzNzRlYmE3ZGQ4NjMyYTA0OTg0M2E1YjAwOGQ3MzZhZGIyODY4Y2JmMjliNjI1YWRjOWQ4MzViNGE1MWE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://orexcoin.test/admin/cron-jobs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">orexcoin.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2118332020\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-998297671 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>acceptCookies</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BH5IPda4hzHNSzJjHWxHu92ZkCc7CfFowEonEkF</span>\"\n  \"<span class=sf-dump-key>orexcoin_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">oGjwXvI2S05jRt9urdB3N1br7wpMijx1NNUSl9lh</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998297671\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2075857692 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 03 Jun 2025 04:40:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://orexcoin.test/admin/cron-jobs</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2075857692\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1290690540 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">0BH5IPda4hzHNSzJjHWxHu92ZkCc7CfFowEonEkF</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">notify</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://orexcoin.test/user/mining/history</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>theme_mode</span>\" => \"<span class=sf-dump-str title=\"4 characters\">dark</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>notify</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    \"<span class=sf-dump-key>message</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Cron running successfully!</span>\"\n    \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Success</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1290690540\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://orexcoin.test/site-cron?run_action=3", "action_name": "cron.job", "controller_action": "App\\Http\\Controllers\\CronJobController@runCronJobs"}, "badge": "302 Found"}}