@use '../../utils' as *;

/*----------------------------------------*/
/* Recent post styles
/*----------------------------------------*/
.rc-post {
    padding: 12px;
    gap: 16px;
    margin-bottom: 16px;
    border: 1px solid rgba($heading, $alpha: 0.16);
    @include border-radius(12px);

    @include dark-theme {
        border-color: rgba($white, $alpha: 0.16);
    }

    @media #{$xxs} {
        gap: 12px;
    }

    &:hover {
        & .rc-post-thumb {
            img {
                @include transform(scale(1.1));
            }
        }
    }

    &:last-child {
        margin-bottom: 0;
    }
}

.rc-post-title {
    font-size: 16px;
    overflow: hidden;
    -webkit-line-clamp: 2;
    display: box;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    white-space: normal;
    color: var(--td-heading);
    font-weight: 700;
}

.rc-post-thumb-clip {
    position: relative;
    padding: 1px;
    overflow: hidden;
    flex: 0 0 auto;

    &::before,
    &::after {
        position: absolute;
        inset-inline-start: 0;
        top: 0;
        transition: all .3s;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(71, 118, 230, 1) 0%, rgba(142, 84, 233, 1) 100%);
        opacity: 0.6;
        content: "";
        clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
        border-radius: 2px;
    }
}

.rc-post-thumb {
    position: relative;
    z-index: 3;
    background: #04060a;
    clip-path: polygon(99.543% 0%, 0.457% 0%, 0.457% 0%, 0.383% 0.009%, 0.312% 0.035%, 0.247% 0.076%, 0.187% 0.131%, 0.134% 0.199%, 0.088% 0.279%, 0.051% 0.368%, 0.023% 0.465%, 0.006% 0.57%, 0% 0.68%, 0% 99.32%, 0% 99.32%, 0.006% 99.43%, 0.023% 99.535%, 0.051% 99.632%, 0.088% 99.722%, 0.134% 99.801%, 0.187% 99.869%, 0.247% 99.924%, 0.312% 99.965%, 0.383% 99.991%, 0.457% 100%, 87.956% 100%, 87.956% 100%, 87.989% 99.998%, 88.022% 99.993%, 88.054% 99.984%, 88.086% 99.972%, 88.117% 99.957%, 88.147% 99.938%, 88.176% 99.916%, 88.204% 99.891%, 88.231% 99.863%, 88.257% 99.832%, 99.844% 84.744%, 99.844% 84.744%, 99.872% 84.703%, 99.898% 84.66%, 99.921% 84.613%, 99.942% 84.564%, 99.959% 84.513%, 99.974% 84.46%, 99.985% 84.404%, 99.993% 84.348%, 99.998% 84.29%, 100% 84.232%, 100% 0.68%, 100% 0.68%, 99.994% 0.57%, 99.977% 0.465%, 99.949% 0.368%, 99.912% 0.279%, 99.866% 0.199%, 99.813% 0.131%, 99.753% 0.076%, 99.688% 0.035%, 99.617% 0.009%, 99.543% 0%);
    overflow: hidden;
    flex: 0 0 auto;
    @include border-radius(1px);

    & img {
        width: 116px;
        height: 80px;
        object-fit: cover;

    }
}

.rc-meta {
    margin-top: 3px;

    & span {
        font-size: 14px;

        & svg,
        & i {
            margin-inline-end: 6px;
        }

        & svg {
            @include transform(translateY(-2px));
        }

        &:hover {
            & a {
                color: var(--td-primary);
            }
        }
    }
}