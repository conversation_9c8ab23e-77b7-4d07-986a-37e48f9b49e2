@extends('frontend::layouts.user')

@section('title')
  {{ __('Mining') }}
@endsection
@push('style')
  <style>
    .gateway-data-wrapper {
    display: none
    }
  </style>
@endpush
@section('content')
  <div class="col-xxl-12">
    <div class="page-title-wrapper mb-16">
    <div class="page-title-contents">
      <h3 class="page-title">{{ __('Mining') }}</h3>
    </div>
    </div>
    @include('frontend::user.mining.include.navigation')
  </div>
  <div class="col-xxl-12">
    <div class="row gy-30 justify-content-center" data-select2-id="12">
    <div class="col-lg-9" data-select2-id="11">
      <div class="buy-mining-plan-area default-area-style" data-select2-id="10">
      <div class="heading-top">
        <h5 class="title">{{ __('Checkout') }}</h5>
      </div>
      <div class="buy-mining-plan-contents">
        <div class="buy-mining-plan-info">
        <div class="icon">
          <img src="{{ asset($scheme->icon) }}" alt="Plan Icon">
        </div>
        <div class="contents">
          <h4 class="title">{{ $scheme->name }}</h4>
          <p class="description">{{ $scheme->description }}</p>
        </div>
        </div>
      </div>
      <div class="buy-mining-plan-form" data-select2-id="9">
        <div class="row gy-24" data-select2-id="7">
        <div class="col-lg-12" data-select2-id="6">
          <div class="td-form-group input-fill" data-select2-id="5">
          <label class="input-label">{{ __('Plan Name') }}</label>
          <div class="input-field">
            <input type="text" disabled id="title" class="form-control" value="{{ $scheme->name }}"
            placeholder="{{ $scheme->name }}" required="">
          </div>
          </div>
        </div>
        <div class="col-lg-12">
          <div class="td-form-group input-fill">
          <label class="input-label" for="price">{{ __('Price') }} </label>
          <div class="input-field">
            <input disabled value="{{ formatAmount($scheme->price, $currencySymbol, true) }}"
            placeholder="{{ formatAmount($scheme->price, $currencySymbol, true) }}" type="text" id="price"
            class="form-control" placeholder="{{ formatAmount($scheme->price, $currencySymbol, true) }}"
            required="">
          </div>
          </div>
        </div>
        @if ($priceData['discountAmount'] > 0)
      <div class="col-lg-12">
        <div class="td-form-group input-fill">
        <label class="input-label" for="discount">{{ __('Discount') }} </label>
        <div class="input-field">
        <input disabled type="text"
        value="{{ formatAmount($priceData['discountAmount'], $currencySymbol, true) }}" id="discount"
        class="form-control"
        placeholder="{{ formatAmount($priceData['discountAmount'], $currencySymbol, true) }}" required="">
        </div>
        </div>
      </div>
      @endif
        <div class="col-lg-12">
          <div class="td-form-group input-fill">
          <label class="input-label" for="discount">{{ __('Total') }} </label>
          <div class="input-field">
            <input disabled type="text"
            value="{{ formatAmount($priceData['planFinalPrice'], $currencySymbol, true) }}" id="discount"
            class="form-control"
            placeholder="{{ formatAmount($priceData['planFinalPrice'], $currencySymbol, true) }}" required="">
          </div>
          </div>
        </div>
        <div class="col-xxl-12">
          <form method="post" enctype="multipart/form-data" action="{{ route('user.plan-purchase.pay') }}">
            @csrf
            <div class="col-lg-12">
              <div class="td-form-group input-fill">
              <label class="input-label" for="discount">{{ __('Payment Method') }} </label>
              <div class="input-field">
                <select name="payment_method" id="payment_method" class="defaultselect2 ">
                <option value="" disabled selected>{{ __('Select Method') }}</option>
                <option data-coin="default" value="default">
                {{ __('Default Currency (:balance :currency)', ['currency' => setting('site_currency', 'global'), 'balance' => formatAmount($user->balance, $currencyDecimals)]) }}

                @foreach ($user->wallets as $wallet)
            <option data-coin="{{ $wallet->coin->id }}" value="{{ $wallet->id }}">
              {{ $wallet->coin->name . " ($wallet->balance {$wallet->coin->code})" }}
            </option>
            @endforeach
                <option data-coin="gateway" value="gateway">{{ __('Gateway') }}</option>
                </select>
              </div>
              </div>
            </div>
          <input type="hidden" name="scheme_id" value="{{ $scheme->id }}">
          <div class="checkout-payment-info">


            <!-- Wallet Selection -->
            <div class="gateway-data-wrapper mt-3">
            <div class="td-form-group input-fill w-100">
              <label class="input-label" for="user_wallet">{{ __('Currency') }} <span>*</span></label>
              <div class="input-field">
              <select name="user_wallet" id="user_wallet" class="defaultselect2 "
                onchange="userWalletInfo(this)">
                <option value="" disabled selected>{{ __('Select Wallet') }}</option>
                </option>
                <option value="default" data-user-wallet-name="{{ __('Main Wallet') }}">
                {{ setting('site_currency', 'global') }}
                </option>
                @foreach ($wallets as $wallet)
          <option value="{{ $wallet->id }}" data-user-wallet-name="{{ $wallet?->coin?->name }}">
          {{ $wallet?->coin?->name }} ({{ $wallet?->coin?->code }})
          </option>
          @endforeach
              </select>
              </div>
              <p class="feedback-invalid wallet-required"></p>
              @error('user_wallet')
          <span class="text-danger">{{ $message }}</span>
        @enderror

            </div>
            </div>
            <!-- Payment Gateway -->
            <div class="gateway-data-wrapper mt-4">
            <div class="td-form-group input-fill w-100">
              <label class="input-label">{{ __('Gateway') }} <span>*</span></label>
              <div class="input-field">
              <select name="gateway_code" id="gateway_code" class="defaultselect2 ">
                <option value="" disabled selected>{{ __('Select Gateway') }}</option>
              </select>
              </div>
              <p class="feedback-invalid gateway-required"></p>
              <p class="input-attention danger-text gateway-charge mt-1"></p>
              @error('gateway_code')
          <span class="text-danger">{{ $message }}</span>
        @enderror
            </div>
            </div>
            <!-- Payment Gateway -->
            <div class="mt-3">
            <div class="row w-100">
              <div class="td-form-group input-fill col-xxl-4">
              <label class="input-label" for="charge_amount">{{ __('Charge') }} </label>
              <div class="input-field">
                <input disabled type="text" value="{{ formatAmount(0, $currencySymbol, true) }}"
                id="charge_amount" class="form-control"
                placeholder="{{ formatAmount(0, $currencySymbol, true) }}" required="">
              </div>
              </div>
              <div class="td-form-group input-fill col-xxl-4">
              <label class="input-label" for="final_amount">{{ __('Final Amount') }} </label>
              <div class="input-field">
                <input disabled type="text"
                value="{{ formatAmount($priceData['planFinalPrice'], $currencySymbol, true) }}"
                id="final_amount" class="form-control"
                placeholder="{{ formatAmount($priceData['planFinalPrice'], $currencySymbol, true) }}"
                required="">
              </div>
              </div>
              <div class="td-form-group input-fill col-xxl-4">
              <label class="input-label" for="discount">{{ __('Payable Amount') }} </label>
              <div class="input-field">
                <input disabled type="text"
                value="{{ formatAmount($priceData['planFinalPrice'], $currencySymbol, true) }}"
                id="payable_amount" class="form-control"
                placeholder="{{ formatAmount($priceData['planFinalPrice'], $currencySymbol, true) }}"
                required="">
              </div>
              </div>
            </div>
            </div>
            <!-- Review Data Placeholder -->
            <div class="checkout-payment-info-item col-12">
            <div class="show-review-data w-100"></div>
            </div>
          </div>

          <div class="col-xl-12 mt-2">
            <div class="td-form-btns d-flex flex-wrap">
            <a href="{{ route('user.mining.') }}" class="td-btn grd-outline-fill-btn primary-btn btn-m-w">
              <div class="inner-btn">
              <span class="btn-text">{{ __('Back') }}</span>
              </div>
            </a>
            <button type="submit"
              class="td-btn btn-chip grd-fill-btn-primary btn-m-w">{{ __('Buy Now') }}</button>
            </div>
          </div>
          </form>
        </div>
        </div>
      </div>
      </div>
    </div>
    </div>
  </div>
@endsection
@push('js')
    <script>
    "use strict";
    const CONFIG = {
      isUserWalletTopUp: "{{ request('user_wallet') }}",
      routes: {
      gatewayMethod: "{{ route('user.userWallet.gatewayMethod') }}"
      },
      csrf: "{{ csrf_token() }}"
    };

    const elements = {
      userWalletSelect: $('select[name="user_wallet"]'),
      paymentGatewaySelect: $('select[name="gateway_code"]'),
      amountInput: $('input[name="amount"]')

    };

    function validateForm() {
      const gateway = elements.paymentGatewaySelect.val();

      if (!gateway) {
      return showError('.gateway-required', '{{ __('Payment gateway is required') }}');
      }

      return true;
    }

    function showError(element, message) {
      $(element).show().html(message);
    }

    function clearError(element) {
      $(element).hide().html('');
    }

    function handleNextStep(e) {
      e.preventDefault();
      if (!$(e.currentTarget).closest('.step-one').length) return;

      if (validateForm()) {
      $('.active-step-two, .active-line-one').addClass('active');
      $('.show-check-mark-one').html(
        '<svg width="18" height="17" viewBox="0 0 18 17" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.23283 12.8517L3.36475 9.00375L4.33177 8.04177L7.23283 10.9277L13.4591 4.73389L14.4261 5.69587L7.23283 12.8517Z" fill="white"/></svg>'
      );
      }
    }

    async function getGateways() {
      try {
      clearError('.gateway-required');
      const response = await $.ajax({
        type: "POST",
        url: CONFIG.routes.gatewayMethod,
        data: {
        wallet_currency: elements.userWalletSelect.val(),
        gateway_type: 'add_money',
        _token: CONFIG.csrf
        }
      });

      if (response.options) {
        elements.paymentGatewaySelect.html(response.options)

        $('.amount-text, .charge-text, .total-text, .payTotal-text').html('');
        $('.amount-required').html('');
        $('.show-review-data').html('');
      }
      } catch (error) {
      console.log(error);
      showError('.gateway-required', '{{ __('Failed to load payment gateways') }}');
      }
    }



    function updateGatewayInfo() {
      $('.amount-required').text('');
      $('.show-review-data').html('');
      $('.gateway-required').text('');

      const selectedOption = elements.paymentGatewaySelect.find(':selected');


      if (!selectedOption.length || selectedOption.is(':disabled')) {
      showError('.gateway-required', '{{ __('Please select a valid payment gateway') }}');
      return;
      }


      const data = {
      minAmount: selectedOption.data('minimum-amount'),
      maxAmount: selectedOption.data('maximum-amount'),
      routeUrl: selectedOption.data('routeurl'),
      gatewayRate: selectedOption.data('gateway-rate'),
      paymentMethod: selectedOption.data('payment-method'),
      gatewayCurrency: selectedOption.data('gateway-currency')
      };

      var chargeFormatted = selectedOption.data('gateway-charge-type') == 'percentage' ? formatAmount(selectedOption.data('gateway-charge'), 2) : selectedOption.data('gateway-charge');
      showError('.amount-required', `{{ __('Minimum') }} ${data.minAmount} ${data.gatewayCurrency} {{ __('and Maximum') }} ${data.maxAmount} ${data.gatewayCurrency}`);
      $(".conversionRate").text(data.gatewayRate);
      $(".gateway-charge").text("{{ __('Charge') }}: " + chargeFormatted +
      (selectedOption
        .data('gateway-charge-type') == 'percentage' ? '%' : (' ' + data.gatewayCurrency)));


      $.get(`${data.routeUrl}?amount={{ $priceData['planFinalPrice'] }}`)
      .done(response => {
        $(".show-review-data").html(response.html);
        $('#charge_amount').val(chargeFormatted + ' ' + data.gatewayCurrency);
        $('#payable_amount').val(data.gatewayCurrency == '{{ $currency }}' ? formatAmount(response.payAmount, "{{ $currencyDecimals }}") : formatAmount(response.payAmount,8) + ' ' + data.gatewayCurrency);
        $('#final_amount').val(formatAmount(response.finalAmount, 2) + ' {{ $currency }}');
      })
      .fail(() => {
        showError('.gateway-required', '{{ __('Failed to load gateway details') }}');
      });
    }


    elements.userWalletSelect.on('change', getGateways);

    $(document).ready(() => {
      $('.error-message').html('');


      elements.paymentGatewaySelect.on('change', updateGatewayInfo);


      window.userWalletInfo = element => {
      if (element && element.value) {
        const walletName = $(element).find(':selected').data('user-wallet-name');
        $('.user-wallet-text').html(walletName || '');
      }
      };
    });

    function formatAmount(amount, decimals) {
      const factor = Math.pow(10, decimals);
      const rounded =
      Math.round((Number(amount) + Number.EPSILON) * factor) / factor;

      return rounded.toFixed(decimals);
    }

    $(document).on('change', '#payment_method', function () {
      var method = $(this).val();
      if (method == 'gateway') {
        $('.gateway-data-wrapper').show();
      } else {
        $('.gateway-data-wrapper').hide();

        if(method != 'default'){
          $.get("{{ route('user.plan-purchase.price.convert', ':coin') }}?amount={{ $priceData['planFinalPrice'] }}".replace(':coin', $(this).find(':selected').data('coin')))
          .done(response => {
            $('#payable_amount').val(formatAmount(response.finalAmount, 8) + ` ${response.currency}`);
          })
          .fail(() => {
            showError('.gateway-required', '{{ __('Failed to load gateway details') }}');
          });
        }else{
          $('#payable_amount,#final_amount').val('{{ formatAmount($priceData['planFinalPrice'], $currencySymbol, true) }}');
        }

      }
    })
    </script>
@endpush