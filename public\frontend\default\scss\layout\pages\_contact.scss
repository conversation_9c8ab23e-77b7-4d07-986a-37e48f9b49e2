@use '../../utils' as *;

/*----------------------------------------*/
/* Contact styles
/*----------------------------------------*/

// Contact form style
.contact-form-left {
    padding-inline-end: 60px;

    @media #{$xl} {
        padding-inline-end: 30px;
    }

    @media #{$xs,$sm,$md,$lg} {
        padding-inline-end: 0;
    }
}

.contact-foem-title {
    font-size: 25px;
    text-align: center;
    margin-bottom: 20px;
}

.contact-form-box {
    position: relative;
    margin-inline-start: 50px;

    @media #{$xl} {
        margin-inline-start: 30px;

    }

    @media #{$xs,$sm,$md,$lg} {
        margin-inline-start: 0;
    }

    .clip-path {
        position: relative;
        padding: 1px;
        height: 100%;
    }

    .clip-path-inner {
        position: relative;
        z-index: 3;
        padding: 40px 40px;
        height: 100%;

        &::before {
            position: absolute;
            content: "";
            width: 100%;
            height: 100%;
            top: 0;
            inset-inline-start: 0;
            clip-path: polygon(0% 1.349%, 0% 98.651%, 0% 98.651%, 0.016% 98.87%, 0.063% 99.077%, 0.139% 99.271%, 0.24% 99.448%, 0.364% 99.605%, 0.509% 99.74%, 0.671% 99.849%, 0.85% 99.931%, 1.041% 99.982%, 1.242% 100%, 93.955% 100%, 93.955% 100%, 94.044% 99.997%, 94.132% 99.986%, 94.219% 99.969%, 94.304% 99.946%, 94.387% 99.916%, 94.469% 99.879%, 94.547% 99.837%, 94.623% 99.788%, 94.696% 99.734%, 94.766% 99.673%, 99.568% 95.181%, 99.568% 95.181%, 99.647% 95.101%, 99.719% 95.014%, 99.782% 94.921%, 99.839% 94.823%, 99.887% 94.721%, 99.927% 94.614%, 99.959% 94.504%, 99.981% 94.391%, 99.995% 94.276%, 100% 94.159%, 100% 1.349%, 100% 1.349%, 99.984% 1.13%, 99.937% 0.923%, 99.861% 0.729%, 99.76% 0.552%, 99.636% 0.395%, 99.491% 0.26%, 99.329% 0.151%, 99.15% 0.069%, 98.959% 0.018%, 98.758% 0%, 50% 0%, 1.242% 0%, 1.242% 0%, 1.041% 0.018%, 0.85% 0.069%, 0.671% 0.151%, 0.509% 0.26%, 0.364% 0.395%, 0.24% 0.552%, 0.139% 0.729%, 0.063% 0.923%, 0.016% 1.13%, 0% 1.349%);
            background: linear-gradient(0deg, rgba(255, 255, 255, 0.1) -64.03%, rgba(9, 70, 255, 0.06) 86.17%);
            z-index: -1;
            border-radius: 16px;
        }

    }

    .bg-shape {
        position: absolute;
        content: "";
        height: 100%;
        width: 100%;
        top: 0;
        inset-inline-start: 0;
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
}

// Contact info style
.td-contact-info-section {
    background-color: #E6EFFC;

    @include dark-theme {
        background-color: #0C142B;
    }
}

.contact-info-item {
    position: relative;
    text-align: center;

    .icon {
        margin-bottom: 25px;
        width: 100px;
        height: 100px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 1px solid rgba($heading, $alpha: 0.1);

        @include dark-theme {
            border-color: rgba($white, $alpha: 0.1);
        }

        img {
            width: 50px;
        }
    }

    .contents {
        .title {
            font-size: 24px;
        }

        .info {
            margin-top: 12px;

            a {
                color: #484848;
                font-family: var(--td-ff-heading);
                background-image: linear-gradient(87.17deg, #4776E6 0%, #8E54E9 100%);
                -webkit-background-clip: text;
                background-clip: text;

                @include dark-theme {
                    color: rgba($white, $alpha: 0.7);
                }

                &:hover {
                    color: var(--td-primary);
                    -webkit-text-fill-color: transparent;
                }
            }
        }
    }
}

.contact-info-main {
    .row {
        & [class*="col-"] {
            &:not(:last-child) {
                .contact-info-item {
                    &::after {
                        position: absolute;
                        content: "";
                        height: 100%;
                        width: 1px;
                        background-color: rgba($heading, $alpha: 0.1);
                        inset-inline-end: -15%;
                        top: 0;

                        @include dark-theme {
                            background-color: rgba($white, $alpha: 0.1);
                        }

                        @media #{$xs,$sm,$md} {
                            display: none;
                        }
                    }
                }
            }
        }
    }
}