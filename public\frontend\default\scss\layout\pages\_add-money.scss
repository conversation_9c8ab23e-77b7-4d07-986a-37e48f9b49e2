@use '../../utils' as *;

/*----------------------------------------*/
/* Add Money styles
/*----------------------------------------*/
.default-area-style {
    background: var(--td-white);
    border-radius: 16px;
    position: relative;
    z-index: 1;
    border: 1px solid rgba($heading, $alpha: 0.16);

    @include dark-theme {
        background-color: #0C1633;

        &::before {
            position: absolute;
            content: "";
            inset: 0;
            padding: 1px;
            background: linear-gradient(180deg, rgba(11, 39, 122, 1) 0%, rgba(0, 148, 255, 1) 100%);
            -webkit-mask: linear-gradient(var(--td-white) 0 0) content-box, linear-gradient(var(--td-white) 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            border-radius: 16px;
            z-index: -1;
        }
    }

    .heading-top {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 30px;
        height: 60px;
        background: #F6F6F6;
        border-radius: 16px 16px 0px 0px;
        margin: 0px 1px;

        @include dark-theme {
            background: #16213F;
        }

        .title {
            font-size: 20px;
            font-weight: 700;
            font-family: var(--td-ff-body);

            @include dark-theme {
                color: var(--td-heading);
            }
        }
    }

    .default-content-inner {
        padding: 30px 30px 30px;

        @media #{$xs} {
            padding: 24px 20px 24px;
        }
    }
}