<?php if(request('tab') == 'transactions'): ?>
    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
        'tab-pane fade',
        'show active' => request('tab') == 'transactions',
    ]); ?>" id="pills-transactions" role="tabpanel" aria-labelledby="pills-transactions-tab">
        <div class="row">
            <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12">
                <div class="site-card">
                    <div class="site-card-header">
                        <h4 class="title"><?php echo e(__('Transactions')); ?></h4>
                    </div>
                    <div class="site-card-body table-responsive">
                        <div class="site-table">
                            <div class="table-filter">
                                <form action="" method="get">
                                    <input type="hidden" name="tab" value="transactions">
                                    <div class="filter d-flex">
                                        <div class="search">
                                            <label for=""><?php echo e(__('Search:')); ?></label>
                                            <input type="text" name="query" value="<?php echo e(request('query')); ?>" />
                                        </div>
                                        <select name="type" id="type" class="form-select form-select-sm">
                                            <option value="" selected><?php echo e(__('Filter By Type')); ?></option>
                                            <?php $__currentLoopData = App\Enums\TxnType::cases(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($type->value); ?>" <?php if(request('type') == $type->value): echo 'selected'; endif; ?>>
                                                    <?php echo e($type->value); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                        <button class="apply-btn" type="submit"><i
                                                data-lucide="search"></i><?php echo e(__('Search')); ?></button>
                                    </div>
                                </form>
                            </div>
                            <table class="table">
                                <thead>
                                    <tr>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Date'),
                                            'field' => 'created_at',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Transaction ID'),
                                            'field' => 'tnx',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Type'),
                                            'field' => 'type',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Wallet'),
                                            'field' => 'wallet_type',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Amount'),
                                            'field' => 'amount',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Charge'),
                                            'field' => 'charge',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Total Amount'),
                                            'field' => 'final_amount',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Gateway'),
                                            'field' => 'method',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                        <?php echo $__env->make('backend.filter.th', [
                                            'label' => __('Status'),
                                            'field' => 'status',
                                        ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <tr>
                                            <td><?php echo e($transaction->created_at); ?></td>
                                            <td><?php echo e($transaction->tnx); ?></td>
                                            <td>
                                                <?php echo $__env->make('backend.transaction.include.__txn_type', [
                                                    'txnType' => $transaction->type->value,
                                                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                            </td>
                                            <td>
                                                <?php echo e($transaction->wallet_type === 'default' ? __('Main Wallet') : $transaction->currency?->name); ?>

                                            </td>
                                            <td>
                                                <?php echo e(formatAmount($transaction->amount, $transaction->currency, true)); ?>

                                            </td>
                                            <td><?php echo e(formatAmount($transaction->charge, $transaction->currency, true)); ?>

                                            </td>
                                            <td><?php echo e(formatAmount($transaction->final_amount, $transaction->currency, true)); ?>

                                            </td>
                                            <td><?php echo e($transaction->method); ?></td>
                                            <td>
                                                <?php echo $__env->make('backend.transaction.include.__txn_status', [
                                                    'status' => $transaction->status->value,
                                                ], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <td colspan="7" class="text-center"><?php echo e(__('No Data Found!')); ?></td>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                            <?php echo e($transactions->links('backend.include.__pagination')); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH E:\laragon\www\orexcoin\resources\views/backend/user/include/__transactions.blade.php ENDPATH**/ ?>