@use '../utils' as *;

/*----------------------------------------*/
/* Badge styles
/*----------------------------------------*/
// default badge
.td-badge {
	@include inline-flex();
	align-items: center;
	justify-content: center;
	padding: 7px 15px;
	line-height: 1;
	font-size: 14px;
	background: rgba(94, 167, 253, 0.2);
	@include border-radius(28px);
	column-gap: 6px;
	min-width: 60px;

	&.badge-success {
		color: #fff;
		background: #03A66D;
	}

	&.badge-warning {
		color: var(--td-white);
		background-color: #F7931A;
	}

	&.badge-danger {
		background: #e94e5b;
		color: #fff;
	}

	&.badge-green {
		background-color: rgba($green, $alpha: 1);
		color: var(--td-green);
	}

	&.badge-transparent-primary {
		background-color: rgba($primary, $alpha: 1);
		color: var(--td-white);
	}

	&.badge-transparent-danger {
		background-color: rgba($danger, $alpha: 1);
		color: var(--td-danger);
	}

	&.badge-transparent-green {
		background-color: rgba($green, $alpha: 1);
		color: var(--td-green);
	}

	&.badge-transparent-warning {
		background-color: rgba($warning, $alpha: 1);
		color: var(--td-warning);
	}

	// badge outline
	&.badge-outline-primary {
		background-color: rgba($primary, $alpha: 1);
		color: var(--td-primary);
		border: 1px solid rgba($primary, $alpha: 1);

		@include dark-theme {
			background-color: rgba($primary, $alpha: 0.16);
			color: var(--td-white);
		}
	}

	&.badge-outline-danger {
		background-color: rgba($danger, $alpha: 0.16);
		color: var(--td-white);
		border: 1px solid rgba($danger, $alpha: 1);

		@include dark-theme {
			background-color: rgba($danger, $alpha: 0.16);

			color: var(--td-danger);
		}
	}

	&.badge-outline-warning {
		background-color: rgba($warning, $alpha: 1);
		color: var(--td-white);
		border: 1px solid rgba($warning, $alpha: 1);

		@include dark-theme {
			background-color: rgba($warning, $alpha: 0.16);
			color: var(--td-warning);
		}
	}

	&.badge-outline-success {
		background-color: rgba($success, $alpha: 1);
		color: var(--td-white);
		border: 1px solid rgba($success, $alpha: 1);

		@include dark-theme {
			background-color: rgba($success, $alpha: 0.16);
			color: var(--td-white);
		}
	}

	// fill badges
	&.fill-badge-success {
		background-color: var(--td-green);
		color: var(--td-white);
	}

	&.fill-badge-warning {
		background: var(--td-warning);
		color: var(--td-white);
	}

	&.fill-badge-danger {
		background: var(--td-danger);
		color: var(--td-white);
	}

	&.fill-badge-green {
		background-color: var(--td-green);
		color: var(--td-white);
		;
	}

	&.fill-badge-transparent-primary {
		background-color: var(--td-primary);
		color: var(--td-white);
	}
}

// gradient badge
.gradient-badge {
	position: relative;
	padding: 7px 14px;
	background: linear-gradient(90deg, #C340C0 0%, #FB405A 50%, #F7A34A 100%);
	background-clip: text;
	-webkit-background-clip: text;
	-webkit-text-fill-color: transparent;
	z-index: 1;
	font-size: 14px;
	font-weight: 700;
	display: inline-block;

	&::after {
		position: absolute;
		content: "";
		background: linear-gradient(90deg, rgba(195, 64, 192, 0.14) 0%, rgba(251, 64, 90, 0.14) 50%, rgba(247, 163, 74, 0.14) 100%);
		width: 100%;
		height: 100%;
		top: 0;
		inset-inline-start: 0;
		z-index: -1;
		border-radius: 30px;
	}
}

// Outline badge
.td-outline-badge {
	border-radius: 36px;
	padding: 5px 16px;
	font-size: 12px;
	font-weight: 500;

	&.badge-success {
		border: 1px solid #4CAF50;
		background: rgba(76, 175, 80, 0.16);
		color: #4CAF50;
	}

	&.badge-warning {
		border: 1px solid #F7931A;
		background: rgba(247, 147, 26, 0.16);
		color: #F7931A;
	}
}

// clip badge
.clip-badge {
	position: relative;
	z-index: 3;
	background: #282138;
	clip-path: polygon(0 0, 100% 0, 100% calc(100% - 12px), calc(100% - 12px) 100%, 0 100%);
	border-radius: 2px;
	width: 100%;
	position: relative;
	display: inline-block;
	padding: 1px;
	background: transparent;

	&::before {
		position: absolute;
		inset-inline-start: 0;
		top: 0;
		transition: all 0.3s;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, rgb(71, 118, 230) 0%, rgb(142, 84, 233) 100%);
		;
		content: "";
		clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
		border-radius: 2px;
		z-index: -1;
	}

	.inner-badge {
		background: #f4eefd;
		font-size: 14px;
		font-weight: 700;
		display: inline-flex;
		position: relative;
		z-index: 3;
		clip-path: polygon(0 0, 100% 0, 100% calc(100% - 14px), calc(100% - 18px) 100%, 0 100%);
		gap: 8px;
		padding: 0 20px;
		height: 42px;
		align-items: center;
		justify-content: center;

		@include dark-theme {
			background: #392467;
		}

		&:before {
			position: absolute;
			top: 0;
			inset-inline-start: 0;
			content: "";
			background: linear-gradient(90deg, rgba(71, 118, 230, 0.2) -1.59%, rgba(142, 84, 233, 0.2) 42.06%);
			z-index: -1;
			width: 100%;
			height: 100%;
		}
	}

	.icon {
		width: 18px;
		display: inline-flex;
		align-items: center;
	}
}